# 任务摘要：`pg_hsm_step2_bi_report_price`表实体类及数据库操作生成

## 问题

需要为数据库表`pg_hsm_step2_bi_report_price`生成实体类及相关数据库操作代码。该表与现有的`pg_hsm_step2_bi_report_dcp`表结构完全相同，仅表名不同。

## 解决方案

基于`pg_hsm_step2_bi_report_dcp`表的相关代码，创建了以下文件：

1. 实体类：`PgHsmStep2BiReportPrice.java`
   - 继承自`BaseEntity`
   - 包含与`PgHsmStep2BiReportDcp`相同的字段和属性
   - 所有字段都有相应的getter和setter方法

2. Mapper接口：`PgHsmStep2BiReportPriceMapper.java`
   - 继承自`BaseMapper<PgHsmStep2BiReportPrice>`
   - 添加了`@DS("bi-task")`数据源注解

3. Service接口：`IPgHsmStep2BiReportPriceService.java`
   - 继承自`IService<PgHsmStep2BiReportPrice>`

4. Service实现类：`PgHsmStep2BiReportPriceServiceImpl.java`
   - 继承自`ServiceImpl<PgHsmStep2BiReportPriceMapper, PgHsmStep2BiReportPrice>`
   - 实现了`IPgHsmStep2BiReportPriceService`接口
   - 添加了`@Service`注解

5. Controller类：`PgHsmStep2BiReportPriceController.java`
   - 继承自`BaseController`
   - 添加了`@RestController`和`@RequestMapping("/pg-hsm-step2-bi-report-price")`注解

6. Mapper XML配置文件：`PgHsmStep2BiReportPriceMapper.xml`
   - 设置命名空间为`com.lenztech.bi.enterprise.mapper.PgHsmStep2BiReportPriceMapper`

## 注意事项

1. 所有文件结构与对应的`PgHsmStep2BiReportDcp`相关文件保持一致
2. 只替换了类名和相关引用，保持其他内容完全相同
3. 表字段定义、注解和注释均保持一致 