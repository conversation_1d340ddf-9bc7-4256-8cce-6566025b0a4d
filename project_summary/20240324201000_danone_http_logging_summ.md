# 丹能服务HTTP请求日志增强总结

## 问题描述

在DanoneService中，虽然有记录HTTP请求的基本信息，但缺少详细的请求头和完整请求体的日志记录。这使得在排查API调用问题时难以获取完整的请求信息。

## 解决方案

增强了HTTP请求的日志记录，添加了以下详细信息：

1. **请求头信息**：记录所有HTTP请求头
   - 使用`req.getAllHeaders()`获取所有请求头
   - 格式化输出每个请求头的名称和值

2. **请求体信息**：完整记录HTTP请求体
   - 将整个JSON请求体以易读格式记录到日志

3. **响应头信息**：记录所有HTTP响应头
   - 使用`response.getAllHeaders()`获取所有响应头
   - 格式化输出响应头详情

4. **响应内容**：完整记录HTTP响应内容
   - 将整个响应内容记录到日志中

## 代码实现

```java
// 打印请求头信息
StringBuilder headersLog = new StringBuilder("请求头信息:\n");
Arrays.stream(req.getAllHeaders()).forEach(header -> 
    headersLog.append(String.format("  %s: %s\n", header.getName(), header.getValue()))
);
log.info(headersLog.toString());

// 打印请求体信息
log.info("请求体内容:\n{}", requestBody);

// 执行请求
response = client.execute(req);

// 打印响应头信息
StringBuilder responseHeadersLog = new StringBuilder("响应头信息:\n");
Arrays.stream(response.getAllHeaders()).forEach(header -> 
    responseHeadersLog.append(String.format("  %s: %s\n", header.getName(), header.getValue()))
);
log.info(responseHeadersLog.toString());

log.info("HTTP状态码: {}", statusCode);
log.info("响应内容:\n{}", result);
```

## 日志输出格式

增强后的日志输出格式如下：

```
请求头信息:
  Content-Type: application/json;charset=utf-8
  Nonce: a2f5c9d7e1b3a6f8
  CurTime: 1679647582
  CheckSum: a1b2c3d4e5f6g7h8i9j0...

请求体内容:
[{"requestId":"550e8400-e29b-41d4-a716-446655440000","type":1,"inspectAddress":"..."}]

发送请求 - URL: https://danone-compass-nacos-test.winsfa.com/api/interfaces/mdmSfaApi/saveInspectData

响应头信息:
  Content-Type: application/json;charset=UTF-8
  Date: Sun, 24 Mar 2024 20:05:32 GMT
  Connection: keep-alive
  Content-Length: 85

HTTP状态码: 200
响应内容:
{"code":200,"message":"数据处理成功","data":null}
```

## 改进效益

1. **问题排查更便捷**：能看到完整的请求和响应信息，方便排查API调用问题
2. **减少调试时间**：不需要额外工具抓包就能查看完整请求
3. **增强可观测性**：全方位记录HTTP通信过程，提高系统可观测性
4. **便于第三方API对接**：在与第三方API对接时，可以清晰看到请求格式是否符合预期

## 后续优化建议

1. 考虑将敏感信息（如认证信息）在日志中进行脱敏处理
2. 在生产环境中可调整日志级别，避免产生过多日志
3. 考虑添加请求ID，关联同一次请求的所有日志记录 