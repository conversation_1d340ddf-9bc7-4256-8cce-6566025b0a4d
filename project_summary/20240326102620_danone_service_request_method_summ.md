# 丹能数据推送服务请求方法优化

## 问题概述

方法`sendRequestAndUpdateLog`中，之前存在两种请求后端的方案：
1. 使用SDK方案（通过`HttpsApiClientenv_uat.getInstance().compass_saveinspectdataSyncMode`）
2. 使用自定义HTTP客户端方案（通过`sendRequest`方法）

虽然两种方案都会执行请求，但只使用第二种方案（自定义HTTP客户端）的响应来更新日志记录。但实际上，SDK方案才是最新的方案，需要完全切换到SDK方案，使用SDK方案的响应状态报文。

## 修改内容

1. 修改了`sendRequestAndUpdateLog`方法，完全使用SDK方案（ApiResponse）发送请求及获取响应
2. 删除了不再需要的`sendRequest`方法及其相关代码
3. 删除了`ResponseResult`内部类，不再需要
4. 删除了不再使用的HTTP客户端相关的导入和常量
5. 保留了必要的业务逻辑，例如解析响应JSON并根据code字段判断请求是否成功

## 优化效果

1. 代码更加简洁，移除了冗余的HTTP请求实现
2. 统一了请求方案，只使用SDK方案进行请求
3. 确保响应处理逻辑与实际发送请求的方法一致，避免之前可能存在的不一致问题

## 注意事项

1. 仍然保留了对响应内容的解析和验证逻辑，确保只有当HTTP状态码为200且JSON响应中的code字段为"0"时，才将请求标记为成功
2. 异常处理逻辑保持不变，确保即使请求失败也不会影响后续处理 