# 恢复丹能数据推送SDK方案

## 背景

根据客户要求，必须使用SDK方案进行数据推送，虽然SDK默认使用`application/octet-stream`作为Content-Type可能导致服务器端返回不支持的错误，但客户将自行与第三方解决这个问题。

## 修改内容

1. 恢复了`sendRequestAndUpdateLog`方法使用SDK方案发送请求
2. 恢复了`processAndSendData`方法中的SDK初始化代码
3. 删除了不再需要的HTTP客户端相关导入
4. 保留了部分日志增强，便于排查问题

## 恢复方案详情

恢复使用SDK方案，具体实现如下：
1. 使用`HttpsApiClientenv_uat.getInstance().compass_saveinspectdataSyncMode`方法发送请求
2. 明确在请求头中设置Content-Type为"application/json;charset=utf-8"（虽然服务端可能仍会解析为octet-stream）
3. 恢复SDK的初始化代码，设置AppKey和AppSecret
4. 保留了增强的日志记录功能，以便更好地调试和排查问题

## 遗留问题

Content-Type的问题仍然存在，SDK默认可能会使用`application/octet-stream;charset=utf-8`作为Content-Type，这可能导致服务器返回错误：
```
{"code":"100500","data":"","msg":"Content type 'application/octet-stream;charset=utf-8' not supported"}
```

按照要求，客户将与第三方解决这个问题，这可能包括：
1. 让第三方服务器支持octet-stream内容类型
2. 找到修改SDK默认内容类型的方法
3. 调整SDK源码使其正确应用设置的Content-Type 