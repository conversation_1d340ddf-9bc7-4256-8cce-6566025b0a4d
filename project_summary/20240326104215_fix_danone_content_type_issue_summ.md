# 修复丹能数据推送Content-Type错误

## 问题概述

在使用SDK方案发送请求后，服务器返回错误：
```
{"code":"100500","data":"","msg":"Content type 'application/octet-stream;charset=utf-8' not supported"}
```

问题原因是SDK方案默认使用`application/octet-stream`作为Content-Type，而服务器期望的是`application/json;charset=utf-8`。

## 解决方案

我们决定停止使用SDK方案，改为使用原生的Apache HttpClient发送请求，以确保可以正确设置Content-Type头。具体修改包括：

1. 修改`sendRequestAndUpdateLog`方法，使用Apache HttpClient替代SDK发送请求
2. 明确设置Content-Type为"application/json;charset=utf-8"
3. 删除processAndSendData方法中不需要的SDK初始化代码
4. 添加详细的日志记录，帮助追踪请求和响应

## 修改内容

1. 添加了必要的Apache HttpClient相关导入
2. 完全重写了`sendRequestAndUpdateLog`方法，使用原生HttpClient:
   - 设置超时参数
   - 正确设置Content-Type和其他请求头
   - 使用StringEntity发送JSON请求体
   - 处理响应并解析
   - 添加了充分的日志记录和资源清理
3. 移除了不再需要的SDK初始化代码

## 注意事项

1. 仍然保持了原有的业务逻辑，如请求成功条件判断（HTTP 200 + code="0"）
2. 添加了更详细的日志记录，便于排查问题
3. 确保了资源清理，避免连接泄漏

## 影响

此次修改解决了Content-Type不支持的问题，使丹能数据能够正确推送给第三方API，同时保持了代码的可维护性和日志记录的完整性。 