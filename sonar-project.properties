# must be unique in a given SonarQube instance
# --- optional properties ---
sonar.projectKey=bi-service

# defaults to project key
#sonar.projectName=My project
# defaults to 'not provided'
#sonar.projectVersion=1.0
sonar.projectName=${env.JOB_NAME}.${env.BUILD_NUMBER} 
sonar.projectVersion=1.0
sonar.sources=enterprise-api-service/src/main/java/
sonar.java.binaries=enterprise-api-service/src/main/java/com/lenztech/bi/enterprise/
# Encoding of the source code. Default is default system encoding
#sonar.sourceEncoding=UTF-8
