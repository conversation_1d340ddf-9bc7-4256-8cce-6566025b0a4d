apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    k8s-app: NAME
    qcloud-app: NAME
  name: NAME
  namespace: NAMESPACE
spec:
  progressDeadlineSeconds: 600
  replicas: 3
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      k8s-app: NAME
      qcloud-app: NAME
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        k8s-app: NAME
        qcloud-app: NAME
    spec:
      containers:
      - env:
        - name: PARAMS
          value: -Djava.security.egd=file:/dev/./urandom -DJM.SNAPSHOT.PATH=/data
        - name: lenz_logs_NAMESPACE-bi-service
          value: /data/logs/bi-service/info.log
        envFrom:
        - configMapRef:
            name: nacos-conf
        image: VERSION
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /actuator/health
            port: 10094
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 5
        name: NAME
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /actuator/health
            port: 10094
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 5
        resources:
          limits:
            cpu: "4"
            memory: 4Gi
          requests:
            cpu: "1"
            memory: 1Gi
        securityContext:
          runAsGroup: 1000
          runAsUser: 1000
        volumeMounts:
        - mountPath: /data
          name: data
        - mountPath: /nfs_data
          name: nfs-data
        - mountPath: /data/logs/bi-service
          name: log
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: lenz-dockerimg
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - name: data
        nfs:
          path: /data
          server: NFSSERVER
      - name: nfs-data
        nfs:
          path: /
          server: NFSSERVER
      - emptyDir: {}
        name: log
