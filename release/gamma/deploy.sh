#!/bin/bash
echo "发布所有docker服务"
echo "当前用户UID:"`id -u`
echo "当前用户GID:"`id -g`
export userId=`id -u`
export groupId=`id -g`
echo "发布所有docker服务,ipSuffix:"
cd /data/projects/bi-service
sleep 10
stop=$(curl -s http://127.0.0.1:10094/service/stop)
if [ $stop = 'SUCCESS' ];then
    echo "服务已停止"
    while true
    do
        kill=$(curl -s http://127.0.0.1:10094/service/isCanKill)
        if [ $kill = 'true' ];then
            echo $kill
            echo "可以关掉服务了"
            break
        else
            echo $kill
        fi
    done
else
    echo "服务停止失败"
fi
docker-compose -f docker-compose-application.yml pull
docker-compose -f docker-compose-application.yml stop -t 15
docker-compose -f docker-compose-application.yml rm -f
echo "清理none标签镜像"
docker images|grep -E "<none>"| awk '{print $3}' | xargs docker rmi -f||true
docker-compose -f docker-compose-application.yml up -d
sleep 5
docker-compose -f docker-compose-application.yml ps