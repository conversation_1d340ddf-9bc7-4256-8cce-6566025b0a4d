#!/usr/bin/python
# _*_coding:utf-8_*_

import json
import sys
import time
import warnings


from tencentcloud.clb.v20180317 import clb_client, models
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile


warnings.filterwarnings("ignore")
cred = credential.Credential("AKIDAAvQecIitnQmrfDftynLdePt74lBKCoA", "6cssb6KIzjHZcPwD6nGUTCfsqZ3II3Zp")
httpProfile = HttpProfile()
httpProfile.endpoint = "clb.tencentcloudapi.com"
server_id = sys.argv[1]
weight = int(sys.argv[2])
# server_id = "ins-658q67fp"
# weight = 100
clientProfile = ClientProfile()
clientProfile.httpProfile = httpProfile
client = clb_client.ClbClient(cred, "ap-beijing", clientProfile)


def set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port):
    req = models.ModifyTargetWeightRequest()
    params = {
        "LoadBalancerId": clb_id,
        "ListenerId": listenerId,
        "LocationId": locationId,
        "Targets": [
            {
                "InstanceId": server_id,
                "Port": port
            }
        ],
        "Weight": weight
    }

    req.from_json_string(json.dumps(params))
    client.ModifyTargetWeight(req)
    # resp = client.ModifyTargetWeight(req)
    # print(resp.to_json_string())


def get_clb_info(server_id):
    req = models.DescribeTargetsRequest()
    params = {
        "LoadBalancerId": clb_id
    }

    req.from_json_string(json.dumps(params))
    resp = client.DescribeTargets(req)
    json_data = json.loads(resp.to_json_string())
    for dic_i in json_data.keys():
        if dic_i == 'Listeners':
            listeners = json_data.get('Listeners')[0]
            rules = listeners.get('Rules')[0]
            target_lis = rules.get('Targets')
            for server_info in target_lis:
                instanceid = server_info.get('InstanceId')
                if server_id == instanceid:
                    Weight = server_info.get('Weight')
                    # print(Weight)
                    return Weight


clb_id = 'lb-2vn0n0i7'
listenerId = 'lbl-nq6iokkp'
locationId = 'loc-g4mr03ez'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

clb_id = 'lb-li93nwcn'
listenerId = 'lbl-aci4a0ax'
locationId = 'loc-kn4adn53'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

clb_id = 'lb-llg2r009'
listenerId = 'lbl-jmmq8v8p'
locationId = 'loc-nofld4br'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

clb_id = 'lb-llg2r009'
listenerId = 'lbl-hnlg0ns3'
locationId = 'loc-7pgt6z59'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

clb_id = 'lb-rrnxbl43'
listenerId = 'lbl-qgv3gk03'
locationId = 'loc-gwijbhhn'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

clb_id = 'lb-rrnxbl43'
listenerId = 'lbl-0jsrb9l5'
locationId = 'loc-5m35aa5d'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

clb_id = 'lb-3mzaip8j'
listenerId = 'lbl-1noqj5ex'
locationId = 'loc-6sxzo0rz'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

clb_id = 'lb-3mzaip8j'
listenerId = 'lbl-jqszf70p'
locationId = 'loc-oglh8axb'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

clb_id = 'lb-le29bia1'
listenerId = 'lbl-p4etixfp'
locationId = 'loc-jmm0dqpz'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

clb_id = 'lb-le29bia1'
listenerId = 'lbl-5pa4aycl'
locationId = 'loc-azgtz44j'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

Weight = get_clb_info(server_id)
if Weight == weight:
    print("负载均衡权重修改成功，当前权重: %s" %Weight)
else:
    print("修改负载均衡权重失败，当前权重: %s" %Weight)
    sys.exit(1)
time.sleep(2)
