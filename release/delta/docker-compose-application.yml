version: "3"
services:
  enterprise-api-service:
    image: ${REGISTRY}/${NAME_SPACE}/enterprise-api-service:${VERSION}
    user: ${userId}:${groupId}
    container_name: enterprise-api-service
    restart: always
    ports:
      - 10094:10094
      - 11094:11094
    volumes:
      - /data:/data
      - /tmp
    env_file:
      - .env
      - .nacos
    environment:
      - PARAMS=${PARAMS} -Deureka.instance.hostname=${INTERNAL_IP}
      - JAVA_OPTS=${JAVA_OPTS} -Xms128M -Xmx1G -Xss1M
