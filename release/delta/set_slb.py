#!/usr/bin/python
# _*_coding:utf-8_*_

import json
import sys
import time
import warnings


from tencentcloud.clb.v20180317 import clb_client, models
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile


warnings.filterwarnings("ignore")
cred = credential.Credential("AKIDAAvQecIitnQmrfDftynLdePt74lBKCoA", "6cssb6KIzjHZcPwD6nGUTCfsqZ3II3Zp")
httpProfile = HttpProfile()
httpProfile.endpoint = "clb.tencentcloudapi.com"
server_id = sys.argv[1]
weight = int(sys.argv[2])

clientProfile = ClientProfile()
clientProfile.httpProfile = httpProfile
client = clb_client.ClbClient(cred, "ap-beijing", clientProfile)


def set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port=80):
    req = models.ModifyTargetWeightRequest()
    params = {
        "LoadBalancerId": clb_id,
        "ListenerId": listenerId,
        "LocationId": locationId,
        "Targets": [
            {
                "InstanceId": server_id,
                "Port": port
            }
        ],
        "Weight": weight
    }

    req.from_json_string(json.dumps(params))
    client.ModifyTargetWeight(req)
    # resp = client.ModifyTargetWeight(req)
    # print(resp.to_json_string())


def get_clb_info(server_id):
    req = models.DescribeTargetsRequest()
    params = {
        "LoadBalancerId": clb_id
    }

    req.from_json_string(json.dumps(params))
    resp = client.DescribeTargets(req)
    json_data = json.loads(resp.to_json_string())
    for dic_i in json_data.keys():
        if dic_i == 'Listeners':
            listeners = json_data.get('Listeners')[0]
            rules = listeners.get('Rules')[0]
            target_lis = rules.get('Targets')
            for server_info in target_lis:
                instanceid = server_info.get('InstanceId')
                if server_id == instanceid:
                    Weight = server_info.get('Weight')
                    # print(Weight)
                    return Weight


clb_id = 'lb-macrmue9'
listenerId = 'lbl-imxuxvkz'
locationId = 'loc-jv00tu8d'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

clb_id = 'lb-macrmue9'
listenerId = 'lbl-akmjpax7'
locationId = 'loc-o5y3sux9'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

clb_id = 'lb-ryaep7in'
listenerId = 'lbl-jmdunz8r'
locationId = 'loc-f1eg3t8n'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

clb_id = 'lb-ryaep7in'
listenerId = 'lbl-o0y7cqiz'
locationId = 'loc-jus819wt'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

clb_id = 'lb-ifgd9wmh'
listenerId = 'lbl-3mtpi80f'
locationId = 'loc-cb82odkd'
port = 80
set_clb_weight(clb_id, server_id, listenerId, locationId, weight, port)
time.sleep(3)

Weight = get_clb_info(server_id)
if Weight == weight:
    print("负载均衡权重修改成功，当前权重: %s" %Weight)
else:
    print("修改负载均衡权重失败，当前权重: %s" %Weight)
    sys.exit(1)
time.sleep(2)