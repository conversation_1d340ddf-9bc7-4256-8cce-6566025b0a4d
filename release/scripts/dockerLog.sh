#!/bin/bash
if [ ! -n "$1" ] ;then
    echo "you have not input a service name,use default service gateway!"
    service=gateway
else
    echo "you input service name is $1"
    service=$1
fi
if [ ! -n "$2" ] ;then
    echo "you have not input line number,use default line number:300!"
    lines=300
else
    echo "you input line number is $2"
    lines=$2
fi
ID=`docker ps|grep ${service} |awk '{print $1}'`
echo "查看容器: "${ID} "尾部" ${lines} "行日志"
docker logs -f --tail ${lines} ${ID}