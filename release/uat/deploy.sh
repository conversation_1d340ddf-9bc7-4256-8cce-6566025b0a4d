#!/bin/bash
echo "发布所有docker服务"
echo "当前用户UID:"`id -u`
echo "当前用户GID:"`id -g`
export userId=`id -u`
export groupId=`id -g`
echo "发布所有docker服务,ipSuffix:"
cd /data/projects/bi-service
docker-compose -f docker-compose-application.yml pull
docker-compose -f docker-compose-application.yml stop -t 15
docker-compose -f docker-compose-application.yml rm -f
echo "清理none标签镜像"
docker images|grep -E "<none>"| awk '{print $3}' | xargs docker rmi -f||true
docker-compose -f docker-compose-application.yml up -d
sleep 5
docker-compose -f docker-compose-application.yml ps