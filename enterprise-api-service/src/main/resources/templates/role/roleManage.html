<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<link rel="stylesheet" type="text/css" href="/css/formSelects-v4.css"/>
<head th:include="layout :: htmlhead" th:with="title='角色管理'"></head>
<script type="text/javascript" src="/js/dateUtils.js"></script>
<body class="layui-layout-body">
<div class="layui-layout layui-layout-admin">
    <!--头-->
    <div th:replace="fragments/head :: header"></div>

    <div class="layui-body" style="margin: 1%">
        <table id="roleList" lay-filter="roleTable"></table>

        <button class="layui-btn layui-btn-normal" onclick="add()">新增</button>

        <script type="text/html" id="optBar">
            <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
            {{#  if(d.roleStatus == '0'){ }}
            <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="recover">恢复</a>
            {{#  } else { }}
            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
            {{#  } }}
        </script>
    </div>

    <!--添加或编辑-->
    <div id="setRole" class="layer_self_wrap" style="width:500px;display:none;">
        <form id="roleForm" class="layui-form layui-form-pane" method="post" action="" style="margin-top: 20px;">
            <input id="pageNum" type="hidden" name="pageNum"/>
            <input id="id" type="hidden" name="id"/>

            <div class="layui-form-item">
                <label class="layui-form-label">角色名称</label>
                <div class="layui-input-inline">
                    <input id="roleName" name="roleName" lay-verify="required" autocomplete="off" class="layui-input" type="text"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">角色描述</label>
                <div class="layui-input-inline">
                    <input id="roleDesc" name="roleDesc" lay-verify="required" autocomplete="off" class="layui-input" type="text"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">权限</label>
                <div class="layui-input-inline">
                    <select name="permissions"  xm-select="permissions">
                        <!--<option value="1">北京</option>
                        <option value="2">上海</option>
                        <option value="3">广州</option>
                        <option value="4">深圳</option>
                        <option value="5">天津</option>-->
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block" style="margin-left: 10px;">
                    <button class="layui-btn"  lay-submit="" lay-filter="roleSubmit">提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>

    <!--底部-->
    <div th:replace="fragments/footer :: footer"></div>
    <script src="/js/role/roleManage.js"></script>

</div>
</body>
</html>