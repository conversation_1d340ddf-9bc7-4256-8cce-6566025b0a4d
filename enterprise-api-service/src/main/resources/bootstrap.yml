server:
  port: 10094
  compression:
    enabled: true
    min-response-size: 10
    excluded-user-agents: gozilla,traviata|bss
    mime-types: application/json,application/xml,text/htm
spring:
  application:
    name: bi-service
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  output:
    ansi:
      enabled: always
  profiles:
    # 环境配置
    active: local
  cloud:
    sentinel:
      transport:
        port: 11094
    nacos:
      username: local
      password: ppz#2021@local
      server-addr: txnacos.langjtech.com:80
      discovery:
        namespace: ${spring.profiles.active}
        group: Tope
      config:
        # 配置文件格式
        file-extension: yml
        # 分组
        group: Tope
        refresh-enabled: true
        prefix: ${spring.application.name}
        namespace: ${spring.profiles.active}
        # 共享配置
#        extension-configs:
#          - data-id: bi-service-application.yml # 配置文件名-Data Id
#            group: Tope # 默认为DEFAULT_GROUP
#            refresh: true # 是否动态刷新，默认为false
mybatis-plus:
  mapper-locations: classpath:mybatis/mappers/**/*.xml
  type-aliases-package: com.lenztech.bi.enterprise.entity
management:
  endpoints:
    web:
      exposure:
        include: 'metrics,heapdump,threaddump,httptrace,beans,health,prometheus'
      base-path: /actuator
    health:
      show-details: always
  metrics:
    tags:
      application: ${spring.application.name}

trax:
  lenz:
    snowflake:
      datacenterId: 1
      enableServer: true
      host: localhost
      port: 8080
      maxTotal: 2000
      minEvictableIdleTimeMillis: 3000
      timeBetweenEvictionRunsMillis: 1000

sharding:
  jmlEffectiveDay: 20250101
  pecEffectiveDay: 20250101
  unileverEffectiveDay: 20250101
