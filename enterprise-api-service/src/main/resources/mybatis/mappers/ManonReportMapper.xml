<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenztech.bi.enterprise.mapper.ManonReportMapper">

	<!-- 根据拜访id查询 -->
	<select id="getBiTargetList" resultType="com.lenztech.bi.enterprise.dto.manon.ManonBiReport">
		SELECT
			id,
			task_id AS taskId,
			response_id AS responseId,
			question_id AS questionId,
			img_id AS imgId,
			is_remake AS isRemake,
			really_layer AS reallyLayer,
			facingNo,
			product_name AS productName,
			product_id AS productId,
			customer_code AS customerCode,
			facing_count AS facingCount,
			img_local_path AS imageLocalPath
		FROM dm_manlun
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
  	</select>


</mapper>