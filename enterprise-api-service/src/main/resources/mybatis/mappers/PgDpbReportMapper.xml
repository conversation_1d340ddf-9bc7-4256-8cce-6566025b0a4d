<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenztech.bi.enterprise.mapper.PgDpbReportMapper" >

    <select id="getPgHsmDetailDTOList" resultType="com.lenztech.bi.enterprise.dto.pg.PgHsmSkuDetail">
        select phdbr.id id,phdbr.category category,phdbr.kpi_name kpiName,phdbr.kpi_value kpiValue,phc.kpi_type kpiType,phc.barcode barCode from pg_hsm_detail_bi_report phdbr left join pg_hsm_conf phc on (phdbr.kpi_name=phc.kpi_name) where phc.month=#{month, jdbcType=VARCHAR} and phdbr.response_id=#{responseId, jdbcType=VARCHAR}
        and phdbr.date=#{date, jdbcType=VARCHAR}
    </select>

    <select id="getPgHsmStep2BiReportDateList" resultType="java.lang.String">
        select date from  pg_hsm_step2_bi_report WHERE exec_date = #{execDate, jdbcType=VARCHAR} group by date;
    </select>

    <select id="getPgHsmDetailDTOListByCategory" resultType="com.lenztech.bi.enterprise.dto.pg.PgHsmSkuDetail">
        select phdbr.id id,phdbr.category category,phdbr.kpi_name kpiName,phdbr.kpi_value kpiValue,phc.kpi_type kpiType,phc.barcode barCode from pg_hsm_detail_bi_report phdbr left join pg_hsm_conf phc on (phdbr.kpi_name=phc.kpi_name) where phc.month=#{month, jdbcType=VARCHAR} and phdbr.category=#{category, jdbcType=VARCHAR} and phdbr.response_id=#{responseId, jdbcType=VARCHAR}
                                                                                                                                                                                                                                            and phdbr.date=#{date, jdbcType=VARCHAR}
    </select>
    <select id="getPgHsmDetailDTOListByCategoryDcp"
            resultType="com.lenztech.bi.enterprise.dto.pg.PgHsmSkuDetail">
        select phdbr.id id,phdbr.category category,phdbr.kpi_name kpiName,phdbr.kpi_value kpiValue,phc.kpi_type kpiType,phc.barcode barCode from pg_hsm_detail_bi_report_dcp phdbr left join pg_hsm_conf phc on (phdbr.kpi_name=phc.kpi_name) where phc.month=#{month, jdbcType=VARCHAR} and phdbr.category=#{category, jdbcType=VARCHAR} and phdbr.response_id=#{responseId, jdbcType=VARCHAR}
                                                                                                                                                                                                                                            and phdbr.date=#{date, jdbcType=VARCHAR}

    </select>

    <insert id="batchInsert" keyProperty="id">
        INSERT INTO pg_hsm_cdl_push_detail_log (exec_date, task_id, response_id, addressIDnum, category, month, date, kpi_name, kpi_value, customer_type, create_time, update_time)
        VALUES
        <foreach item="item" index="index" collection="list" open="(" separator="),(" close=")">
            #{item.execDate}, #{item.taskId}, #{item.responseId}, #{item.addressIDnum}, #{item.category}, #{item.month}, #{item.date}, #{item.kpiName}, #{item.kpiValue}, #{item.customerType}, now(), now()
        </foreach>
    </insert>
    <insert id="insertBatchPskuDetail">
        INSERT INTO pg_ge_dp_fmot_psku_detail (month, date, address_id, distributor_id, distributor_store_id, store_seq_code, is_dcp_Flag, category, psku_actual, sku_name, sku_barcode, sku_distribution, store_type, sub_banner_name, category_code, create_time, update_time)
        VALUES
        <foreach item="item" index="index" collection="list" open="(" separator="),(" close=")">
            #{item.month}, #{item.date}, #{item.addressId}, #{item.distributorId}, #{item.distributorStoreId}, #{item.storeSeqCode}, #{item.isDcpFlag}, #{item.category}, #{item.pskuActual}, #{item.skuName}, #{item.skuBarcode}, #{item.skuDistribution}, #{item.storeType}, #{item.subBannerName}, #{item.categoryCode}, now(), now()
        </foreach>
    </insert>

</mapper>