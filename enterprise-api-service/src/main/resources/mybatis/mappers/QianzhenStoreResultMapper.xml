<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.QianzhenStoreResultMapper">


    <select id="getListDynamicTable" resultType="com.lenztech.bi.enterprise.entity.QianzhenStoreResult">
        SELECT
            id,
            response_id as responseId,
            mdist_target as mdistTarget,
            mdist_count as mdistCount ,
            mdist_rate as mdistRate,
            dist_count as distCount,
            lack_count as lackCount,
            facing_count as facingCount,
            target_rate as  targetRate,
            percentage,
            mfac_target as mfacTarget
        FROM ${prefix}_store_result
        WHERE response_id = #{responseId,jdbcType=VARCHAR};
    </select>
</mapper>
