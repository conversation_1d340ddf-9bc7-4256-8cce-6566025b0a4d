<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.DanengPushLogMapper">

    <!-- 批量插入日志记录 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO daneng_push_log (
            store_code, response_id, request_body, response_body, 
            status, status_code, error_message, push_time, 
            response_time, nonce, customer_type, month, 
            inspect_date, create_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
            #{item.storeCode}, #{item.responseId}, #{item.requestBody}, #{item.responseBody}, 
            #{item.status}, #{item.statusCode}, #{item.errorMessage}, #{item.pushTime}, 
            #{item.responseTime}, #{item.nonce}, #{item.customerType}, #{item.month}, 
            #{item.inspectDate}, #{item.createTime}
            )
        </foreach>
    </insert>


</mapper> 