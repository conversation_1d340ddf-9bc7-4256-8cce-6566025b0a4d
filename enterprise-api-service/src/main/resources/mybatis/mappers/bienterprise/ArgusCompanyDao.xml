<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.bienterprise.ArgusCompanyDao">
    <select id="findRealNameByAaccount" resultType="java.lang.String">
        SELECT real_name FROM `user` WHERE company_id=#{companyId} and account=#{account} and is_deleted=0
    </select>

    <select id="getCompanyMessage" parameterType="java.lang.String" resultType="com.lenztech.bi.enterprise.dto.bi.CompanyDTO">
        select company_id companyId,company_name companyName from company where is_deleted=0 and   company_name LIKE  CONCAT('%',#{message},'%')
    </select>

    <select id="getCompanyMessageByName" parameterType="java.lang.String" resultType="com.lenztech.bi.enterprise.dto.bi.CompanyDTO">
        select company_id companyId,company_name companyName from company where is_deleted=0 and company_name =  #{companyName} limit 1
    </select>

    <select id="getCompanyUser" resultType="com.lenztech.bi.enterprise.dto.CompanyUserDTO">
        SELECT account, company_id AS companyId FROM `user` WHERE account=#{account} and is_deleted=0
    </select>

    <select id="getCompanyUserList" resultType="com.lenztech.bi.enterprise.dto.CompanyUserDTO">
        SELECT account, company_id AS companyId, real_name AS realName   FROM `user` WHERE company_id=#{companyId} and is_deleted=0
    </select>

    <select id="getCompanyId" parameterType="java.lang.String" resultType="com.lenztech.bi.enterprise.dto.bi.CompanyDTO">
        select company_id companyId,company_name companyName from company where is_deleted=0 and   company_name = #{message,jdbcType=VARCHAR}
    </select>
</mapper>
