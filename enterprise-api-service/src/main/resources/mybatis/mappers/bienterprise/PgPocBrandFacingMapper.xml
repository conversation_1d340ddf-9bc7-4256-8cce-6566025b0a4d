<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.bienterprise.PgPocBrandFacingMapper">

    <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.PgPocBrandFacingEntity">
        <result column="taskid" property="taskId" jdbcType="VARCHAR"/>
        <result column="rid" property="rid" jdbcType="VARCHAR"/>
        <result column="brand" property="brand" jdbcType="VARCHAR"/>
        <result column="facing" property="facing" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="columns">
        taskid,
		rid,
		brand,
		facing,
		update_time
	</sql>

    <sql id="searchCondition">
        1 = 1
        <if test="responseId != null">
            AND rid = #{responseId}
        </if>
    </sql>

    <select id="listPgPocBrandFacingEntity" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="columns"></include>
        FROM
        t_poc_brand_facing
        WHERE
        <include refid="searchCondition" />
    </select>

</mapper>