<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.bienterprise.PgPocRuleMapper">

    <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.PgPocRuleEntity">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="taskid" property="taskId" jdbcType="VARCHAR"/>
        <result column="rid" property="rid" jdbcType="VARCHAR"/>
        <result column="image" property="image" jdbcType="INTEGER"/>
        <result column="layer" property="layer" jdbcType="INTEGER"/>
        <result column="product_id" property="productId" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="coordinate" property="coordinate" jdbcType="VARCHAR"/>
        <result column="store_status" property="storeStatus" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="columns">
		id,
		taskid,
		rid,
		image,
		layer,
		product_id,
		status,
		coordinate,
		store_status
	</sql>

    <sql id="searchCondition">
        1 = 1
        <if test="responseId != null">
            AND rid = #{responseId}
        </if>
    </sql>

    <select id="listPgPocRuleEntity" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="columns"></include>
        FROM
        t_poc_rule
        WHERE
        <include refid="searchCondition" />
    </select>

</mapper>