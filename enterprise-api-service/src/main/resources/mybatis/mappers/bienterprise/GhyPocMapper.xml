<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.bienterprise.GhyPocMapper">
  <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.GhyPocEntity">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="response_id" jdbcType="VARCHAR" property="responseId" />
    <result column="image_id" jdbcType="VARCHAR" property="imageId" />
    <result column="scene" jdbcType="VARCHAR" property="scene" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="price" jdbcType="DOUBLE" property="price" />
    <result column="index_name" jdbcType="VARCHAR" property="indexName" />
    <result column="index_value" jdbcType="VARCHAR" property="indexValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, response_id, image_id, scene, product_id, price, index_name, index_value, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ghy_poc_2002
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ghy_poc_2002
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenztech.bi.enterprise.entity.GhyPocEntity">
    insert into ghy_poc_2002 (id, response_id, image_id, 
      scene, product_id, price, 
      index_name, index_value, create_time
      )
    values (#{id,jdbcType=INTEGER}, #{responseId,jdbcType=VARCHAR}, #{imageId,jdbcType=VARCHAR}, 
      #{scene,jdbcType=VARCHAR}, #{productId,jdbcType=INTEGER}, #{price,jdbcType=DOUBLE}, 
      #{indexName,jdbcType=VARCHAR}, #{indexValue,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.lenztech.bi.enterprise.entity.GhyPocEntity">
    insert into ghy_poc_2002
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="responseId != null">
        response_id,
      </if>
      <if test="imageId != null">
        image_id,
      </if>
      <if test="scene != null">
        scene,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="indexName != null">
        index_name,
      </if>
      <if test="indexValue != null">
        index_value,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="responseId != null">
        #{responseId,jdbcType=VARCHAR},
      </if>
      <if test="imageId != null">
        #{imageId,jdbcType=VARCHAR},
      </if>
      <if test="scene != null">
        #{scene,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DOUBLE},
      </if>
      <if test="indexName != null">
        #{indexName,jdbcType=VARCHAR},
      </if>
      <if test="indexValue != null">
        #{indexValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenztech.bi.enterprise.entity.GhyPocEntity">
    update ghy_poc_2002
    <set>
      <if test="responseId != null">
        response_id = #{responseId,jdbcType=VARCHAR},
      </if>
      <if test="imageId != null">
        image_id = #{imageId,jdbcType=VARCHAR},
      </if>
      <if test="scene != null">
        scene = #{scene,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="indexName != null">
        index_name = #{indexName,jdbcType=VARCHAR},
      </if>
      <if test="indexValue != null">
        index_value = #{indexValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenztech.bi.enterprise.entity.GhyPocEntity">
    update ghy_poc_2002
    set response_id = #{responseId,jdbcType=VARCHAR},
      image_id = #{imageId,jdbcType=VARCHAR},
      scene = #{scene,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=INTEGER},
      price = #{price,jdbcType=DOUBLE},
      index_name = #{indexName,jdbcType=VARCHAR},
      index_value = #{indexValue,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="listGhyPocEntityEntity" parameterType="java.util.Map" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"></include>
    FROM ghy_poc_2002
    WHERE
    response_id = #{responseId,jdbcType=VARCHAR}
    and image_id = #{imageId,jdbcType=VARCHAR}
  </select>
</mapper>