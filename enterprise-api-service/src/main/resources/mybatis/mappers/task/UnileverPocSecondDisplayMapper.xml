<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.task.UnileverSecondDisplayImageMapper">

    <select id="getSecondDisplayImages" resultType="com.lenztech.bi.enterprise.dto.unilever.SecondDisplayImageDTO">
        SELECT * FROM
(SELECT img_id, repeat_no FROM t_image_shelf_image_repeat WHERE response_id=#{rid, jdbcType=VARCHAR}) AS repTable
INNER JOIN
(SELECT id, shelf_id, originally_image_url FROM t_image_shelf_image WHERE response_id=#{rid, jdbcType=VARCHAR}) AS linkTable
ON repTable.img_id=linkTable.id
WHERE repeat_no=#{repeatNo, jdbcType=VARCHAR}
    </select>
</mapper>
