<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.task.TResponseMapper">
    <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.TResponse" >
        <id column="Id" property="id" jdbcType="VARCHAR" />
        <result column="taskid_owner" property="taskidOwner" jdbcType="VARCHAR" />
        <result column="taskid" property="taskid" jdbcType="VARCHAR" />
        <result column="phone" property="phone" jdbcType="VARCHAR" />
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="position" property="position" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="uId" property="uId" jdbcType="VARCHAR" />
        <result column="GPS" property="gps" jdbcType="VARCHAR" />
        <result column="actid1" property="actid1" jdbcType="VARCHAR" />
        <result column="actid2" property="actid2" jdbcType="VARCHAR" />
        <result column="alterstatus" property="alterstatus" jdbcType="VARCHAR" />
        <result column="altertime1" property="altertime1" jdbcType="TIMESTAMP" />
        <result column="altertime2" property="altertime2" jdbcType="TIMESTAMP" />
        <result column="nowstatus" property="nowstatus" jdbcType="VARCHAR" />
        <result column="rangevalue" property="rangevalue" jdbcType="OTHER" />
        <result column="tips" property="tips" jdbcType="VARCHAR" />
        <result column="shdq" property="shdq" jdbcType="VARCHAR" />
        <result column="sswcldq" property="sswcldq" jdbcType="VARCHAR" />
        <result column="fen" property="fen" jdbcType="VARCHAR" />
        <result column="fen1" property="fen1" jdbcType="VARCHAR" />
        <result column="fen2" property="fen2" jdbcType="VARCHAR" />
        <result column="up_time" property="upTime" jdbcType="TIMESTAMP" />
        <result column="percent" property="percent" jdbcType="DOUBLE" />
        <result column="reward_old" property="rewardOld" jdbcType="DOUBLE" />
        <result column="doorGPS" property="doorGPS" jdbcType="VARCHAR" />
        <result column="doorAddress" property="doorAddress" jdbcType="VARCHAR" />
        <result column="ifoblique_task" property="ifobliqueTask" jdbcType="VARCHAR" />
        <result column="ifoblique_user" property="ifobliqueUser" jdbcType="VARCHAR" />
        <result column="store_status" property="storeStatus" jdbcType="TINYINT" />
    </resultMap>
    <sql id="Base_Column_List" >
      Id, taskid_owner, taskid, phone, start_time, end_time, position, status, uId, GPS,
      actid1, actid2, alterstatus, altertime1, altertime2, nowstatus, rangevalue, tips,
      shdq, sswcldq, fen, fen1, fen2, up_time, percent, reward_old, doorGPS, doorAddress,
      ifoblique_task, ifoblique_user, store_status
    </sql>

    <select id="selectRidByTaskId" resultType="java.lang.String">
        SELECT
        *
        FROM
        t_response
        WHERE
        taskid_owner = #{taskId, jdbcType=VARCHAR}
        ORDER BY
        up_time DESC
    </select>

    <select id="selectLastVisitTimeByQid" resultType="java.lang.String">
        SELECT
	        DATE_FORMAT( update_time, '%Y-%m-%d %H:%i:%S' )
        FROM
	        t_answer
        WHERE
	        qid = #{qid, jdbcType=VARCHAR}
        ORDER BY
	        update_time DESC
	        LIMIT 1;
    </select>

    <select id="selectTaskIdByQid" resultType="java.lang.String">
        SELECT
        taskid
        FROM
        t_question
        WHERE
        id = #{qid, jdbcType=VARCHAR}
     </select>

    <select id="getEnterpriseResponseByUpTime" resultType="com.lenztech.bi.enterprise.dto.CompanyResponseDTO" >
        SELECT * from (
        SELECT
        tt.enterprise_edition AS company,
        tr.id AS responseId,
        tr.uId AS userId,
        tr.start_time AS startTime,
        tr.end_time AS endTime,
        tr.taskid AS storeId
        FROM
        t_response tr
        LEFT JOIN
        t_task tt ON tr.taskid_owner = tt.id
        WHERE
        tt.enterprise_edition IS NOT NULL
        AND tr.up_time <![CDATA[ >= ]]> #{startTime, jdbcType=VARCHAR}
        AND tr.up_time <![CDATA[ <= ]]> #{endTime, jdbcType=VARCHAR}
        AND tt.address_status = 1
        ) temp
        WHERE
        temp.company != ""
    </select>

    <select id="getResponseByDateAndUserList" resultType="com.lenztech.bi.enterprise.entity.TResponse" >
        SELECT
        tr.id AS id,
        tr.taskid AS taskid,
        tr.start_time AS startTime,
        tr.end_time AS endTime
        FROM
        t_response tr
        LEFT JOIN
        t_task tt ON tr.taskid_owner = tt.id
        WHERE
        tr.uId = #{userId, jdbcType=VARCHAR}
        AND tr.up_time <![CDATA[ >= ]]> #{startTime, jdbcType=VARCHAR}
        AND tr.up_time <![CDATA[ <= ]]> #{endTime, jdbcType=VARCHAR}
        AND tt.address_status = 1
    </select>

    <select id="getStoreList" resultType="com.lenztech.bi.enterprise.dto.StoreListDTO" >
        SELECT
        tr.taskid AS storeId,
        ttl.reallyAddress AS storeName
        FROM
        t_response tr
        LEFT JOIN t_tasklaunch ttl ON tr.taskid = ttl.taskid
        WHERE
        tr.uId = #{userId, jdbcType=VARCHAR}
        AND tr.start_time <![CDATA[ >= ]]> #{startTime, jdbcType=VARCHAR}
        AND tr.start_time <![CDATA[ <= ]]> #{endTime, jdbcType=VARCHAR}
        GROUP BY tr.taskid
    </select>
    <select id="getUserResponseList" resultType="com.lenztech.bi.enterprise.dto.UserResponseListDTO" >
      SELECT
      tr.id AS responseId,
      ttl.reallyAddress AS storeName,
      ttl.position AS taskAddress,
      tr.position AS executeAddress,
      tr.start_time AS executeDateTime,
      tr.end_time AS endDateTime,
      tr.up_time AS upLoadDateTime
      FROM
      t_response tr
      LEFT JOIN t_tasklaunch ttl ON tr.taskid = ttl.taskid
      WHERE
      tr.uId = #{userId, jdbcType=VARCHAR}
        AND tr.start_time <![CDATA[ >= ]]> #{startTime, jdbcType=VARCHAR}
        AND tr.start_time <![CDATA[ <= ]]> #{endTime, jdbcType=VARCHAR}
        <if test="storeId != null">
            AND tr.taskid = #{storeId, jdbcType=VARCHAR}
        </if>
      ORDER BY tr.start_time
    </select>

    <select id="getUserVisitStoreNum" resultType="com.lenztech.bi.enterprise.dto.UserVisitStoreNumDTO" >
      SELECT
      tr.uId AS userId,
      tr.taskid AS storeId,
      COUNT(*) AS storeNum
      FROM
      t_response tr
      LEFT JOIN t_task tt ON tr.taskid_owner = tt.id
      WHERE
      tr.uId IN
      <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
          #{item, jdbcType=VARCHAR}
      </foreach>
      AND tr.start_time <![CDATA[ >= ]]> #{startTime, jdbcType=VARCHAR}
      AND tr.start_time <![CDATA[ <= ]]> #{endTime, jdbcType=VARCHAR}
      AND tt.address_status = 1
      GROUP BY tr.uId,tr.taskid
    </select>

</mapper>
