<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.task.QingPiKpiMapper">

	<!-- 分销集合 -->
	<select id="getProductDistList" resultType="com.lenztech.bi.enterprise.dto.tsingtao.TsingtaoProduct">
		SELECT
			t1.response_id responseId,
			t4.merge_id mergeId ,
			t4.product_simple_name productName,
			t4.Tsingtao tsingtao,
			max(
				IF (
					t1.appeal_result_status IS NOT NULL,
					t1.appeal_result_status,
					t1.`status`
				)
			) exist
		FROM
			t_image_store_product_exist t1
		JOIN lenzbi.product_list_qingpi_202006 t4 ON t4.product_id = t1.product_id
		WHERE
			t1.response_id = #{responseId}
		GROUP BY
			t4.merge_id
    </select>

    <!-- 面位集合 -->
    <select id="getProductFacingList" resultType="com.lenztech.bi.enterprise.dto.tsingtao.TsingtaoProduct">
		SELECT
			tr.id responseId,
			qt.merge_id mergeId,
			qt.Tsingtao tsingtao,
			IFNULL(
				sum(tip.isfaceing * qt.ratio),
				sum(tip.isfaceing)
			) facingCount
		FROM t_response tr
		LEFT JOIN t_image_shelf tis ON tis.response_id = tr.id
		LEFT JOIN t_image_shelf_image tisi ON tisi.shelf_id = tis.shelf_id
		LEFT JOIN t_image_product tip ON tip.image_id = tisi.id AND tip.response_id = tr.id
		LEFT JOIN lenzbi.product_list_qingpi_202006 qt ON qt.product_id = tip.product_id
		WHERE
			tis.groupno = 0
			AND tis.question_id IN (
					SELECT
						id
					FROM
						t_question
					WHERE
						taskid = (SELECT
									taskid_owner
									FROM
										t_response
									WHERE
										id = #{responseId}
									)
						AND qindex = '2'
					)
			AND tr.id = #{responseId}
			AND tip.inrange != 2
		GROUP BY
			qt.merge_id
    </select>

</mapper>
