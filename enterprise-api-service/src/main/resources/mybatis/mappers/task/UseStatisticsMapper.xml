<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.task.UseStatisticsMapper">

    <insert id="batchInsert">
        insert into t_use_statistics
        (date,user_id,
        account,
        user_name,
        enterprise_id,
        visit_num,
        visit_store_num,
        start_time,
        end_time,
        on_road_time,
        picture_num,
        response_num)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.date,jdbcType=VARCHAR},
            #{item.userId,jdbcType=VARCHAR},
            #{item.account,jdbcType=VARCHAR},
            #{item.userName,jdbcType=VARCHAR},
            #{item.enterpriseId,jdbcType=VARCHAR},
            #{item.visitNum,jdbcType=VARCHAR},
            #{item.visitStoreNum,jdbcType=VARCHAR},
            #{item.startTime,jdbcType=VARCHAR},
            #{item.endTime,jdbcType=VARCHAR},
            #{item.onRoadTime,jdbcType=VARCHAR},
            #{item.pictureNum,jdbcType=VARCHAR},
            #{item.responseNum,jdbcType=VARCHAR})
        </foreach>
        ON DUPLICATE KEY UPDATE
        visit_num = VALUES(visit_num),
        visit_store_num = VALUES(visit_store_num),
        start_time = VALUES(start_time),
        end_time = VALUES(end_time),
        on_road_time = VALUES(on_road_time),
        picture_num = VALUES(picture_num),
        response_num = VALUES(response_num)
    </insert>
</mapper>
