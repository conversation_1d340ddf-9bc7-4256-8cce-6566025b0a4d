<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.task.OperateLogMapper">

    <select id="getUserUseAppDays" resultType="com.lenztech.bi.enterprise.dto.UserUseAppDayDTO">
        SELECT
	    temp.user_id AS userId,
	    temp.phone,
	    COUNT( * ) AS days
        FROM
	    ( SELECT
	        tol.user_id,
	        CAST( tol.operate_time AS DATE ) AS operateDate,
	        tol.phone
	        FROM t_operate_log tol
	        WHERE
	        tol.operate_time <![CDATA[ >= ]]> #{startTime, jdbcType=VARCHAR}
			AND tol.operate_time <![CDATA[ <= ]]> #{endTime, jdbcType=VARCHAR}
			AND tol.company_id = #{companyId, jdbcType=VARCHAR}
	    ) temp
        GROUP BY
        temp.operateDate,
	    temp.user_id
    </select>
</mapper>
