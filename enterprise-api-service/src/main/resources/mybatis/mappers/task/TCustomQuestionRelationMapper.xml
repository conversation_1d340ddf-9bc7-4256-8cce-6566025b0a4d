<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.task.TCustomQuestionRelationMapper">
    <select id="selectBICompanyId" resultType="java.lang.Integer">
        select bi_service_mapping_company_id FROM t_custom_question_relation
        WHERE partner_id = #{partnerId,jdbcType=VARCHAR} limit 1
    </select>


    <select id="selectResponseId" resultType="java.lang.String">
        select response_id from t_custom_request where data_id = #{dataId,jdbcType=VARCHAR}
    </select>

    <select id="selectTaskIdFromResponse" resultType="java.lang.String">
        select taskid_owner from t_response where id =  #{responseId,jdbcType=VARCHAR}
    </select>

    <select id="selectBICompanyIdByTaskId" resultType="java.lang.Integer" >
        select bi_service_mapping_company_id from t_custom_question_relation where task_id = #{taskId,jdbcType=VARCHAR}
        limit 1
    </select>
</mapper>
