<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.ByhealthReportMapper">

	<!-- 查询图片集合 -->
	<select id="getTargetList" resultType="com.lenztech.bi.enterprise.dto.byhealth.ByhealthBiResult">
		SELECT
		    id,
			task_id,
			response_id,
			image_id,
			facing_count,
			other_facing_count,
			hj_facings,
			hj_other_facings,
			dj_facings,
			dj_other_facings,
			dd_facings,
			dd_other_facings
		from t_response_other
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
  	</select>
</mapper>