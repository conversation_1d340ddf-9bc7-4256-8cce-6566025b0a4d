<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.JzV2DisplayMapper">


    <!--查询图片集合-->
    <select id="getImageList" resultType="com.lenztech.bi.enterprise.dto.jzv2.JzDisplay">
        select
            response_id responseId,
            img_id imgId,
            scene_name sceneName,
            scene_seq sceneSeq,
            area,
            ratio,
            if_duplicate ifDuplicate,
            duplicate_img_id duplicateImgId,
            update_time updateTime
        from jz_display
        where response_id = #{responseId, jdbcType=VARCHAR}
    </select>
</mapper>