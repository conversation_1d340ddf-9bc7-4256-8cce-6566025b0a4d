<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.XinheAppAggDataMapper">
    <select id="getListByImportInfo" resultType="com.lenztech.bi.enterprise.entity.XinheAppAggData">
        select * from xinhe_app_agg_data
        <where>
            <if test=" downloadExcelDTO.taskId != null ">
                and task_id = #{downloadExcelDTO.taskId}
            </if>
            <if test=" downloadExcelDTO.importTaskId != null ">
                and import_taskid = #{downloadExcelDTO.importTaskId}
            </if>
            <if test=" downloadExcelDTO.importBatchId != null ">
                and import_batchid = #{downloadExcelDTO.importBatchId}
            </if>
        </where>
        <if test=" downloadExcelDTO.limit != null and downloadExcelDTO.offset != null">
            limit #{downloadExcelDTO.offset},#{downloadExcelDTO.limit}
        </if>
    </select>
</mapper>
