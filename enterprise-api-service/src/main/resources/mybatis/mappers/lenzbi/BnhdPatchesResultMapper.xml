<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.BnhdPatchesResultMapper">

    <select id="getProductPriceInfo" resultType="com.lenztech.bi.enterprise.dto.bnhd.ProductPriceInfo">
        SELECT product_name productName,count(1) totalAmount,GROUP_CONCAT(price) productPrice from bnhd_patches_result where response_id=#{responseId} GROUP by product_id;
    </select>

    <select id="getRecognizeSceneProducts" resultType="com.lenztech.bi.enterprise.dto.bnhd.ProductPriceInfo">
        SELECT product_name productName,count(1) totalAmount,GROUP_CONCAT(level) currentUnit from bnhd_patches_result where response_id=#{responseId} and scene=#{scene} GROUP by product_id;
    </select>
</mapper>
