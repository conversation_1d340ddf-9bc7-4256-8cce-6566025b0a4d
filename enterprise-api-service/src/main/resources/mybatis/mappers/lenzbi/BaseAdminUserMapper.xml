<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.BaseAdminUserMapper">
  <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.BaseAdminUser">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="sys_user_name" jdbcType="VARCHAR" property="sysUserName" />
    <result column="sys_user_pwd" jdbcType="VARCHAR" property="sysUserPwd" />
    <result column="role_id" jdbcType="INTEGER" property="roleId" />
    <result column="user_phone" jdbcType="VARCHAR" property="userPhone" />
    <result column="user_status" jdbcType="INTEGER" property="userStatus" />
    <result column="reg_time" jdbcType="VARCHAR" property="regTime" />
  </resultMap>

  <sql id="baseColumn">
    id,sys_user_name,sys_user_pwd,user_status,reg_time,user_phone,role_id
  </sql>


  <update id="updateUser">
    UPDATE base_admin_user
    <set>
      <if test="sysUserName != null">
        sys_user_name = #{sysUserName},
      </if>
      <if test="sysUserPwd != null">
        sys_user_pwd = #{sysUserPwd},
      </if>
      <if test="roleId != null">
        role_id = #{roleId},
      </if>
      <if test="userPhone != null">
        user_phone = #{userPhone}
      </if>
    </set>
    WHERE id = #{id}
  </update>


  <update id="updatePwd">
    UPDATE base_admin_user
    set sys_user_pwd = #{password}
    where sys_user_name = #{userName}
  </update>

  <update id="updateUserStatus">
    UPDATE base_admin_user
    SET user_status = #{status}
    WHERE id = #{id}
  </update>


  <select id="getUserByUserName" resultMap="BaseResultMap">
    SELECT <include refid="baseColumn"/>
    FROM base_admin_user
    WHERE sys_user_name = #{sysUserName}
    and user_status = 1
    <if test="id != null">
      and id != #{id}
    </if>
  </select>

  <select id="findByUserName" resultMap="BaseResultMap">
    SELECT <include refid="baseColumn"/>
    FROM base_admin_user
    WHERE sys_user_name = #{userName}
    and user_status = 1
  </select>

  <select id="getUserList" resultType="com.lenztech.bi.enterprise.dto.AdminUserDTO">
    SELECT id, sys_user_name AS sysUserName ,role_id AS roleId,
    (SELECT role_name FROM base_admin_role as r WHERE r.id = u.role_id) as roleName,
    user_phone AS userPhone,reg_time AS regTime,user_status AS userStatus
    FROM base_admin_user as u
    <where>
      <if test="sysUserName != null and sysUserName != '' ">
        and sys_user_name LIKE '%' #{sysUserName} '%'
      </if>
      <if test="userPhone != null and userPhone != '' ">
        and user_phone  LIKE '%' #{userPhone} '%'
      </if>
      <if test="startTime != null and startTime != '' ">
        and reg_time &gt;  #{startTime}
      </if>
      <if test="endTime != null and endTime != '' ">
        and reg_time &lt;  #{endTime}
      </if>
    </where>
  </select>

</mapper>