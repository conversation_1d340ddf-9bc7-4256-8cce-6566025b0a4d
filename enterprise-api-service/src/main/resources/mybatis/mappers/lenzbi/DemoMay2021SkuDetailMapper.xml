<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.DemoMay2021SkuDetailMapper">

    <select id="getDetailListByProductId" resultType="com.lenztech.bi.enterprise.entity.DemoMay2021SkuDetail">
        SELECT
               detail.response_id AS responseId,
               detail.facing_count AS facingCount,
               detail.standard_facing_count AS standardFacingCount,
               detail.if_wrong_price AS ifWrongPrice
        FROM
             demo_may2021_sku_detail detail
        LEFT JOIN
             demo_may2021_kpi kpi
                 ON detail.response_id = kpi.response_id
        WHERE
              kpi.create_time > #{nowTime,jdbcType=VARCHAR}
          AND detail.product_id = #{productId,jdbcType=INTEGER}
    </select>
    <select id="getTrendDetailListByProductId" resultType="com.lenztech.bi.enterprise.entity.DemoMay2021SkuDetail">
        SELECT
            detail.facing_count AS facingCount,
            kpi.create_time AS updateTime
        FROM
            demo_may2021_sku_detail detail
                LEFT JOIN
            demo_may2021_kpi kpi
            ON detail.response_id = kpi.response_id
        WHERE
            detail.product_id = #{productId,jdbcType=INTEGER}
        ORDER BY kpi.create_time DESC
        LIMIT 0,4
    </select>
</mapper>
