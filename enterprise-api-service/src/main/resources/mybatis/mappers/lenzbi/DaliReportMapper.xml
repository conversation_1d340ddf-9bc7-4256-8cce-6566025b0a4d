<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.DaliReportMapper">

	<select id="getImageUrlByScene" resultType="com.lenztech.bi.enterprise.entity.TDaliPoc">
		SELECT GROUP_CONCAT(DISTINCT image_url) imageUrl,scene FROM t_dali_poc where rid=#{responseId,jdbcType=VARCHAR} GROUP BY scene
	</select>

	<select id="getProductPriceInfoList" resultType="com.lenztech.bi.enterprise.dto.bnhd.ProductPriceInfo">
		SELECT sum(amount) totalAmount,GROUP_CONCAT(DISTINCT layer) currentUnit, product_name productName FROM t_dali_poc where rid=#{responseId,jdbcType=VARCHAR} AND scene=#{scene,jdbcType=VARCHAR} GROUP BY product_id
	</select>
</mapper>