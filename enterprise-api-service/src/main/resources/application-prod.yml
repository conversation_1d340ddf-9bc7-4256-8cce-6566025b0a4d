#logger:
#  path: /data/logs/bi-service
#eureka:
#  client:
#    serviceUrl:
#      defaultZone: ******************************************/eureka/
#spring:
#  datasource:
#    druid:
#      stat-view-servlet:
#        loginUsername: admin
#        loginPassword: ppzAdmin
#    dynamic:
#      druid:
#        filters: stat,wall
#        stat:
#          merge-sql: true
#          log-slow-sql: true
#          slow-sql-millis: 5000
#        useGlobalDataSourceStat: true
#      p6spy: true # 默认false,建议线上关闭。
#      primary: lenzbi #设置默认的数据源或者数据源组,默认值即为master
#      datasource:
#        # 数据源切换中间件，下划线会被默认认定为组管理，导致数据源切换不确定，切忌使用下划线配置命名
#        lenzbi:
#          driverClassName: com.mysql.jdbc.Driver
#          url: ************************************************************************************************************************************************************************
#          username: new_lenzt
#          password: Lenz66k@com
#          druid:
#            # 初始化大小，最小，最大
#            initialSize: 5
#            maxActive: 500
#            maxPoolPreparedStatementPerConnectionSize: 20
#            # 配置获取连接等待超时的时间
#            maxWait: 60000
#            # 配置一个连接在池中最小生存的时间，单位是毫秒
#            minEvictableIdleTimeMillis: 300000
#            minIdle: 5
#            # 打开PSCache，并且指定每个连接上PSCache的大小
#            poolPreparedStatements: true
#            testOnBorrow: true
#            testOnReturn: false
#            testWhileIdle: false
#            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
#            timeBetweenEvictionRunsMillis: 60000
#            validationQuery: SELECT 1 FROM DUAL
#        # 数据源切换中间件，下划线会被默认认定为组管理，导致数据源切换不确定，切忌使用下划线配置命名
#        lenzbi-pec:
#          driverClassName: com.mysql.jdbc.Driver
#          url: *********************************************************************************************************************************************************************
#          username: tongyi
#          password: 7bglkf9uSDkohQon
#          druid:
#            # 初始化大小，最小，最大
#            initialSize: 5
#            maxActive: 500
#            maxPoolPreparedStatementPerConnectionSize: 20
#            # 配置获取连接等待超时的时间
#            maxWait: 60000
#            # 配置一个连接在池中最小生存的时间，单位是毫秒
#            minEvictableIdleTimeMillis: 300000
#            minIdle: 5
#            # 打开PSCache，并且指定每个连接上PSCache的大小
#            poolPreparedStatements: true
#            testOnBorrow: true
#            testOnReturn: false
#            testWhileIdle: false
#            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
#            timeBetweenEvictionRunsMillis: 60000
#            validationQuery: SELECT 1 FROM DUAL
#        # 数据源切换中间件，下划线会被默认认定为组管理，导致数据源切换不确定，切忌使用下划线配置命名
#        bi-enterprise:
#          driverClassName: com.mysql.jdbc.Driver
#          url: *******************************************************************************************************************************************************************************
#          username: paipaizhuanbi
#          password: ppz@lenz*0&H100Dabe
#          druid:
#            # 初始化大小，最小，最大
#            initialSize: 5
#            maxActive: 500
#            maxPoolPreparedStatementPerConnectionSize: 20
#            # 配置获取连接等待超时的时间
#            maxWait: 60000
#            # 配置一个连接在池中最小生存的时间，单位是毫秒
#            minEvictableIdleTimeMillis: 300000
#            minIdle: 5
#            # 打开PSCache，并且指定每个连接上PSCache的大小
#            poolPreparedStatements: true
#            testOnBorrow: true
#            testOnReturn: false
#            testWhileIdle: false
#            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
#            timeBetweenEvictionRunsMillis: 60000
#            validationQuery: SELECT 1 FROM DUAL
#        # 数据源切换中间件，下划线会被默认认定为组管理，导致数据源切换不确定，切忌使用下划线配置命名
#        task:
#          driverClassName: com.mysql.jdbc.Driver
#          url: **********************************************************************************************************************************************************************
#          username: bi_service
#          password: OVW3hQGf0KhIa1KQ
#          druid:
#            # 初始化大小，最小，最大
#            initialSize: 5
#            maxActive: 500
#            maxPoolPreparedStatementPerConnectionSize: 20
#            # 配置获取连接等待超时的时间
#            maxWait: 60000
#            # 配置一个连接在池中最小生存的时间，单位是毫秒
#            minEvictableIdleTimeMillis: 300000
#            minIdle: 5
#            # 打开PSCache，并且指定每个连接上PSCache的大小
#            poolPreparedStatements: true
#            testOnBorrow: true
#            testOnReturn: false
#            testWhileIdle: false
#            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
#            timeBetweenEvictionRunsMillis: 60000
#            validationQuery: SELECT 1 FROM DUAL
#        # 数据源切换中间件，下划线会被默认认定为组管理，导致数据源切换不确定，切忌使用下划线配置命名
#        bi-task:
#          driverClassName: com.mysql.jdbc.Driver
#          url: **********************************************************************************************************************************************************
#          username: bi_service
#          password: T087y7d9U5Io1hjN
#          druid:
#            # 初始化大小，最小，最大
#            initialSize: 5
#            maxActive: 500
#            maxPoolPreparedStatementPerConnectionSize: 20
#            # 配置获取连接等待超时的时间
#            maxWait: 60000
#            # 配置一个连接在池中最小生存的时间，单位是毫秒
#            minEvictableIdleTimeMillis: 300000
#            minIdle: 5
#            # 打开PSCache，并且指定每个连接上PSCache的大小
#            poolPreparedStatements: true
#            testOnBorrow: true
#            testOnReturn: false
#            testWhileIdle: false
#            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
#            timeBetweenEvictionRunsMillis: 60000
#            validationQuery: SELECT 1 FROM DUAL
#node:
#  serviceUrl: https://node.ppznet.com
#mobile:
#  serviceUrl: http://mobile.ppznet.com
#bi:
#  serviceUrl: http://bi.ppznet.com
##识别明细
#h5:
#  recongnitionDetailServiceUrl: ${mobile.serviceUrl}/result/#/result?responseid={responseId}
#  appealServiceUrl: ${node.serviceUrl}/dist/#/Appeal?op=h5&taskId={taskId}&responseId={responseId}&account={account}
#  heapServiceUrl: ${node.serviceUrl}/dist/#/report?responseId={responseId}&hasSubReturn=1
##时间打点接口
#record:
#  server:
#    domain: https://img-ai-prod.ppznet.com
#    getRecordUrl: ${record.server.domain}/api/record/v1/responseTimeRecord/listResponseTimeRecord
##是否开启接口文档
#swagger:
#  enable: false
##MNS
#aliyun_mns_accessId: LTAI57Juq5v8XLfz
#aliyun_mns_accessKey: GhWm4TzNyzO7fu8kSPyX1aTv8YLIGK
#aliyun_mns_accountEndpoint: https://****************.mns.cn-beijing.aliyuncs.com/
#aliyun_mns_queueNameAppeal: PROD-APPEAL-FINISH-QUEUE
#aliyun_mns_syn_priority: 2
#aliyun_mns_asyn_priority: 4
#china_shop_task_id: 0f75746327e94cb1a8dd4b49828cf3ff