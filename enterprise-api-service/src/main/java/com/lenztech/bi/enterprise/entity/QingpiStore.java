package com.lenztech.bi.enterprise.entity;

import com.lenztech.bi.enterprise.entity.base.BaseEntity;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-06
 */
public class QingpiStore extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 大区（对应大区经理）
     */
    private String daqu;

    private String daquEn;

    /**
     * 二级办（对应区域经理）
     */
    private String erjiban;

    private String erjibanEn;

    private String storeCode;

    private String storeName;

    /**
     * 业代账号
     */
    private String phone;

    /**
     * 系统名称（考核规则判定）
     */
    private String systemName;

    /**
     * 门店类型（CVS门店不参与所有面位指标计算）
     */
    private String storeType;

    /**
     * 系统经理账号
     */
    private String systemPhone;

    /**
     * phone对应的人员姓名（变更比较频繁）
     */
    private String phoneName;

    public String getDaqu() {
        return daqu;
    }

    public void setDaqu(String daqu) {
        this.daqu = daqu;
    }
    public String getDaquEn() {
        return daquEn;
    }

    public void setDaquEn(String daquEn) {
        this.daquEn = daquEn;
    }
    public String getErjiban() {
        return erjiban;
    }

    public void setErjiban(String erjiban) {
        this.erjiban = erjiban;
    }
    public String getErjibanEn() {
        return erjibanEn;
    }

    public void setErjibanEn(String erjibanEn) {
        this.erjibanEn = erjibanEn;
    }
    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }
    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }
    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }
    public String getSystemPhone() {
        return systemPhone;
    }

    public void setSystemPhone(String systemPhone) {
        this.systemPhone = systemPhone;
    }
    public String getPhoneName() {
        return phoneName;
    }

    public void setPhoneName(String phoneName) {
        this.phoneName = phoneName;
    }

    @Override
    public String toString() {
        return "QingpiStore{" +
        "daqu=" + daqu +
        ", daquEn=" + daquEn +
        ", erjiban=" + erjiban +
        ", erjibanEn=" + erjibanEn +
        ", storeCode=" + storeCode +
        ", storeName=" + storeName +
        ", phone=" + phone +
        ", systemName=" + systemName +
        ", storeType=" + storeType +
        ", systemPhone=" + systemPhone +
        ", phoneName=" + phoneName +
        "}";
    }
}
