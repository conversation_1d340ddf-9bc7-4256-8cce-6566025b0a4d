package com.lenztech.bi.enterprise.dto.unileverapp;

import lombok.Data;

import java.util.List;

/**
 * @ClassName ApiImageResultDTO
 * @Description: TODO
 * <AUTHOR>
 * @Date 2022/1/10
 **/

@Data
public class ApiImageResultDTO {

    /**
     * 图片id
     */
    private String imageId;

    /**
     * 是否翻拍 1是 0否
     */
    private Integer remake;

    /**
     * Ai翻拍校验置信度分值
     */
    private Double remakeScore;

    /**
     * sku详情list
     */
    private List<ApiPatchResultDTO> patches;

    /**
     * 是否重复 1是 0否
     */
    private Integer repeat;

    /**
     * 重复组号
     */
    private Integer repeatGroup;

}
