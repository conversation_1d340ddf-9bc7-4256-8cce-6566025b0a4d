package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 同界拼接图
 *
 * <AUTHOR>
 * @since 2022-01-17 14:55:30
 */
@Data
@TableName("general_app_price")
public class GeneralAppPrice implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 自增id
    */
    private Integer id;
    /**
    * 答卷id
    */
    private String responseId;
    /**
    * 商品id
    */
    private String productId;

    /**
     * 价格
     */
    private String price;

}