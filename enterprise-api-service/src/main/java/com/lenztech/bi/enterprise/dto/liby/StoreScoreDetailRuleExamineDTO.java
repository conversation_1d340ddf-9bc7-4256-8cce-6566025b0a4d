package com.lenztech.bi.enterprise.dto.liby;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/14 14:16
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class StoreScoreDetailRuleExamineDTO {

    private Integer productId;
    /**
     * 考核项
     */
    private String examineItem;
    /**
     * 识别内容
     */
    private Long value;

}
