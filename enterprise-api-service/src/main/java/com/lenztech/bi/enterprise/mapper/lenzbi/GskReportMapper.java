package com.lenztech.bi.enterprise.mapper.lenzbi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.dto.gsk.GskBiReport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * gsk-bi指标Mapper
 *
 * <AUTHOR>
 * @date 2021-05-28 15:44:19
 */
@Mapper
@DS("lenzbi")
public interface GskReportMapper {

    /**
     * 查询指标集合
     *
     * @param responseId 答卷Id
     * @return List<GskBiReport>
     */
    List<GskBiReport> getBiTargetList(@Param("responseId") String responseId);

}
