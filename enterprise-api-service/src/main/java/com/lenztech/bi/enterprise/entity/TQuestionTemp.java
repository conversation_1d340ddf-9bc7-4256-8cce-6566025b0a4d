package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> lv
 */
@Data
public class TQuestionTemp {

    /**
     * 操作id
     */
    @TableId("Id")
    private String Id;

    private String taskid;
    private String uid;
    private String title;
    private String type;
    private String responseId;
    // 大题名
    private String loopsku;
    // 小题名
    @TableField("optionValue")
    private String optionValue;
}
