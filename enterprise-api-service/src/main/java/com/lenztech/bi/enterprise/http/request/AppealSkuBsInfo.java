package com.lenztech.bi.enterprise.http.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel("申诉SKU信息")
public class AppealSkuBsInfo implements Serializable {

    @ApiModelProperty("分销申诉：0有分销；1无分销")
    private Integer exist;

    @ApiModelProperty("面位数")
    private Double facing;

    @ApiModelProperty("sku代码")
    private Integer productId;

    public Integer getExist() {
        return exist;
    }

    public void setExist(Integer exist) {
        this.exist = exist;
    }

    public Double getFacing() {
        return facing;
    }

    public void setFacing(Double facing) {
        this.facing = facing;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }
}
