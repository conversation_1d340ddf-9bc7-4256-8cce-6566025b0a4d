package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.dto.gsk.GskBiReport;
import com.lenztech.bi.enterprise.dto.gsk.GskBiTargetResp;
import com.lenztech.bi.enterprise.mapper.lenzbi.GskReportMapper;
import com.lenztech.bi.enterprise.service.GskReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * gsk-bi指标ServiceImpl
 *
 * <AUTHOR>
 * @date 2021-05-28 15:44:19
 */
@Slf4j
@Service
public class GskReportServiceImpl implements GskReportService {

    @Autowired
    private GskReportMapper gskReportMapper;

    /**
     * 查询指标集合
     *
     * @param responseId
     * @return GskBiTargetResp
     */
    @Override
    public GskBiTargetResp getBiTargetList(String responseId) {
        log.info("responseId:" + responseId);
        GskBiTargetResp gskBiTargetResp = new GskBiTargetResp();
        try {
            List<GskBiReport> gskBiReportList = gskReportMapper.getBiTargetList(responseId);
            gskBiTargetResp.setResponseId(responseId);
            gskBiTargetResp.setTargetList(gskBiReportList);
        } catch (Exception e) {
            log.error("/getBiTargetList========", e);
        }
        return gskBiTargetResp;
    }

}
