package com.lenztech.bi.enterprise.dto.pg;

import com.lenztech.bi.enterprise.entity.PgPocBrandFacingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2019/11/28 14:41
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class PgPocBrandFacingListDTO {

    private List<PgPocBrandFacingEntity> pgPocBrandFacingEntities;

}
