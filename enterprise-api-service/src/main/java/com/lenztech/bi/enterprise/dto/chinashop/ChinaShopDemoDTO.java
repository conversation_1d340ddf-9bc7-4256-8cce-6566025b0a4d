package com.lenztech.bi.enterprise.dto.chinashop;

import com.lenztech.bi.enterprise.dto.car.LineChartDTO;
import com.lenztech.bi.enterprise.dto.car.PeopleNumDTO;
import com.lenztech.bi.enterprise.dto.car.PostCodeDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/9/2 9:35
 **/

@Data
public class ChinaShopDemoDTO {

    private List<ChinaShopDetail> detailList;

    private String stitchPictureUrl;

    private InstrumentResult nowResult;

    private InstrumentResult oldResult;

    private List<HistoryRecord> recordList;

    private List<StockProduct> stockProductList;

    private List<ShelfOccupyRate> rateList;

    private List<WrongPrice> wrongPriceList;

    /**
     * 报警记录
     */
    private AlarmRecord alarmRecord;

}
