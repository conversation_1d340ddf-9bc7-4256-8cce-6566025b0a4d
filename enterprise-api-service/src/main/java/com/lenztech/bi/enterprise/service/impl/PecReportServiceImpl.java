package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.comon.DataSourceEnums;
import com.lenztech.bi.enterprise.dto.junlebao.OriginalRepeatBiResultDTO;
import com.lenztech.bi.enterprise.dto.junlebao.RepeatBiResultResp;
import com.lenztech.bi.enterprise.dto.pec.PecBiTarget;
import com.lenztech.bi.enterprise.dto.pec.PecBiTargetResp;
import com.lenztech.bi.enterprise.entity.TTongyiDuplicate;
import com.lenztech.bi.enterprise.mapper.PecReportMapper;
import com.lenztech.bi.enterprise.mapper.TTongyiDuplicateMapper;
import com.lenztech.bi.enterprise.service.PecReportService;
import com.lenztech.bi.enterprise.service.ShardingService;
import com.lenztech.bi.enterprise.utils.DateUtil;
import com.trax.lenz.common.core.id.SnowFlakeFactory;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Month;
import java.time.YearMonth;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 统一bi指标ServiceImpl
 *
 * <AUTHOR>
 * @date 2019-10-17 15:44:19
 */
@Service
@RefreshScope
public class PecReportServiceImpl implements PecReportService {

    public static final Logger logger = LoggerFactory.getLogger(PecReportServiceImpl.class);

    @Autowired
    private PecReportMapper pecReportMapper;

    @Autowired
    private TTongyiDuplicateMapper ttongyiDuplicateMapper;

    @Autowired
    private ShardingService shardingService;

    @Autowired
    private SnowFlakeFactory snowFlakeFactory;

    @Value("${sharding.pecEffectiveDay:''}")
    private String effectiveDay;

    /**
     * 查询指标集合
     *
     * @param responseId
     * @return JmlBiTargetResp
     */
    @Override
    public PecBiTargetResp getBiTargetList(String responseId) {
        PecBiTargetResp pecBiTargetResp = new PecBiTargetResp();
        try {
            logger.info("responseId:" + responseId);
            String shardingMonth = shardingService.getShardingKey(responseId, effectiveDay);
            String month = snowFlakeFactory.getDateMoth(responseId);
            String todayMonth = DateUtil.convert2String(new Date(), DateUtil.DTFormat.yyyyMM.getFormat());
            if (isSharding(Integer.valueOf(month), Integer.valueOf(todayMonth))) {
                logger.info("统一项目查询历史答卷！切换数据源为polar-db！responseId={}, month={}", responseId, month);
                DynamicDataSourceContextHolder.push(DataSourceEnums.LENZ_BI_POLAR.getCode());
            }
            List<PecBiTarget> pecBiTargetList = pecReportMapper.getBiTargetList(responseId, shardingMonth);
            pecBiTargetResp.setResponseId(responseId);
            pecBiTargetResp.setTargetList(pecBiTargetList);
        } catch (Exception e) {
            logger.error("查询统一项目bi结果异常！", e);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
        return pecBiTargetResp;
    }

    public static void main(String[] args) {
        System.out.println(isSharding(202311, 202404));
        System.out.println(isSharding(202312, 202404));
        System.out.println(isSharding(202401, 202404));
        System.out.println(isSharding(202402, 202404));
        System.out.println(isSharding(202403, 202404));
        System.out.println(isSharding(202404, 202404));
        System.out.println(isSharding(202405, 202404));
    }

    private static boolean isSharding(Integer month1, Integer month2){
        return !month1.equals(month2) && (month2 - month1) != 89 && (month2 - month1) > 1;
    }

    /**
     * 获取统一查重结果
     * @param responseId
     * @return
     */
    @Override
    public RepeatBiResultResp getPecRepeatResult(String responseId) {
        RepeatBiResultResp resp = new RepeatBiResultResp();

        LambdaQueryWrapper<TTongyiDuplicate> pecWrapper = new LambdaQueryWrapper<>();
        pecWrapper.eq(TTongyiDuplicate::getResponseId, responseId);
        List<TTongyiDuplicate> tTongyiDuplicateList = ttongyiDuplicateMapper.selectList(pecWrapper);

        if (CollectionUtils.isEmpty(tTongyiDuplicateList)) {
            return resp;
        }

        List<TTongyiDuplicate> list = tTongyiDuplicateList.stream().filter(t -> StringUtils.isNotBlank(t.getGroupId())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(list)) {
            return resp;
        }

        List<OriginalRepeatBiResultDTO> repeatResultList = Lists.newArrayList();
        for (TTongyiDuplicate tJlbDuplicatePoc : list) {
            OriginalRepeatBiResultDTO dto = new OriginalRepeatBiResultDTO();
            dto.setImageId(tJlbDuplicatePoc.getImageId());
            dto.setGroupId(tJlbDuplicatePoc.getGroupId());
            dto.setUrl(tJlbDuplicatePoc.getImageUrl());
            dto.setScore(String.valueOf(tJlbDuplicatePoc.getScore()));
            repeatResultList.add(dto);
        }

        resp.setRepeatResultList(repeatResultList);
        return resp;
    }

}
