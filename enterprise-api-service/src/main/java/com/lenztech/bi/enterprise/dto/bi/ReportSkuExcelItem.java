package com.lenztech.bi.enterprise.dto.bi;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description:sku信息
 * @Author: <PERSON><PERSON><PERSON>e
 * @Date: 3/10/20 PM3:15
 */
@Data
public class ReportSkuExcelItem implements Serializable {

    /**
     * 任务ID
     */
    @Excel(name = "taskid", orderNum = "0", width = 32)
    private String taskId;

    /**
     * sku id（tree表id）
     */
    @Excel(name = "ID", orderNum = "1", width = 32)
    private Integer treeId;

    /**
     * sku 名称
     */
    @Excel(name = "识别目标", orderNum = "1", width = 32)
    private String name;

    /**
     * 品牌id
     */
    @Excel(name = "品牌", orderNum = "3", width = 8)
    private Integer bandId;

    /**
     * 品类id
     */
    @Excel(name = "品类", orderNum = "3", width = 8)
    private Integer categoryId;

    /**
     * 本品/竞品类型
     */
    @Excel(name = "本品/竞品", orderNum = "3", width = 16)
    private Integer type;

}
