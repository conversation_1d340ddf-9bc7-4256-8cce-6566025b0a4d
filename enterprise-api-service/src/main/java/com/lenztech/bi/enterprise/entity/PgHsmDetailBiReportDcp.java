package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public class PgHsmDetailBiReportDcp extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 操作id
     */
    private String taskId;

    private String responseId;

    @TableField("addressIDnum")
    private String addressidnum;

    private String category;

    private String month;

    private String date;

    /**
     * 指标名称
     */
    private String kpiName;

    /**
     * 指标值
     */
    private String kpiValue;

    private LocalDateTime createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public String getAddressidnum() {
        return addressidnum;
    }

    public void setAddressidnum(String addressidnum) {
        this.addressidnum = addressidnum;
    }
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }
    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }
    public String getKpiName() {
        return kpiName;
    }

    public void setKpiName(String kpiName) {
        this.kpiName = kpiName;
    }
    public String getKpiValue() {
        return kpiValue;
    }

    public void setKpiValue(String kpiValue) {
        this.kpiValue = kpiValue;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "PgHsmDetailBiReportDcp{" +
            "id=" + id +
            ", taskId=" + taskId +
            ", responseId=" + responseId +
            ", addressidnum=" + addressidnum +
            ", category=" + category +
            ", month=" + month +
            ", date=" + date +
            ", kpiName=" + kpiName +
            ", kpiValue=" + kpiValue +
            ", createTime=" + createTime +
        "}";
    }
}
