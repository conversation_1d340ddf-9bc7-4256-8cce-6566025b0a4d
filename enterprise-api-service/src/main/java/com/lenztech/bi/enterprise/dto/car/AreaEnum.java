package com.lenztech.bi.enterprise.dto.car;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/9/2 10:20
 **/
public enum AreaEnum {


    EST("202East", "东区"),

    WEST("204West", "西区"),

    SOUTH("203South", "南区"),

    NORTH("201North", "北区"),
    ;

    private String nameEn;
    private String nameCn;

    AreaEnum(String nameEn, String nameCn) {
        this.nameEn = nameEn;
        this.nameCn = nameCn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public String getNameCn() {
        return nameCn;
    }

    public static String getNameCnByNameEn(String nameEn) {
        for (AreaEnum areaEnum : AreaEnum.values()) {
            if (areaEnum.getNameEn().equals(nameEn)) {
                return areaEnum.nameCn;
            }
        }
        return null;
    }
}
