package com.lenztech.bi.enterprise.dto.car;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/9/1 11:22
 **/

@Data
public class CarData {
    /**
     * 品牌
     */
    @Excel(name = "brand")
    private String brand;
    /**
     * 车型
     */
    @Excel(name = "model")
    private String model;
    /**
     * 地区
     */
    @Excel(name = "area")
    private String area;
    /**
     * 城市
     */
    @Excel(name = "city")
    private String city;
    /**
     * 贴码数量
     */
    @Excel(name = "postCodeNum")
    private String postCodeNum;
    /**
     * 扫码数量
     */
    @Excel(name = "scanCodeNum")
    private String scanCodeNum;
    /**
     * 目标数量
     */
    @Excel(name = "targetNum")
    private Integer targetNum;
    /**
     * 问卷成功
     */
    @Excel(name = "successResponseNum")
    private Integer successResponseNum;
    /**
     * 转化方式
     */
    @Excel(name = "translateWay")
    private String translateWay;
    /**
     * 方式占比
     */
    @Excel(name = "wayRate")
    private String wayRate;
}
