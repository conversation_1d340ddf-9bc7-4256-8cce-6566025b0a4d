package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.dto.cloudPatrolShop.StoreImageListResp;
import com.lenztech.bi.enterprise.dto.cloudPatrolShop.StoreIndicatorResp;
import com.lenztech.bi.enterprise.dto.cloudPatrolShop.StoreListResp;

import java.util.List;

public interface CloudPatrolShopService {

    /**
     * 查询门店列表
     *
     * <AUTHOR>
     * @date 2023/1/3 10:26
     */
    List<StoreListResp> getStoreList(String provinceName, String cityName, String address);

    /**
     * 查询门店题目列表
     *
     * <AUTHOR>
     * @date 2023/1/3 15:35
     */
    List<StoreImageListResp> getStoreImageList(String storeId);

    /**
     * 获取门店指标（必分销率&sos）
     *
     * <AUTHOR>
     * @date 2023/1/3 18:07
     */
    List<StoreIndicatorResp> getStoreIndicator(String storeId);
}
