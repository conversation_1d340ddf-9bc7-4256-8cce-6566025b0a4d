package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TBfdScene implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String responseId;

    /**
     * 图片Id
     */
    private String imageId;

    private String sceneId;

    /**
     * 场景编码
     */
    private String name;

    /**
     * 场景原始信息
     */
    private String originalName;

    /**
     * 场景编码
     */
    private Float score;

    /**
     * 坐标
     */
    private String coordinate;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * UUID
     */
    private String uuid;

}
