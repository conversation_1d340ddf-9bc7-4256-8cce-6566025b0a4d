package com.lenztech.bi.enterprise.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-14
 */

@Data
public class DemoMay2021Kpi extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String questionId;

    private String responseId;

    /**
     * 答卷创建时间
     */
    private LocalDateTime createTime;

    /**
     * 货架缺货率
     */
    private BigDecimal shortageRatio;

    /**
     * 缺货数
     */
    private Integer shortageSkuCount;

    /**
     * 价格错误数
     */
    private Integer wrongPriceSkuCount;

    /**
     * 价格错误率
     */
    private BigDecimal wrongPriceSkuRatio;

    /**
     * 低排面SKU数
     */
    private Integer lowFacingSkuCount;

    /**
     * 潜在销售损失
     */
    private BigDecimal potentialLoss;

    /**
     * 拼接图链接
     */
    private String stitchUrl;

    private LocalDateTime updateTime;

    private BigDecimal pskuShortageRatio;

    private Integer wrongPricePskuCount;

    private Integer lowFacingPskuCount;


}
