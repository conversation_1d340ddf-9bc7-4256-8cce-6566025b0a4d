//package com.lenztech.bi.enterprise.service;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.fasterxml.jackson.core.type.TypeReference;
//import com.google.common.base.Strings;
//import com.lenztech.bi.enterprise.comon.*;
//import com.lenztech.bi.enterprise.dto.BiReportSkuDTO;
//import com.lenztech.bi.enterprise.dto.IrResponseData;
//import com.lenztech.bi.enterprise.dto.bi.BiReportDetailReq;
//import com.lenztech.bi.enterprise.dto.exectime.IrGetOperateTimeListReq;
//import com.lenztech.bi.enterprise.dto.exectime.IrGetOperateTimeListResp;
//import com.lenztech.bi.enterprise.dto.exectime.IrResponseOperateDetail;
//import com.lenztech.bi.enterprise.dto.tsingtao.TsingtaoProduct;
//import com.lenztech.bi.enterprise.dto.tsingtao.TsingtaoProductKpi;
//import com.lenztech.bi.enterprise.entity.*;
//import com.lenztech.bi.enterprise.mapper.BiStoreDetailReportMapper;
//import com.lenztech.bi.enterprise.mapper.BiStoreReportMapper;
//import com.lenztech.bi.enterprise.mapper.lenzbi.QingpiStoreMapper;
//import com.lenztech.bi.enterprise.mapper.lenzbi.QingpiTarProductListMapper;
//import com.lenztech.bi.enterprise.mapper.task.QingPiKpiMapper;
//import com.lenztech.bi.enterprise.mapper.task.TResponseMapper;
//import com.lenztech.bi.enterprise.mapper.task.TTasklaunchMapper;
//import com.lenztech.bi.enterprise.mns.AppealDataFinishListener;
//import com.lenztech.bi.enterprise.utils.*;
//import org.apache.commons.collections.CollectionUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//
//import java.math.BigDecimal;
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * @Description:BI报表详情（sku）
// * @Author: zhangjie
// * @Date: 4/02/20 PM3:15
// */
//@Service
//public class BiComputeDataService {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(BiComputeDataService.class);
//
//    @Autowired
//    private TResponseMapper responseMapper;
//
//    @Autowired
//    private TTasklaunchMapper tasklaunchMapper;
//
//    @Autowired
//    private QingpiStoreMapper qingpiStoreMapper;
//
//    @Autowired
//    private BiStoreReportMapper biStoreReportMapper;
//
//    @Autowired
//    private BiStoreDetailReportMapper biStoreDetailReportMapper;
//
//    @Autowired
//    private QingPiKpiMapper qingPiKpiMapper;
//
//    @Autowired
//    private BiReportSkuService biReportSkuService;
//
//    @Value("${record.server.getRecordUrl}")
//    private String getRecordUrl;
//
//    @Autowired
//    private AppealDataFinishListener appealDataFinishListener;
//
//    @Autowired
//    private QingpiTarProductListMapper qingpiTarProductListMapper;
//
//    /**
//     * 保留位数
//     */
//    private final int roundScale = 4;
//
//    public void computeQingPiHistory(String taskId) {
//        List<String> ridList = responseMapper.selectRidByTaskId(taskId);
//        if (CollectionUtils.isNotEmpty(ridList)){
//            for (String rid : ridList){
//
//                LambdaQueryWrapper<BiStoreDetailReport> reportLambdaQueryWrapper = new LambdaQueryWrapper<>();
//                reportLambdaQueryWrapper.eq(BiStoreDetailReport::getResponseId, rid);
//
//                List<BiStoreDetailReport> biStoreDetailReportList = biStoreDetailReportMapper.selectList(reportLambdaQueryWrapper);
//                if (CollectionUtils.isEmpty(biStoreDetailReportList)){
//                    BiReportDetailReq reportDetailReq = new BiReportDetailReq();
//                    reportDetailReq.setResponseId(rid);
//
//                    appealDataFinishListener.sendMessageMns(JsonUtil.toJsonString(reportDetailReq));
//                }
//                LOGGER.info("=================rid:{}", rid);
//            }
//        }
//    }
//
//    public int answerTime(String responseId) {
//        LambdaQueryWrapper<TResponse> responseLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        responseLambdaQueryWrapper.eq(TResponse::getId, responseId);
//        TResponse tResponse = responseMapper.selectOne(responseLambdaQueryWrapper);
//
//        return getOperateTime(tResponse);
//    }
//    /**
//     * 通过rid计算bi中间表数据
//     *
//     * @param responseId
//     */
//    public void computeQingPiDetail(String responseId) {
//
//        LOGGER.info("【计算青啤中间表数据】responseId：{}", responseId);
//
//        BiStoreReport biStoreReport = new BiStoreReport();
//        //清除指定答卷历史数据
//        clearHistoryData(responseId);
//
//        LambdaQueryWrapper<TResponse> responseLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        responseLambdaQueryWrapper.eq(TResponse::getId, responseId);
//        TResponse tResponse = responseMapper.selectOne(responseLambdaQueryWrapper);
//
//        LambdaQueryWrapper<TTasklaunch> tasklaunchLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        tasklaunchLambdaQueryWrapper.eq(TTasklaunch::getTaskid, tResponse.getTaskid());
//        TTasklaunch tasklaunch = tasklaunchMapper.selectOne(tasklaunchLambdaQueryWrapper);
//        //门店编码
//        String addressIdNum = tasklaunch.getAddressIDnum();
//
//        //青啤门店信息
//        LambdaQueryWrapper<QingpiStore> qingpiStoreLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        qingpiStoreLambdaQueryWrapper.eq(QingpiStore::getStoreCode, addressIdNum);
//        QingpiStore qingpiStore = qingpiStoreMapper.selectOne(qingpiStoreLambdaQueryWrapper);
//
//        //任务id
//        String taskId = tResponse.getTaskidOwner();
//        //账号
//        String account = tResponse.getPhone();
//
//        if (qingpiStore == null){
//            return;
//        }
//        // 系统名称
//        String systemName = qingpiStore.getSystemName();
//        // 大区
//        String daQu = qingpiStore.getDaqu();
//
//        //基本信息设置
//        setStoreReportBasic(biStoreReport, tResponse, tasklaunch);
//
//        // 需考核的集合
//        List<TsingtaoProductKpi> tsingtaoProductKpiList = qingpiTarProductListMapper.getQingpiProductList(daQu, systemName);
//
//        // 分销集合
//        List<TsingtaoProduct> distList = qingPiKpiMapper.getProductDistList(responseId, daQu, systemName);
//
//        // 计算分销率
//        setDistRate(biStoreReport, tsingtaoProductKpiList, distList);
//
//        // 面位集合
//        List<TsingtaoProduct> facingList = qingPiKpiMapper.getProductFacingList(responseId);
//
//        // 计算kpi
//        setFacing(biStoreReport, facingList);
//
//        // 设置操作时间
//        biStoreReport.setOperatingTime(getOperateTime(tResponse));
//
//        //存储sku信息
//        saveStoreReportSkuDetail(taskId, distList, facingList);
//
//        //存储kpi信息
//        biStoreReportMapper.insert(biStoreReport);
//
//    }
//
//    /**
//     * 计算分销率、缺货率
//     *
//     * @param biStoreReport
//     * @param tsingtaoProductKpiList
//     * @param existProductList
//     */
//    private void setDistRate(BiStoreReport biStoreReport, List<TsingtaoProductKpi> tsingtaoProductKpiList, List<TsingtaoProduct> existProductList){
//        int groupTotalNum = 0;
//        int distGroupNum = 0;
//        if (CollectionUtils.isNotEmpty(tsingtaoProductKpiList)) {
//            // 按productId分组
//            Map<String, Integer> numMap = new HashMap<>();
//            Map<String, List<TsingtaoProductKpi>> map = new HashMap<>();
//            for (TsingtaoProductKpi tsingtaoProduct : tsingtaoProductKpiList) {
//                String productGroup = tsingtaoProduct.getProductGroup();
//                if (map.containsKey(productGroup)) {
//                    List<TsingtaoProductKpi> list = map.get(productGroup);
//                    list.add(tsingtaoProduct);
//                } else {
//                    List<TsingtaoProductKpi> list = new ArrayList<>();
//                    list.add(tsingtaoProduct);
//                    map.put(productGroup, list);
//                }
//                numMap.put(productGroup, tsingtaoProduct.getTarDistNum());
//            }
//            groupTotalNum = map.size();
//
//            // 循环map
//            for (String productGroup : map.keySet()) {
//                int i = 0;
//                List<TsingtaoProductKpi> list = map.get(productGroup);
//                for (TsingtaoProductKpi product : list) {
//                    for (TsingtaoProduct tsingtaoProduct : existProductList){
//                        if (product.getMergeId().equals(tsingtaoProduct.getMergeId())) {
//                            if ("1".equals(tsingtaoProduct.getExist())){
//                                i++;
//                            }
//                            tsingtaoProduct.setProductType(BiDataProductTypeEnum.MUST_PRODUCT.getValue().toString());
//                            break;
//                        }
//                    }
//                }
//                Integer num = numMap.get(productGroup);
//                if (i >= num) {
//                    distGroupNum++;
//                }
//            }
//        }
//
//        for (TsingtaoProduct tsingtaoProduct : existProductList){
//            if (StringUtil.isNotBlank(tsingtaoProduct.getProductType())){
//                continue;
//            }
//            // 竞品
//            if ("0".equals(tsingtaoProduct.getTsingtao())){
//                tsingtaoProduct.setProductType(BiDataProductTypeEnum.COMP_PRODUCT.getValue().toString());
//            } else {
//                tsingtaoProduct.setProductType(BiDataProductTypeEnum.SELF_PRODUCT.getValue().toString());
//            }
//        }
//
//        // 分销率
//        double distRate = distRate(distGroupNum, groupTotalNum);
//        biStoreReport.setDistRate(Arith.doubleToBigDecimal(distRate));
//
//        // 缺货率= 1 - 分销率
//        double outOfStockRate = Arith.sub(1, biStoreReport.getDistRate().doubleValue());
//        biStoreReport.setOutOfStockRate(Arith.doubleToBigDecimal(outOfStockRate));
//    }
//
//    /**
//     * 清除指定答卷历史数据
//     * @param responseId 答卷id
//     */
//    private void clearHistoryData(String responseId){
//        LambdaQueryWrapper<BiStoreReport> biStoreReportLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        biStoreReportLambdaQueryWrapper.eq(BiStoreReport::getResponseId, responseId);
//        biStoreReportMapper.delete(biStoreReportLambdaQueryWrapper);
//
//        LambdaQueryWrapper<BiStoreDetailReport> detailReportLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        detailReportLambdaQueryWrapper.eq(BiStoreDetailReport::getResponseId, responseId);
//        biStoreDetailReportMapper.delete(detailReportLambdaQueryWrapper);
//    }
//
//    /**
//     * 计算答卷操作时间
//     *
//     * @param response 答卷
//     */
//    private int getOperateTime(TResponse response) {
//        int operateTime = 0;
//
//        String dateFormat = DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat();
//        SimpleDateFormat simpleDateFormat = DateUtil.getSimpleDateFormat(dateFormat);
//
//        IrGetOperateTimeListReq operateTimeListReq = new IrGetOperateTimeListReq();
//        operateTimeListReq.setResponseId(response.getId());
//        operateTimeListReq.setStartTime(simpleDateFormat.format(response.getStartTime()));
//
//        String result = HttpConnectionUtils.post(getRecordUrl, JsonUtil.toJsonString(operateTimeListReq));
//        if (Strings.isNullOrEmpty(result)){
//            return operateTime;
//        }
//        IrResponseData<IrGetOperateTimeListResp> irResponseData = JsonUtil.parseObject(result, new TypeReference<IrResponseData<IrGetOperateTimeListResp>>() {
//        });
//        //答卷的操作记录列表
//        IrGetOperateTimeListResp operateTimeListResp = irResponseData.getData();
//        if (operateTimeListResp != null) {
//            List<IrResponseOperateDetail> operateArray = operateTimeListResp.getOperateArray();
//            if (CollectionUtils.isNotEmpty(operateArray)) {
//                for (int i = 0; i < operateArray.size(); i = i + 2) {
//                    int j = i + 1;
//                    IrResponseOperateDetail startOp = operateArray.get(i);
//                    if (j < operateArray.size()) {
//                        IrResponseOperateDetail quitOp = operateArray.get(j);
//                        if (ResponseOperateTypeEnum.START_ANSWER.getValue().equals(startOp.getOperateType()) && ResponseOperateTypeEnum.QUIT_ANSWER.getValue().equals(quitOp.getOperateType())){
//                            operateTime += Math.abs(DateUtil.diffSecond(DateUtil.convert2Date(startOp.getOperateTime(),dateFormat), DateUtil.convert2Date(quitOp.getOperateTime(), dateFormat)));
//                        }
//                    }
//                }
//            }
//        }
//        return operateTime;
//    }
//
//    /**
//     * 计算货架占比
//     *
//     * @param facingList 面位集合
//     */
//    private void setFacing(BiStoreReport biStoreReport, List<TsingtaoProduct> facingList) {
//        //本品面位
//        double selfProductFacing = 0d;
//        //竞品面位
//        double competingProductFacing = 0d;
//
//        for (TsingtaoProduct tsingtaoProduct : facingList){
//            String productType = tsingtaoProduct.getTsingtao();
//            if ("0".equals(productType)){
//                competingProductFacing = competingProductFacing + tsingtaoProduct.getFacingCount();
//            } else if("-1".equals(productType) || "-2".equals(productType)){
//                selfProductFacing = selfProductFacing + tsingtaoProduct.getFacingCount();
//            }
//        }
//
//        // 总面位
//        double facingAllCategory = selfProductFacing + competingProductFacing;
//
//        //本品货架占比
//        double productSos = productSos(selfProductFacing, facingAllCategory);
//
//        //竞品货架占比
//        double competingProductSos = competingProductSos(competingProductFacing, facingAllCategory);
//
//        biStoreReport.setProductSos(Arith.doubleToBigDecimal(productSos));
//        biStoreReport.setCompetingProductSos(Arith.doubleToBigDecimal(competingProductSos));
//    }
//
//    /**
//     * 本品货架占比--》本品sku面位数/全品类sku面位数
//     *
//     * @param selfProductFacing 本品面位
//     * @param facingAll         全品类面位
//     * @return
//     */
//    private double productSos(double selfProductFacing, double facingAll) {
//        double productSos = 0d;
//
//        if (facingAll > 0) {
//            productSos = Arith.div(selfProductFacing, facingAll);
//        }
//        return Arith.round(productSos, roundScale);
//    }
//
//    /**
//     * 竞品货架占比--》竞品sku面位数/全品类sku面位数
//     *
//     * @param competingProductFacing 竞品面位
//     * @param facingAll              全品类面位
//     * @return
//     */
//    private double competingProductSos(double competingProductFacing, double facingAll) {
//        double competingProductSos = 0d;
//
//        if (facingAll > 0) {
//            competingProductSos = Arith.div(competingProductFacing, facingAll);
//        }
//        return Arith.round(competingProductSos, roundScale);
//    }
//
//    /**
//     * 必备分销率 ---》必备本品sku有的sku个数 / 必备本品sku的sku个数
//     *
//     * @param mustHaveExistNum 必备本品sku有的sku个数
//     * @param mustHaveNum      必备本品sku的sku个数
//     * @return double
//     */
//    private double distRate(int mustHaveExistNum, int mustHaveNum) {
//        double distRate = 0d;
//
//        if (mustHaveNum > 0) {
//            distRate = Arith.div(mustHaveExistNum, mustHaveNum);
//        }
//        return Arith.round(distRate, roundScale);
//    }
//
//    /**
//     * 保存报表基本信息
//     *
//     * @param response 答卷
//     */
//    private void setStoreReportBasic(BiStoreReport biStoreReport, TResponse response, TTasklaunch tasklaunch) {
//        biStoreReport.setTaskId(response.getTaskidOwner());
//        biStoreReport.setResponseId(response.getId());
//        biStoreReport.setStoreCode(tasklaunch.getAddressIDnum());
//        biStoreReport.setStoreName(tasklaunch.getReallyAddress());
//
//        //进店开始结束时间
//        biStoreReport.setEnterTime(DateUtil.asLocalDateTime(response.getStartTime()));
//        biStoreReport.setLeaveTime(DateUtil.asLocalDateTime(response.getEndTime()));
//
//        //操作时间
//        biStoreReport.setOperatingTime(0);
//
//    }
//
//    /**
//     * 存储报表sku信息
//     *
//     * @param taskId 任务id
//     * @param distList 分销
//     * @param facingList 面位
//     */
//    private void saveStoreReportSkuDetail(String taskId, List<TsingtaoProduct> distList, List<TsingtaoProduct> facingList) {
//
//        //bi的类型和产品定义的类型映射
//        Map<Integer, Integer> biProductTypeUiTypeMap = new HashMap<>();
//        //bi查询的类型0 必备本品/1非必备本品/2竞品
//        biProductTypeUiTypeMap.put(BiDataProductTypeEnum.MUST_PRODUCT.getValue(), BiReportSkuTypeEnum.MUST_PRODUCT.getValue());
//        biProductTypeUiTypeMap.put(BiDataProductTypeEnum.SELF_PRODUCT.getValue(), BiReportSkuTypeEnum.SELF_PRODUCT.getValue());
//        biProductTypeUiTypeMap.put(BiDataProductTypeEnum.COMP_PRODUCT.getValue(), BiReportSkuTypeEnum.COMP_PRODUCT.getValue());
//
//        Map<Integer, BiReportSkuDTO> skuIdSkuMap = biReportSkuService.skuIdSkuMap(taskId);
//        try {
//            // 产品面位
//            Map<String, BigDecimal> productIdFacingMap = new HashMap<>();
//            if (CollectionUtils.isNotEmpty(facingList)) {
//                for (TsingtaoProduct productFacing : facingList) {
//                    productIdFacingMap.put(productFacing.getMergeId(), Arith.doubleToBigDecimal(productFacing.getFacingCount()));
//                }
//            }
//
//            List<BiStoreDetailReport> storeDetailReportList = new ArrayList<>();
//            //以分销记录为准
//            if (CollectionUtils.isNotEmpty(distList)) {
//                for (TsingtaoProduct productExist : distList) {
//                    BiStoreDetailReport biStoreDetailReport = new BiStoreDetailReport();
//                    biStoreDetailReport.setProductId(Integer.valueOf(productExist.getMergeId()));
//                    biStoreDetailReport.setIsExist(Integer.valueOf(productExist.getExist()));
//                    biStoreDetailReport.setProductName(productExist.getProductName());
//                    biStoreDetailReport.setResponseId(productExist.getResponseId());
//                    if (productIdFacingMap.containsKey(productExist.getMergeId())){
//                        biStoreDetailReport.setFacing(productIdFacingMap.get(productExist.getMergeId()));
//                    } else {
//                        biStoreDetailReport.setFacing(BigDecimal.ZERO);
//                    }
//                    //产品类型
//                    biStoreDetailReport.setProductCheckType(biProductTypeUiTypeMap.get(Integer.valueOf(productExist.getProductType())));
//                    BiReportSkuDTO biReportSkuDTO = skuIdSkuMap.get(Integer.valueOf(productExist.getMergeId()));
//                    if (biReportSkuDTO != null) {
//                        biStoreDetailReport.setBrand(biReportSkuDTO.getBrandName());
//                        biStoreDetailReport.setCategory(biReportSkuDTO.getCategoryName());
//                    }
//                    storeDetailReportList.add(biStoreDetailReport);
//
//                }
//            }
//
//            // 入库
//            for (BiStoreDetailReport biStoreDetailReport : storeDetailReportList) {
//                biStoreDetailReportMapper.insert(biStoreDetailReport);
//            }
//
//            //LOGGER.info("{}", storeDetailReportList.size());
//        } catch (Exception ex) {
//            LOGGER.error("【saveStoreReportSkuDetail】", ex);
//        }
//    }
//
//
//}
