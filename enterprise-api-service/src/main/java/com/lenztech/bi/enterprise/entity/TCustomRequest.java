package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;

import java.time.LocalDateTime;

/**
 * <p>
 * 接入客户特有数据存储表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-28
 */
public class TCustomRequest extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 拜访唯一标识
     */
    private String dataId;

    /**
     * 江中（门店类型，5个可选整数，0->非三类，1->沃尔玛，2->山姆，3->麦德龙，4->小店）
）
     */
    private String custType;

    private LocalDateTime reqTime;

    private LocalDateTime createTime;

    private String responseId;

    /**
     * 接入方Id标识
     */
    private String partnerId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public String getCustType() {
        return custType;
    }

    public void setCustType(String custType) {
        this.custType = custType;
    }

    public LocalDateTime getReqTime() {
        return reqTime;
    }

    public void setReqTime(LocalDateTime reqTime) {
        this.reqTime = reqTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }
}
