package com.lenztech.bi.enterprise.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 宝洁poc无识别结果sku报表
 * <AUTHOR>
 * @version V1.0
 * @date 2019-11-11 15:47
 * @since JDK 1.8
 */
@Data
public class PgPocSkuNotExistEntity {

    /**
     * 任务id
     */
    private String taskId;
    /**
     * 答卷id
     */
    private String rid;
    /**
     * 品牌名称
     */
    private String brand;
    /**
     * 产品id
     */
    private String pid;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 是否是必分销
     */
    private String ifMust;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
