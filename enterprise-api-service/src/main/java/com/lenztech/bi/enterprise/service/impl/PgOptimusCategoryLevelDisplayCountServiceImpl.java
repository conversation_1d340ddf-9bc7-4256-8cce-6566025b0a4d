package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.entity.PgOptimusCategoryLevelDisplayCount;
import com.lenztech.bi.enterprise.mapper.PgOptimusCategoryLevelDisplayCountMapper;
import com.lenztech.bi.enterprise.service.IPgOptimusCategoryLevelDisplayCountService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Category级别二陈数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Service
public class PgOptimusCategoryLevelDisplayCountServiceImpl extends ServiceImpl<PgOptimusCategoryLevelDisplayCountMapper, PgOptimusCategoryLevelDisplayCount> implements IPgOptimusCategoryLevelDisplayCountService {

}
