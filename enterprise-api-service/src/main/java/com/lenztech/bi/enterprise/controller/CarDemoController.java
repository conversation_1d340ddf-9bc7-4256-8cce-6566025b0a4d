package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.car.AllPostCodeRateDTO;
import com.lenztech.bi.enterprise.dto.car.CarDetailDTO;
import com.lenztech.bi.enterprise.dto.car.CarPatternDTO;
import com.lenztech.bi.enterprise.dto.car.CarSellRateDTO;
import com.lenztech.bi.enterprise.dto.car.CarStatisticsDataDTO;
import com.lenztech.bi.enterprise.dto.car.CarStatisticsDataReq;
import com.lenztech.bi.enterprise.dto.car.LineChartStatisticsDTO;
import com.lenztech.bi.enterprise.dto.car.PeopleExecuteStatisticsDTO;
import com.lenztech.bi.enterprise.dto.car.PostCodeDTO;
import com.lenztech.bi.enterprise.dto.car.ProvincePostCodeDTO;
import com.lenztech.bi.enterprise.service.ICarDemoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:汽车demo
 * @Author: yangtao
 * @Date: 3/18/20 AM10:30
 */
@RestController
@RequestMapping("/biResult/carDemo")
public class CarDemoController {

    @Autowired
    private ICarDemoService iCarDemoService;

    /**
     * 获取人员执行情况和门店覆盖
     *
     * @param carType
     * @return
     */
    @RequestMapping(value = "/getPeoplePerformAndStoreCover", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取人员执行情况和门店覆盖")
    public ResponseData getPeoplePerformAndStoreCover(String carType) {
        LineChartStatisticsDTO result = iCarDemoService.getPeoplePerformAndStoreCover(carType);
        return ResponseData.success(result);
    }

    /**
     * 获取人员执行统计
     *
     * @return
     */
    @RequestMapping(value = "/getDataByPeoplePerform", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取人员执行统计")
    public ResponseData getDataByPeoplePerform(String carType, String area) {
        PeopleExecuteStatisticsDTO result = iCarDemoService.getDataByPeoplePerform(carType, area);
        return ResponseData.success(result);
    }

    /**
     * 获取全国贴码率数据
     *
     * @return ResponseData
     */
    @RequestMapping(value = "/getAllPostCodeRate", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取全国贴码率数据")
    public ResponseData getAllPostCodeRate() {
        AllPostCodeRateDTO result = iCarDemoService.getAllPostCodeRate();
        return ResponseData.success(result);
    }

    /**
     * 获取省份贴码率数据
     *
     * @param province
     * @return
     */
    @RequestMapping(value = "/getProvincePostCodeRate", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取省份贴码率数据")
    public ResponseData getProvincePostCodeRate(String province) {
        ProvincePostCodeDTO result = iCarDemoService.getProvincePostCodeRate(province);
        return ResponseData.success(result);
    }

    /**
     * 获取省份贴码率数据
     *
     * @param address
     * @return
     */
    @RequestMapping(value = "/getAddressPostCodeRate", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取地址贴码率数据")
    public ResponseData getAddressPostCodeRate(String address) {
        PostCodeDTO result = iCarDemoService.getAddressPostCodeRate(address);
        return ResponseData.success(result);
    }

    /**
     * 获取各汽车品牌销售占比
     *
     * @return
     */
    @RequestMapping(value = "/getCarSellRate", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取各汽车品牌销售占比")
    public ResponseData getCarSellRate() {
        CarSellRateDTO result = iCarDemoService.getCarSellRate();
        return ResponseData.success(result);
    }

    /**
     * 根据汽车品牌获取统计数据
     *
     * @param req
     * @return
     */
    @RequestMapping(value = "/getStatisticDataByBrand", method = RequestMethod.POST)
    @ControllerAnnotation(use = "根据汽车品牌获取统计数据")
    public ResponseData getStatisticDataByBrand(@RequestBody CarStatisticsDataReq req) {
        CarStatisticsDataDTO result = iCarDemoService.getStatisticDataByBrand(req);
        return ResponseData.success(result);
    }


    /**
     * 根据车型查看详细数据
     *
     * @return
     */
    @RequestMapping(value = "/getDataByCarPattern", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据车型查看详细数据")
    public ResponseData getDataByCarPattern(String pattern, Integer pageSize, Integer pageNo) {
        CarDetailDTO result = iCarDemoService.getDataByCarPattern(pattern, pageSize, pageNo);
        return ResponseData.success(result);
    }


}
