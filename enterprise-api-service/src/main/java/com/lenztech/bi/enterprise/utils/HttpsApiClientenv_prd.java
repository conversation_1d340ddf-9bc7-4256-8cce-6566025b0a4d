//
//  Created by  fred on 2017/1/12.
//  Copyright © 2016年 Alibaba. All rights reserved.
//

package com.lenztech.bi.enterprise.utils;

import com.alibaba.cloudapi.sdk.client.ApacheHttpClient;
import com.alibaba.cloudapi.sdk.enums.HttpMethod;
import com.alibaba.cloudapi.sdk.enums.Scheme;
import com.alibaba.cloudapi.sdk.model.ApiCallback;
import com.alibaba.cloudapi.sdk.model.ApiRequest;
import com.alibaba.cloudapi.sdk.model.ApiResponse;
import com.alibaba.cloudapi.sdk.model.HttpClientBuilderParams;

import java.util.List;
import java.util.Map;

public class HttpsApiClientenv_prd extends ApacheHttpClient{
    public final static String HOST = "ali-api-prd.danonewaters.com.cn";
    static HttpsApiClientenv_prd instance = new HttpsApiClientenv_prd();
    public static HttpsApiClientenv_prd getInstance(){return instance;}

    public void init(HttpClientBuilderParams httpClientBuilderParams){
        httpClientBuilderParams.setScheme(Scheme.HTTPS);
        httpClientBuilderParams.setHost(HOST);
        super.init(httpClientBuilderParams);
    }



    public void compass_saveinspectdata(Map<String, List<String>> queryParams, Map<String, List<String>> headerParams, byte[] body , ApiCallback callback) {
        String path = "/compass/mdmsfaapi/saveinspectdata";
        ApiRequest request = new ApiRequest(HttpMethod.POST_BODY , path, body);
        
        request.setQuerys(queryParams);
        request.setHeaders(headerParams);
        request.setBody(body);


        sendAsyncRequest(request , callback);
    }

    public ApiResponse compass_saveinspectdataSyncMode(Map<String, List<String>> queryParams, Map<String, List<String>> headerParams, byte[] body) {
        String path = "/compass/mdmsfaapi/saveinspectdata";
        ApiRequest request = new ApiRequest(HttpMethod.POST_BODY , path, body);
        
        request.setQuerys(queryParams);
        request.setHeaders(headerParams);
        request.setBody(body);


        return sendSyncRequest(request);
    }

}