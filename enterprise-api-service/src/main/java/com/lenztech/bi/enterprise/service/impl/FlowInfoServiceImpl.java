//package com.lenztech.bi.enterprise.service.impl;
//
//import com.lenztech.bi.enterprise.entity.FlowInfo;
//import com.lenztech.bi.enterprise.mapper.bienterprise.FlowInfoMapper;
//import com.lenztech.bi.enterprise.service.IFlowInfoService;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import org.springframework.stereotype.Service;
//
///**
// * <p>
// * 流程主体信息 服务实现类
// * </p>
// *
// * <AUTHOR>
// * @since 2019-08-28
// */
//@Service
//public class FlowInfoServiceImpl extends ServiceImpl<FlowInfoMapper, FlowInfo> implements IFlowInfoService {
//
//}
