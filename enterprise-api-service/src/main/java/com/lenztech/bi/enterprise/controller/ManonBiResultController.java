package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.manon.ManonBiTargetResp;
import com.lenztech.bi.enterprise.dto.wangwang.WangWangBiTargetResp;
import com.lenztech.bi.enterprise.service.ManonReportService;
import com.lenztech.bi.enterprise.service.WangwangReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 曼伦bi指标Controller
 *
 * <AUTHOR>
 * @date 2019-10-17 15:44:19
 */
@RestController
@RequestMapping("/biResult/manon/")
public class ManonBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(ManonBiResultController.class);

    @Autowired
    private ManonReportService manonReportService;

    /**
     * 获取指标列表
     *
     * @param responseId
     * @return ResponseData<ManonBiTargetResp>
     */
    @RequestMapping(value = "getTargetList", method = RequestMethod.GET)
    public ResponseData<ManonBiTargetResp> get(String responseId) {
        try {
            ManonBiTargetResp manonBiTargetResp = manonReportService.getBiTargetList(responseId);
            return ResponseData.success().data(manonBiTargetResp);
        } catch (Exception e) {
            logger.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

}
