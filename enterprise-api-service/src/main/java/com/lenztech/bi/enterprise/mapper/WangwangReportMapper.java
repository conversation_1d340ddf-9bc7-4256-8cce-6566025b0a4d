package com.lenztech.bi.enterprise.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.dto.jml.JmlBiTarget;
import com.lenztech.bi.enterprise.dto.wangwang.WangwangBiReport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 旺旺bi指标Mapper
 *
 * <AUTHOR>
 * @date 2019-10-17 15:44:19
 */
@Mapper
@DS("lenzbi")
public interface WangwangReportMapper {

    /**
     * 查询指标集合
     *
     * @param responseId 答卷Id
     * @return List<JmlBiTarget>
     */
    List<WangwangBiReport> getBiTargetList(@Param("responseId") String responseId);

}
