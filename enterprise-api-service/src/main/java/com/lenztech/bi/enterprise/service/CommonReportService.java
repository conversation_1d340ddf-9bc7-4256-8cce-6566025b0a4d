package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.common.CommonBiImage;
import com.lenztech.bi.enterprise.dto.common.CommonBiProduct;
import com.lenztech.bi.enterprise.dto.common.CommonBiTargetResp;
import com.lenztech.bi.enterprise.mapper.lenzbi.CommonReportMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 通用bi指标Service
 *
 * <AUTHOR>
 * @date 2021-08-19 14:41:10
 */
@Slf4j
@Service
@RefreshScope
public class CommonReportService {

    @Autowired
    private CommonReportMapper commonReportMapper;

    @Autowired
    private ShardingService shardingService;

    @Value("${sharding.commonEffectiveDay:''}")
    private String effectiveDay;

    /**
     * 查询指标集合
     *
     * @param responseId
     * @return CommonBiTargetResp
     */
    public CommonBiTargetResp getBiTargetList(String responseId) {
        log.info("responseId:" + responseId);
        CommonBiTargetResp commonBiTargetResp = new CommonBiTargetResp();
        String shardingMonth = shardingService.getShardingKey(responseId, effectiveDay);
        try {
            List<CommonBiImage> imageList = commonReportMapper.getImageList(responseId, shardingMonth);
            List<CommonBiProduct> productList = commonReportMapper.getProductList(responseId, shardingMonth);
            commonBiTargetResp.setResponseId(responseId);
            commonBiTargetResp.setImageList(imageList);
            commonBiTargetResp.setProductList(productList);
        } catch (Exception e) {
            log.error("/getBiTargetList========", e);
        }
        return commonBiTargetResp;
    }

    /**
     * 根据responseId查询表数据集合
     *
     * @param tableName
     * @param responseId 答卷Id
     * @return List<Map>
     */
    public List<Map> getTableDataList(String tableName, String responseId) {
        log.info("responseId:" + responseId);
        List<Map> list = new ArrayList<>();
        try {
            list = commonReportMapper.getTableDataList(tableName, responseId);
        } catch (Exception e) {
            log.error("/getBiTargetList========", e);
        }
        return list;
    }
}
