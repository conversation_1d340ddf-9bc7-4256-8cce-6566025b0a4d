package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.dto.byhealth.ByhealthBiResult;
import com.lenztech.bi.enterprise.dto.byhealth.ByhealthBiTargetResp;
import com.lenztech.bi.enterprise.mapper.lenzbi.ByhealthReportMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 汤臣倍健bi指标Service
 *
 * <AUTHOR>
 * @date 2021-08-19 14:41:10
 */
@Slf4j
@Service
public class ByhealthReportService {

    @Autowired
    private ByhealthReportMapper byhealthReportMapper;

    /**
     * 查询指标集合
     *
     * @param responseId
     * @return ByhealthBiTargetResp
     */
    public ByhealthBiTargetResp getBiTargetList(String responseId) {
        log.info("responseId:" + responseId);
        ByhealthBiTargetResp byhealthBiTargetResp = new ByhealthBiTargetResp();
        try {
            List<ByhealthBiResult> list = byhealthReportMapper.getTargetList(responseId);
            byhealthBiTargetResp.setResponseId(responseId);
            byhealthBiTargetResp.setTargetList(list);
        } catch (Exception e) {
            log.error("/getBiTargetList========", e);
        }
        return byhealthBiTargetResp;
    }

}
