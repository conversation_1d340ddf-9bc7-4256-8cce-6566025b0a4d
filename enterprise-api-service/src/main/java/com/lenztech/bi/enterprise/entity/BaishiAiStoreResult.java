package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 千镇门店分销汇总结果
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("baishi_ai_store_result")
public class BaishiAiStoreResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    private Integer id;

    /**
     * rid
     */
    private String responseId;

    /**
     * 必分销数目标
     */
    private Integer mdistTarget;

    /**
     * 必分销数
     */
    private Integer mdistCount;

    /**
     * 必分销率
     */
    private String mdistRate;

    /**
     * 分销数
     */
    private Integer distCount;

    /**
     * 缺货必分销数
     */
    private Integer lackCount;

    /**
     * 目标完成率
     */
    private String targetRate;

    /**
     * 货架占比
     */
    private Double percentage;

    /**
     * 面位数
     */
    private Integer facingCount;

    /**
     * sku必分销面位目标
     */
    private Integer mfacTarget;


}
