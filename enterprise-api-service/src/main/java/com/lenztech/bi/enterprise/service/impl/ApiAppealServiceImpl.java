package com.lenztech.bi.enterprise.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lenztech.bi.enterprise.comon.Constant;
import com.lenztech.bi.enterprise.dto.IStatusMessage;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.RetStatus;
import com.lenztech.bi.enterprise.dto.redis.SendRedisMessageDTO;
import com.lenztech.bi.enterprise.dto.redis.SendRedisProductDTO;
import com.lenztech.bi.enterprise.entity.AppealInfo;
import com.lenztech.bi.enterprise.entity.Company;
import com.lenztech.bi.enterprise.entity.MenuAppealAnswers;
import com.lenztech.bi.enterprise.entity.MenuAppealQuestions;
import com.lenztech.bi.enterprise.entity.TCustomRequest;
import com.lenztech.bi.enterprise.entity.TImageProductTree;
import com.lenztech.bi.enterprise.entity.TResponse;
import com.lenztech.bi.enterprise.entity.TTask;
import com.lenztech.bi.enterprise.entity.TUser;
import com.lenztech.bi.enterprise.http.call.AppealResultNotice;
import com.lenztech.bi.enterprise.http.call.AppealSkuResultInfo;
import com.lenztech.bi.enterprise.http.request.AppealQueryRequest;
import com.lenztech.bi.enterprise.http.request.AppealSkuInfo;
import com.lenztech.bi.enterprise.http.request.AppealSubmitRequest;
import com.lenztech.bi.enterprise.service.ApiAppealService;
import com.lenztech.bi.enterprise.service.IAppealInfoService;
//import com.lenztech.bi.enterprise.service.ICompanyService;
import com.lenztech.bi.enterprise.service.IMenuAppealAnswersService;
import com.lenztech.bi.enterprise.service.IMenuAppealQuestionsService;
//import com.lenztech.bi.enterprise.service.ITCustomQuestionRelationService;
//import com.lenztech.bi.enterprise.service.ITCustomRequestService;
import com.lenztech.bi.enterprise.service.ITImageProductTreeService;
import com.lenztech.bi.enterprise.service.ITImageStoreProductExistService;
import com.lenztech.bi.enterprise.service.ITImageStoreProductService;
import com.lenztech.bi.enterprise.service.ITResponseService;
import com.lenztech.bi.enterprise.service.ITTaskService;
import com.lenztech.bi.enterprise.service.ITUserService;
import com.lenztech.bi.enterprise.utils.DateUtil;
import com.lenztech.bi.enterprise.utils.JsonUtil;
import com.lenztech.bi.enterprise.utils.ValidataUtil;
import com.trax.lenz.common.core.constant.Constants;
import com.trax.lenz.common.core.domain.R;
import com.trax.lenz.dto.request.IdentifyParameterReq;
import com.trax.lenz.dto.response.IdentifyParameterResp;
import com.trax.lenz.remote.TeemoServiceFeignClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

@Service
public class ApiAppealServiceImpl implements ApiAppealService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RedisTemplate redisTemplate;
    @Value("${spring.redis.queue.key}")
    private String redisKey;

//    @Autowired
//    private ITCustomRequestService iTCustomRequestService;

    @Autowired
    private ITResponseService iTResponseService;

    @Autowired
    private ITTaskService iTTaskService;

    @Autowired
    private IAppealInfoService iAppealInfoService;
//
//    @Autowired
//    private ICompanyService iCompanyService;
    @Autowired
    private ITUserService iTUserService;

    @Autowired
    private IMenuAppealAnswersService iMenuAppealAnswersService;

    @Autowired
    private IMenuAppealQuestionsService iMenuAppealQuestionsService;

    @Autowired
    private ITImageProductTreeService iTImageProductTreeService;
//
//    @Autowired
//    private ITCustomQuestionRelationService iTCustomQuestionRelationService;

    @Autowired
    private ITImageStoreProductService itImageStoreProductService;

    @Autowired
    private ITImageStoreProductExistService itImageStoreProductExistService;

    @Autowired
    private TeemoServiceFeignClient teemoServiceFeignClient;

//    @Autowired
//    private RedisTemplate redisTemplate;

    @Override
    public ResponseData addAppeal(AppealSubmitRequest appealSubmitRequest,Boolean flag) throws Exception{
        try{
            QueryWrapper<TCustomRequest> queryWrapper = new QueryWrapper<>();
            Map<String,Object> params = new HashMap<>();
            logger.info("接收到提交申诉请求【申诉参数】：" + JSON.toJSONString(appealSubmitRequest));
            //拼接data_id
            String dataId = StringUtils.join(appealSubmitRequest.getBusinessDataParamList(), Constant.DATA_SEPARATOR);
            //此处因表编码问题,只能分三次查询
            // 20221205: 因客户二次提交, 客户主数据查询更新为teemo获取
            IdentifyParameterReq parameterReq = new IdentifyParameterReq();
            parameterReq.setCustomerRequestIdList(Arrays.asList(appealSubmitRequest.getBusinessDataParamList()));
            parameterReq.setCompanyId(appealSubmitRequest.getCompanyId());
            R<IdentifyParameterResp> customerInfo = teemoServiceFeignClient.getCustomerInfo(parameterReq);

            if (!Constants.SUCCESS.equals(customerInfo.getCode()) || StringUtils.isEmpty(customerInfo.getData().getResponseId())) {
                return ResponseData.failure().msg("未找到答卷");
            }

            String responseId = customerInfo.getData().getResponseId();
//            String taskId = customerInfo.getData().getTaskId();
//            Integer biCompanyId = iTCustomQuestionRelationService.selectBICompanyIdByTaskId(taskId);
//            logger.info("提交申诉请求【responseId】：" + responseId);
//            //查询企业信息
//            Company company = iCompanyService.getById(biCompanyId);
//            if(null == company){
//                return ResponseData.failure().status(RetStatus.builder().status(IStatusMessage.SystemStatus.COMPANY_NOT_EXSIT));
//            }

            QueryWrapper<AppealInfo> queryAppealInfoWrapper = new QueryWrapper<>();
            queryAppealInfoWrapper.eq("response_id", responseId);
            List<AppealInfo> appealInfos = iAppealInfoService.list(queryAppealInfoWrapper);
            if(CollectionUtils.isNotEmpty(appealInfos)){
                return ResponseData.failure().status(RetStatus.builder().status(IStatusMessage.SystemStatus.APPEAL_EXSIT));
            }
            //查询答卷
            TResponse tResponse = iTResponseService.getById(responseId);
            //查询问卷信息
            TTask tTask = iTTaskService.getById(tResponse.getTaskidOwner());
            //查询答卷人信息
            //TUser tUser = iTUserService.getById(tResponse.getUId());
            AppealInfo appealInfo = new AppealInfo();
            appealInfo.setTaskidOwner(tTask.getId());
            appealInfo.setTasknameOwner(tTask.getTaskname());
            appealInfo.setResponseId(responseId);
            appealInfo.setCompanyId(getCompanyId(customerInfo.getData().getTaskId()));
            //SDK机构名称填data_id[1]
            if (appealSubmitRequest.getBusinessDataParamList().length>1){
                appealInfo.setBranchName(appealSubmitRequest.getBusinessDataParamList()[1]);

            }else {
                appealInfo.setBranchName(appealSubmitRequest.getBusinessDataParamList()[0]);

            }
            if (appealSubmitRequest.getBusinessDataParamList().length>2){
                appealInfo.setQuestionName(appealSubmitRequest.getBusinessDataParamList()[2]);

            }else{
                appealInfo.setQuestionName(appealSubmitRequest.getBusinessDataParamList()[0]);
            }

            //SDK问题名称填data_id[2]
            // 申诉描述
            if (StringUtils.isNotBlank(appealSubmitRequest.getAppealContent())){
                appealInfo.setAppealContent(appealSubmitRequest.getAppealContent());
            }else {
                appealInfo.setAppealContent(" ");
            }

            appealInfo.setAppealName(Constant.APPEAL_NAME_PREFIX+ DateUtil.getCurrentDateStr());
            appealInfo.setAppealProgress(Constant.APPEAL_PROGRESS_DEFAULT);
            appealInfo.setShensuType(Constant.APPEAL_SHENSU_TYPE_IMAGE_RECOGNITION);
            appealInfo.setCheckState(Constant.APPEAL_CHECKSTATE);
            //SDK申诉人填data_id[0]
            appealInfo.setCreator(appealSubmitRequest.getBusinessDataParamList()[0]);
            appealInfo.setCreateTime(new Date());
            appealInfo.setStartTime(DateUtil.getFormatTime(tResponse.getStartTime(),"yyyy-MM-dd"));
            //保存申诉
            return saveAppeal( tResponse,  tTask,  appealInfo, appealSubmitRequest,flag);
        }catch (Exception e){
            throw e;
        }
    }

    private Integer getCompanyId(String taskId){
        // GSK
        if ("a91c40f913d04d86b51c0d044f4d3af9".equals(taskId) || "dd04600f44b74761a3a53947fc580837".equals(taskId)){
            return 24;
        }
        // GSK
        if ("607017f9f45845c08cbf41957429e9a5".equals(taskId) || "0695449d26c7421c8693cae789ed00ff".equals(taskId)){
            return 44;
        }
        // 百事
        return 1000048;
    }

    @Transactional
    public ResponseData saveAppeal(TResponse tResponse, TTask tTask, AppealInfo appealInfo,AppealSubmitRequest appealSubmitRequest,Boolean flag){
        List<AppealSkuInfo> skus = appealSubmitRequest.getSkus();
        for (AppealSkuInfo appealSkuInfo : skus) {
            if (org.apache.commons.lang3.StringUtils.isBlank(appealSkuInfo.getSkuCode())) {
                return ResponseData.failure().msg("skuCode is not allow empty");
            }
            if(null != appealSkuInfo.getFacing() && !ValidataUtil.isNumber(String.valueOf(appealSkuInfo.getFacing())) ){
                return ResponseData.failure().msg("facing only supports positive numbers and up to two decimal places");
            }
            if(null != appealSkuInfo.getExist() && (1 != appealSkuInfo.getExist() && 0 != appealSkuInfo.getExist())){
                return ResponseData.failure().msg("exist only supports 0 or 1");
            }
        }
        //保存申诉基本信息
        iAppealInfoService.save(appealInfo);
        // 定义传值给BI的redis数据对象
        SendRedisMessageDTO sendRedisMessageDTO = new SendRedisMessageDTO();
        sendRedisMessageDTO.setResponseId(appealInfo.getResponseId());
        sendRedisMessageDTO.setAppealStatus(2);
        List<SendRedisProductDTO> sendRedisProductDTOList = Lists.newArrayList();

        for (AppealSkuInfo appealSkuInfo : skus) {
            TImageProductTree tImageProductTree = iTImageProductTreeService.getById(appealSkuInfo.getSkuCode());
            // 增加数值是否修改校验  修改了才进行入库 注:2021-12-24, 确认申诉需求为去掉对比识别结果数据逻辑
            if (null != appealSkuInfo.getExist()) {
                MenuAppealQuestions appealQuestion = buildMenuAppealQuestions(tImageProductTree, appealSkuInfo, tTask.getId(), Constant.QUESTION_TYPE_EXIST);
                MenuAppealAnswers menuAppealAnswers = saveQuestionAndBuildAnswer(tResponse, tTask, appealInfo, appealQuestion);
                menuAppealAnswers.setExistStatus(appealSkuInfo.getExist());
                iMenuAppealAnswersService.save(menuAppealAnswers);
                logger.info("【保存申诉分销数据】：" + menuAppealAnswers);

                // 分销做数据处理
                SendRedisProductDTO sendRedisProductDTO = new SendRedisProductDTO();
                sendRedisProductDTO.setExist(menuAppealAnswers.getExistStatus());
                sendRedisProductDTO.setProductId(Integer.valueOf(menuAppealAnswers.getProductId()));
                sendRedisProductDTO.setQuestionId(menuAppealAnswers.getQuestionId());
                sendRedisProductDTOList.add(sendRedisProductDTO);
            }
            // 增加数值是否修改校验  修改了才进行入库 注:2021-12-24, 确认申诉需求为去掉对比识别结果数据逻辑
            if (null != appealSkuInfo.getFacing()) {
                MenuAppealQuestions appealQuestion = buildMenuAppealQuestions(tImageProductTree, appealSkuInfo, tTask.getId(), Constant.QUESTION_TYPE_FACING);
                MenuAppealAnswers menuAppealAnswers = saveQuestionAndBuildAnswer(tResponse, tTask, appealInfo, appealQuestion);
                menuAppealAnswers.setFacing(appealSkuInfo.getFacing());
                iMenuAppealAnswersService.save(menuAppealAnswers);
                logger.info("【保存申诉面位数据】：" + menuAppealAnswers);
            }
        }

        logger.info("当前状态"+flag);
        logger.info("数量"+sendRedisProductDTOList.size());
        if (CollectionUtils.isNotEmpty(sendRedisProductDTOList) && flag)
        {
            logger.info("程序进来发送Redis");
            sendRedisMessageDTO.setSendRedisProductDTOList(sendRedisProductDTOList);
            // list型redis
            ListOperations<String, String> listOperations = redisTemplate.opsForList();
            // 存入数据到redis中
            Long pushRes = listOperations.leftPush(redisKey, JsonUtil.toJsonString(sendRedisMessageDTO));
            logger.info("发送Redis成功:"+pushRes);
        }

        return ResponseData.success();
    }
    /**
     * @descript:保存问题并构建答案
     * @param tResponse
     * @param tTask
     * @param appealInfo
     * @param appealQuestion
     * @return
     */
    private MenuAppealAnswers saveQuestionAndBuildAnswer(TResponse tResponse, TTask tTask, AppealInfo appealInfo, MenuAppealQuestions appealQuestion) {
        iMenuAppealQuestionsService.save(appealQuestion);
        MenuAppealAnswers menuAppealAnswers = new MenuAppealAnswers();
        menuAppealAnswers.setTaskidOwner(tTask.getId());
        menuAppealAnswers.setResponseId(tResponse.getId());
        menuAppealAnswers.setCreateTime(new Date());
        menuAppealAnswers.setUpdateTime(new Date());
        menuAppealAnswers.setAppealId(appealInfo.getAppealId());
        menuAppealAnswers.setQuestionId(appealQuestion.getQuestionId());
        menuAppealAnswers.setQuestionType(appealQuestion.getQuestionType());
        menuAppealAnswers.setProductId(appealQuestion.getProductId());
        return menuAppealAnswers;
    }

    @Override
    public ResponseData queryAppealResult(AppealQueryRequest appealQueryRequest) throws Exception{
        try{
            QueryWrapper<TCustomRequest> queryWrapper = new QueryWrapper<>();
            Map<String,Object> params = new HashMap<>();
            logger.info("接收到申诉查询【申诉查询请求参数】：" + JSON.toJSONString(appealQueryRequest));
            //拼接data_id
            String dataId = StringUtils.join(appealQueryRequest.getBusinessDataParamList(), Constant.DATA_SEPARATOR);
            //此处因表编码问题,只能分三次查询

            // 20221205: 因客户二次提交, 客户主数据查询更新为teemo获取
            IdentifyParameterReq parameterReq = new IdentifyParameterReq();
            parameterReq.setCustomerRequestIdList(Arrays.asList(appealQueryRequest.getBusinessDataParamList()));
            parameterReq.setCompanyId(appealQueryRequest.getCompanyId());
            R<IdentifyParameterResp> customerInfo = teemoServiceFeignClient.getCustomerInfo(parameterReq);

            if (!Constants.SUCCESS.equals(customerInfo.getCode()) || StringUtils.isEmpty(customerInfo.getData().getResponseId())) {
                return ResponseData.failure().msg("未找到答卷");
            }

            String responseId = customerInfo.getData().getResponseId();
            String taskId = customerInfo.getData().getTaskId();
//            Integer biCompanyId = iTCustomQuestionRelationService.selectBICompanyIdByTaskId(taskId);
//            logger.info("查询申诉请求【responseId】：" + responseId);
//            //查询企业信息
//            Company company = iCompanyService.getById(biCompanyId);
//            if(null == company){
//                return ResponseData.failure().status(RetStatus.builder().status(IStatusMessage.SystemStatus.COMPANY_NOT_EXSIT));
//            }

            QueryWrapper<AppealInfo> queryAppealInfoWrapper = new QueryWrapper<>();
            queryAppealInfoWrapper.eq("response_id", responseId);
            AppealInfo appealInfo = iAppealInfoService.getOne(queryAppealInfoWrapper);
            QueryWrapper<MenuAppealAnswers> queryMenuAppealAnswersWrapper = new QueryWrapper<>();
            queryMenuAppealAnswersWrapper.eq("response_id", responseId);
            queryMenuAppealAnswersWrapper.eq("appeal_id",appealInfo.getAppealId());
            List<MenuAppealAnswers> answers = iMenuAppealAnswersService.list(queryMenuAppealAnswersWrapper);
            AppealResultNotice appealResultNotice = new AppealResultNotice();
            appealResultNotice.setCompanyId(appealQueryRequest.getCompanyId());
            appealResultNotice.setBusinessDataParamList(appealQueryRequest.getBusinessDataParamList());
            appealResultNotice.setCheckState(appealInfo.getCheckState());
            List<AppealSkuResultInfo> skus = new CopyOnWriteArrayList<>();
            if(CollectionUtils.isNotEmpty(answers)){
                AppealSkuResultInfo tmpAppealSkuResultInfo = null;
                Map<String, AppealSkuResultInfo> map = new HashMap<String, AppealSkuResultInfo>();
                for (MenuAppealAnswers answer:answers) {
                    tmpAppealSkuResultInfo = map.get(answer.getProductId());
                    if (tmpAppealSkuResultInfo != null) {
                        if(Constant.QUESTION_TYPE_EXIST == answer.getQuestionType()){
                            tmpAppealSkuResultInfo.setExist((Constant.APPEAL_CHECKSTATE ==appealInfo.getCheckState())?answer.getExistStatus():answer.getResultExistStatus());
                        }else if(Constant.QUESTION_TYPE_FACING == answer.getQuestionType()){
                            tmpAppealSkuResultInfo.setFacing((Constant.APPEAL_CHECKSTATE == appealInfo.getCheckState())?answer.getFacing():answer.getResultFacing());
                        }
                        tmpAppealSkuResultInfo.setSkuCode(answer.getProductId());
                    } else {
                        tmpAppealSkuResultInfo = new AppealSkuResultInfo();
                        if(Constant.QUESTION_TYPE_EXIST == answer.getQuestionType()){
                            tmpAppealSkuResultInfo.setExist((Constant.APPEAL_CHECKSTATE ==appealInfo.getCheckState())?answer.getExistStatus():answer.getResultExistStatus());
                        }else if(Constant.QUESTION_TYPE_FACING == answer.getQuestionType()){
                            tmpAppealSkuResultInfo.setFacing((Constant.APPEAL_CHECKSTATE ==appealInfo.getCheckState())?answer.getFacing():answer.getResultFacing());
                        }
                        tmpAppealSkuResultInfo.setSkuCode(answer.getProductId());
                        map.put(answer.getProductId(), tmpAppealSkuResultInfo);
                        skus.add(tmpAppealSkuResultInfo);
                    }
                }
                appealResultNotice.setSkus(skus);
            }
           return ResponseData.success().data(appealResultNotice);
        }catch (Exception e){
            throw e;
        }
    }

    /**
     * @descript:增加申诉时构建申诉问题表数据
     * @param tImageProductTree
     * @param appealSkuInfo
     * @param taskId
     * @param questionType
     * @return
     */
    private MenuAppealQuestions buildMenuAppealQuestions(TImageProductTree tImageProductTree,AppealSkuInfo appealSkuInfo,String taskId,Integer questionType){
        MenuAppealQuestions appealQuestion = new MenuAppealQuestions();
        if(null != tImageProductTree){
            appealQuestion.setProductName(tImageProductTree.getName());
        }else{
            appealQuestion.setProductName("未知商品");
        }
        appealQuestion.setProductId(appealSkuInfo.getSkuCode());
        appealQuestion.setQuestionType(questionType);
        appealQuestion.setTaskidOwner(taskId);
        appealQuestion.setCreateTime(new Date());
        appealQuestion.setUpdateTime(new Date());
        return appealQuestion;
    }


}
