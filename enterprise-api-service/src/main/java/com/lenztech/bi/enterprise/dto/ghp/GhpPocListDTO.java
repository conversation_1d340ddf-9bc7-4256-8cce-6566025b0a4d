package com.lenztech.bi.enterprise.dto.ghp;

import com.lenztech.bi.enterprise.entity.GhyPocEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2019-11-11 16:49
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class GhpPocListDTO {

    private List<GhyPocEntity> ghyPocEntityList;

}
