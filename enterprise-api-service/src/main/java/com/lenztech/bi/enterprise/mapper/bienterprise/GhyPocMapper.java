package com.lenztech.bi.enterprise.mapper.bienterprise;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.entity.GhyPocEntity;
import com.lenztech.bi.enterprise.entity.PgPocSkuExistEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface GhyPocMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(GhyPocEntity record);

    int insertSelective(GhyPocEntity record);

    GhyPocEntity selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(GhyPocEntity record);

    int updateByPrimaryKey(GhyPocEntity record);

    /**
     * 查询有识别结果的sku列表
     * @param param
     * @return
     */
    List<GhyPocEntity> listGhyPocEntityEntity(Map<String, Object> param);
}