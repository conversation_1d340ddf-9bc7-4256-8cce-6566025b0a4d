package com.lenztech.bi.enterprise.dto.nivea;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 妮维雅全局数据拉取响应DTO
 */
@Data
public class GetGlobalDataResp {
    /**
     * 请求的结果数量
     */
    private Integer results_requested;
    
    /**
     * 生成的结果数量
     */
    private Integer results_generated;
    
    /**
     * 结果列表
     */
    private List<GlobalDataResult> results;
    
    @Data
    public static class GlobalDataResult {
        /**
         * 会话ID
         */
        private String session_uid;
        
        /**
         * 客户端会话ID
         */
        private String client_session_uid;
        
        /**
         * 客户端类型
         */
        private String client_type;
        
        /**
         * 项目名称
         */
        private String project_name;
        
        /**
         * 门店编号
         */
        private String store_number;
        
        /**
         * 外部路线ID
         */
        private String external_route_id;
        
        /**
         * 会话日期
         */
        private String session_date;
        
        /**
         * 会话开始时间
         */
        private String session_start_time;
        
        /**
         * 访问者标识
         */
        private String visitor_identifier;
        
        /**
         * 结果元数据
         */
        private ResultsMetadata results_metadata;
        
        /**
         * 结果链接
         */
        private String results_link;
    }
    
    @Data
    public static class ResultsMetadata {
        /**
         * 消息ID
         */
        private Long message_id;
        
        /**
         * 生成时间
         */
        private String generation_time;
        
        /**
         * 版本
         */
        private Integer version;
        
        /**
         * 状态
         */
        private String status;
        
        /**
         * 包含的部分
         */
        private List<String> sections_included;
    }
} 