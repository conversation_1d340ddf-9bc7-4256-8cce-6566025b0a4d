package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * <p>
 * ?????
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-06
 */
public class TTasklaunch extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 操作id
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer Id;

    /**
     * ??id
     */
    private String taskidOwner;

    /**
     * ???
     */
    private String tasknameOwner;

    /**
     * ??id
     */
    private String taskid;

    /**
     * ???
     */
    private String taskName;

    /**
     * ???? 2????3???????4????5 ????
     */
    private String status;

    /**
     * ????
     */
    private String position;

    /**
     * ????
     */
    private Double longitude;

    /**
     * ????
     */
    private Double latitude;

    /**
     * geohash??
     */
    private String geoCode;

    @TableField("rangeValue")
    private String rangeValue;

    /**
     * 0?????1????
     */
    private String addressStatus;

    private Integer executeNum;

    /**
     * ?????????????
     */
    private Integer executeNumOld;

    /**
     * ??????
     */
    private String transactor;

    /**
     * ?????????
     */
    @TableField("reallyAddress")
    private String reallyAddress;

    @TableField("addressIDnum")
    private String addressIDnum;

    /**
     * ??????????
     */
    @TableField("SeparateScope")
    private Integer SeparateScope;

    /**
     * 0:????? 1:????? 2:????? 3:?????
     */
    private String distStatus;

    /**
     * ?????
     */
    private Double subReward;

    /**
     * ??????  99.99???
     */
    @TableField("sub_reward_H")
    private Double subRewardH;

    /**
     * ?
     */
    private String province;

    /**
     * ?????0:?????1:????
     */
    private String canbook;

    /**
     * ??????uid
     */
    private String bookuser;

    /**
     * ????
     */
    private Double weidanReward;

    /**
     * ??
     */
    private String city;

    /**
     * ??
     */
    private String district;

    private String addressBookId;

    /**
     * ??id  ?????
     */
    private String bagid;

    /**
     * ???? 0? 1?
     */
    private String ifhuodong;

    /**
     * ???? 0? 1?
     */
    private String ifbicha;

    /**
     * ?????????????????????,???0
     */
    private Double credit;

    /**
     * ????uid, ??????20????????
     */
    private String specialUser;

    /**
     * ??????
     */
    private String storeTypeNumber;

    /**
     * ?? ????
     */
    private String remark;

    /**
     * ??????  ??0
     */
    private Integer remainTimes;

    private LocalDateTime insertTime;

    private LocalDateTime updateTime;

    public Integer getId() {
        return Id;
    }

    public void setId(Integer Id) {
        this.Id = Id;
    }
    public String getTaskidOwner() {
        return taskidOwner;
    }

    public void setTaskidOwner(String taskidOwner) {
        this.taskidOwner = taskidOwner;
    }
    public String getTasknameOwner() {
        return tasknameOwner;
    }

    public void setTasknameOwner(String tasknameOwner) {
        this.tasknameOwner = tasknameOwner;
    }
    public String getTaskid() {
        return taskid;
    }

    public void setTaskid(String taskid) {
        this.taskid = taskid;
    }
    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }
    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }
    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }
    public String getGeoCode() {
        return geoCode;
    }

    public void setGeoCode(String geoCode) {
        this.geoCode = geoCode;
    }
    public String getRangeValue() {
        return rangeValue;
    }

    public void setRangeValue(String rangeValue) {
        this.rangeValue = rangeValue;
    }
    public String getAddressStatus() {
        return addressStatus;
    }

    public void setAddressStatus(String addressStatus) {
        this.addressStatus = addressStatus;
    }
    public Integer getExecuteNum() {
        return executeNum;
    }

    public void setExecuteNum(Integer executeNum) {
        this.executeNum = executeNum;
    }
    public Integer getExecuteNumOld() {
        return executeNumOld;
    }

    public void setExecuteNumOld(Integer executeNumOld) {
        this.executeNumOld = executeNumOld;
    }
    public String getTransactor() {
        return transactor;
    }

    public void setTransactor(String transactor) {
        this.transactor = transactor;
    }
    public String getReallyAddress() {
        return reallyAddress;
    }

    public void setReallyAddress(String reallyAddress) {
        this.reallyAddress = reallyAddress;
    }
    public String getAddressIDnum() {
        return addressIDnum;
    }

    public void setAddressIDnum(String addressIDnum) {
        this.addressIDnum = addressIDnum;
    }
    public Integer getSeparateScope() {
        return SeparateScope;
    }

    public void setSeparateScope(Integer SeparateScope) {
        this.SeparateScope = SeparateScope;
    }
    public String getDistStatus() {
        return distStatus;
    }

    public void setDistStatus(String distStatus) {
        this.distStatus = distStatus;
    }
    public Double getSubReward() {
        return subReward;
    }

    public void setSubReward(Double subReward) {
        this.subReward = subReward;
    }
    public Double getSubRewardH() {
        return subRewardH;
    }

    public void setSubRewardH(Double subRewardH) {
        this.subRewardH = subRewardH;
    }
    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }
    public String getCanbook() {
        return canbook;
    }

    public void setCanbook(String canbook) {
        this.canbook = canbook;
    }
    public String getBookuser() {
        return bookuser;
    }

    public void setBookuser(String bookuser) {
        this.bookuser = bookuser;
    }
    public Double getWeidanReward() {
        return weidanReward;
    }

    public void setWeidanReward(Double weidanReward) {
        this.weidanReward = weidanReward;
    }
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }
    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }
    public String getAddressBookId() {
        return addressBookId;
    }

    public void setAddressBookId(String addressBookId) {
        this.addressBookId = addressBookId;
    }
    public String getBagid() {
        return bagid;
    }

    public void setBagid(String bagid) {
        this.bagid = bagid;
    }
    public String getIfhuodong() {
        return ifhuodong;
    }

    public void setIfhuodong(String ifhuodong) {
        this.ifhuodong = ifhuodong;
    }
    public String getIfbicha() {
        return ifbicha;
    }

    public void setIfbicha(String ifbicha) {
        this.ifbicha = ifbicha;
    }
    public Double getCredit() {
        return credit;
    }

    public void setCredit(Double credit) {
        this.credit = credit;
    }
    public String getSpecialUser() {
        return specialUser;
    }

    public void setSpecialUser(String specialUser) {
        this.specialUser = specialUser;
    }
    public String getStoreTypeNumber() {
        return storeTypeNumber;
    }

    public void setStoreTypeNumber(String storeTypeNumber) {
        this.storeTypeNumber = storeTypeNumber;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public Integer getRemainTimes() {
        return remainTimes;
    }

    public void setRemainTimes(Integer remainTimes) {
        this.remainTimes = remainTimes;
    }
    public LocalDateTime getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(LocalDateTime insertTime) {
        this.insertTime = insertTime;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "TTasklaunch{" +
        "Id=" + Id +
        ", taskidOwner=" + taskidOwner +
        ", tasknameOwner=" + tasknameOwner +
        ", taskid=" + taskid +
        ", taskName=" + taskName +
        ", status=" + status +
        ", position=" + position +
        ", longitude=" + longitude +
        ", latitude=" + latitude +
        ", geoCode=" + geoCode +
        ", rangeValue=" + rangeValue +
        ", addressStatus=" + addressStatus +
        ", executeNum=" + executeNum +
        ", executeNumOld=" + executeNumOld +
        ", transactor=" + transactor +
        ", reallyAddress=" + reallyAddress +
        ", addressIDnum=" + addressIDnum +
        ", SeparateScope=" + SeparateScope +
        ", distStatus=" + distStatus +
        ", subReward=" + subReward +
        ", subRewardH=" + subRewardH +
        ", province=" + province +
        ", canbook=" + canbook +
        ", bookuser=" + bookuser +
        ", weidanReward=" + weidanReward +
        ", city=" + city +
        ", district=" + district +
        ", addressBookId=" + addressBookId +
        ", bagid=" + bagid +
        ", ifhuodong=" + ifhuodong +
        ", ifbicha=" + ifbicha +
        ", credit=" + credit +
        ", specialUser=" + specialUser +
        ", storeTypeNumber=" + storeTypeNumber +
        ", remark=" + remark +
        ", remainTimes=" + remainTimes +
        ", insertTime=" + insertTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
