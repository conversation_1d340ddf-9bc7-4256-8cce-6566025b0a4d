//package com.lenztech.bi.enterprise.service;
//
//import com.baomidou.mybatisplus.extension.service.IService;
//import com.lenztech.bi.enterprise.entity.TCustomQuestionRelation;
//import org.apache.ibatis.annotations.Param;
//
///**
// * description:  接入客户-题目id对应关系表 服务类
// * <AUTHOR>
// * @date  2019-09-11 13:24
// * @since 2019-09-11
// **/
//public interface ITCustomQuestionRelationService extends IService<TCustomQuestionRelation> {
//
////    /**
////     * description:  根据第三方企业ID查询BI中对应的企业ID
////     * <AUTHOR>
////     * @date  2019-09-11 13:29
////     * @since 2019-09-11
////     * @param companyId :
////     * @return java.lang.Integer
////     **/
////    Integer selectBICompanyId(String companyId);
////
////    String selectResponseId(String dataId);
////
////    String selectTaskIdFromResponse(String responseId);
//
//    Integer selectBICompanyIdByTaskId(String taskId);
//}
