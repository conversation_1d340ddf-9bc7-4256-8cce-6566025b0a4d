package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.controller.base.BaseController;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.entity.MonsterMecPositioningInMainShelf;
import com.lenztech.bi.enterprise.service.QuickFunctionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/quickFunctions/")
public class QuickFunctionsController  extends BaseController {

    @Autowired
    QuickFunctionsService quickFunctionsService;

    @RequestMapping(value = "sendPendingReviewResult", method = RequestMethod.GET)
    @ControllerAnnotation(use = "发送无意义任务地址识别")
    public ResponseData sendPendingReviewResult(String taskIds) {

        return quickFunctionsService.sendPendingReviewResult(taskIds);
    }

    @RequestMapping(value = "sendAiIdentifyResult", method = RequestMethod.GET)
    @ControllerAnnotation(use = "补偿结果")
    public ResponseData sendAiIdentifyResult() {

        return quickFunctionsService.sendAiIdentifyResult();
    }
}
