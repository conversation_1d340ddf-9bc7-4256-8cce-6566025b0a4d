//package com.lenztech.bi.enterprise.service.impl;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.lenztech.bi.enterprise.dto.CompanyUserDTO;
//import com.lenztech.bi.enterprise.entity.TOperateLog;
//import com.lenztech.bi.enterprise.entity.TUser;
//import com.lenztech.bi.enterprise.mapper.bienterprise.ArgusCompanyDao;
//import com.lenztech.bi.enterprise.mapper.task.OperateLogMapper;
//import com.lenztech.bi.enterprise.mapper.task.TUserMapper;
//import com.lenztech.bi.enterprise.service.OperateLogService;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.time.LocalDateTime;
//import java.util.List;
//
///**
// * <AUTHOR>
// * @Description TODO
// * @Date 2020/9/11 15:29
// **/
//@Service
//public class OperateLogServiceImpl implements OperateLogService {
//
//    @Autowired
//    private ArgusCompanyDao argusCompanyDao;
//
//    @Autowired
//    private TUserMapper tUserMapper;
//
//    @Autowired
//    private OperateLogMapper operateLogMapper;
//
//    /**
//     * 生成操作记录 此业务不用了，注释掉访问task库内容，等lenz调用bi的逻辑删除后可删除此接口
//     *
//     * @param userId
//     */
//    @Override
//    public void saveRecord(String userId, String platform, String operateEvent) {
//
//        // 查询用户信息
//        LambdaQueryWrapper<TUser> userWrapper = new LambdaQueryWrapper<>();
//        userWrapper.eq(TUser::getId, userId);
//        List<TUser> userList = tUserMapper.selectList(userWrapper);
//        if (CollectionUtils.isEmpty(userList)) {
//            return;
//        }
//        List<CompanyUserDTO> companyUser = argusCompanyDao.getCompanyUser(userList.get(0).getPhone());
//        TOperateLog record = new TOperateLog();
//        record.setUserId(userId);
//        record.setPhone(companyUser.get(0).getAccount());
//        record.setCompanyId(companyUser.get(0).getCompanyId());
//        record.setOperateTime(LocalDateTime.now());
//        record.setPlatform(platform);
//        record.setOperateEvent(operateEvent);
//        operateLogMapper.insert(record);
//    }
//}
