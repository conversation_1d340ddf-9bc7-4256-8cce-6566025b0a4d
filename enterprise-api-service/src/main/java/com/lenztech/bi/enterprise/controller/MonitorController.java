package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 心跳监控接口
 * @Date 2020/11/26 11:17
 **/

@RestController
@RequestMapping("/monitor")
public class MonitorController {

    /**
     * 监控接口
     *
     * @return ResponseData<>
     */
    @RequestMapping(value = "/common", method = RequestMethod.GET)
    public ResponseData common() {
        return ResponseData.success();
    }
}
