package com.lenztech.bi.enterprise.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/9 15:17
 * @since JDK 1.8
 */
@Data
public class LibyProductDispalyListEntity {

    /**
     * 产品id
     */
    private Integer productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品显示名称
     */
    private String displayName;
    /**
     * 是否考核
     */
    private Integer isCheck;
    /**
     * 场景规则
     */
    private String rule;
    /**
     * 产品品类
     */
    private String category;
    /**
     * 品牌
     */
    private String brand;

}
