package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.gunman.SkuResultInfo;
import com.lenztech.bi.enterprise.dto.walls.WallsSkuResultInfo;
import com.lenztech.bi.enterprise.entity.HeluxueStoreRecord;
import com.lenztech.bi.enterprise.entity.QiangshouStoreRecord;
import com.lenztech.bi.enterprise.service.GunmanReportService;
import com.lenztech.bi.enterprise.service.WangwangReportService;
import com.lenztech.bi.enterprise.service.impl.WallsReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created with IntelliJ IDEA.
 * User: sun<PERSON>yuan
 * Date: 2021/6/4
 * Time: 10:35
 * 类功能: 和路雪, 英文名叫wall's, poc接口
 */
@RestController
@RequestMapping("/biResult/walls/")
public class WallsBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(WallsBiResultController.class);

    @Autowired
    private WallsReportService wallsReportService;

    /**
     * 获取枪手门店相关信息
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getStoreInfo", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取门店相关信息")
    public ResponseData<HeluxueStoreRecord> getStoreinfo(String responseId) {
        try {
            HeluxueStoreRecord result = wallsReportService.getStoreInfo(responseId);
            return ResponseData.success().data(result);
        } catch (Exception e) {
            logger.error("/getStoreInfo========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取枪手门店sku结果
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getSkuResult", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取和路雪门店sku结果")
    public ResponseData getSkuResult(String responseId) {
        try {
            WallsSkuResultInfo result = wallsReportService.getSkuResult(responseId);
            return ResponseData.success().data(result);
        } catch (Exception e) {
            logger.error("/getSkuResult========", e);
        }
        return ResponseData.failure();
    }


}
