package com.lenztech.bi.enterprise.controller.aspect;

import java.lang.annotation.*;

/**
 *
 * Description: controller切面注解
 *
 * @author: liwei<PERSON>
 * @date: 2018/9/12 下午2:09
 */
@Target({ ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ControllerAnnotation {

    /**
     * 用来标记将该API提供给给哪些厂商使用，COMMON 代表通用接口
     */
    public enum ENTERPRISEUSER{COMMON, SP1, INNER};

    /**  具体实现用，切片作用的方法用途 */
    String use() default "";

    /**
     * <AUTHOR>
     * @date 2018/9/12 下午2:07
     * @param 
     * @return com.lenztech.mobile.controller.aspect.ControllerAnnotation.ENTERPRISEUSER
     */
    ENTERPRISEUSER enterpriseUserAPI() default ENTERPRISEUSER.COMMON;
}
