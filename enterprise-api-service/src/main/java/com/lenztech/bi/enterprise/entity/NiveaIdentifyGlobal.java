package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 妮维雅识别结果实体类
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Data
@TableName("nivea_identify_global")
public class NiveaIdentifyGlobal {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 响应ID
     */
    private String responseId;

    /**
     * 门店编号
     */
    private String storeNumber;

    /**
     * 会话ID
     */
    private String sessionUid;

    /**
     * 会话ID
     */
    private String clientSessionUid;

    /**
     * 会话ID
     */
    private String resultsLink;

    /**
     * 会话ID
     */
    private Integer status;

    /**
     * 访问时间
     */
    private Date visitTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


} 