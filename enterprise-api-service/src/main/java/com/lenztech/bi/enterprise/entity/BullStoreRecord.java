package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

/**
 * <p>
 * 公牛门店记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
public class BullStoreRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * rid
     */
    private String responseId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 拍摄照片数
     */
    private Integer picCount;

    /**
     * 翻拍照片数
     */
    private Integer remakeCount;

    /**
     * 重复照片数
     */
    private Integer repeatCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
    public Integer getPicCount() {
        return picCount;
    }

    public void setPicCount(Integer picCount) {
        this.picCount = picCount;
    }
    public Integer getRemakeCount() {
        return remakeCount;
    }

    public void setRemakeCount(Integer remakeCount) {
        this.remakeCount = remakeCount;
    }
    public Integer getRepeatCount() {
        return repeatCount;
    }

    public void setRepeatCount(Integer repeatCount) {
        this.repeatCount = repeatCount;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BullStoreRecord{" +
            "id=" + id +
            ", responseId=" + responseId +
            ", storeName=" + storeName +
            ", picCount=" + picCount +
            ", remakeCount=" + remakeCount +
            ", repeatCount=" + repeatCount +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
        "}";
    }
}
