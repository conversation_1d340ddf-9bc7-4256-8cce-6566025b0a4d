package com.lenztech.bi.enterprise.dto.car;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class ProvinceData {
    /**
     * 省份
     */
    @Excel(name = "province")
    private String province;
    /**
     * 城市
     */
    @Excel(name = "city")
    private String city;
    /**
     * 区域
     */
    @Excel(name = "area")
    private String area;
    /**
     * 地址
     */
    @Excel(name = "address")
    private String address;
    /**
     * 贴码人数
     */
    @Excel(name = "postCodePeopleNum")
    private String postCodePeopleNum;
    /**
     * 贴码量
     */
    @Excel(name = "postCodeNum")
    private String postCodeNum;
    /**
     * 扫码量
     */
    @Excel(name = "scanCodeNum")
    private String scanCodeNum;
    /**
     * 问卷转化总量
     */
    @Excel(name = "responseTranslateNum")
    private String responseTranslateNum;
    /**
     * 裂变
     */
    @Excel(name = "fission")
    private String fission;
    /**
     * 贴码转化
     */
    @Excel(name = "postCodeTranslateNum")
    private String postCodeTranslateNum;
    /**
     * 异业联盟
     */
    @Excel(name = "IPZShopping")
    private String IPZShopping;
    /**
     * 其他
     */
    @Excel(name = "other")
    private String other;

}