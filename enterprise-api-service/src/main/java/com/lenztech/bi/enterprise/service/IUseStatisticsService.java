//package com.lenztech.bi.enterprise.service;
//
//import com.lenztech.bi.enterprise.dto.GetStatisticsReqDTO;
//import com.lenztech.bi.enterprise.dto.PageDataResult;
//import com.lenztech.bi.enterprise.dto.ResponseData;
//
//import javax.servlet.http.HttpServletResponse;
//
///**
// * <AUTHOR>
// * @Description TODO
// * @Date 2020/9/10 16:44
// **/
//public interface IUseStatisticsService {
//
//    /**
//     * 每日用户拜访统计
//     */
//    void userUseStatistics(String executeDate);
//
//    /**
//     * APP 月度使用情况统计
//     * @param reqDTO
//     * @return
//     */
//    ResponseData getOperateStatistics(GetStatisticsReqDTO reqDTO);
//
//    /**
//     * 导出 APP 月度使用情况统计
//     * @param reqDTO
//     * @return
//     */
//    ResponseData exportOperateStatistics(String startDate, String endDate, String companyId, HttpServletResponse response);
//
//    /**
//     * 获取用户统计数据
//     * @param reqDTO
//     * @return
//     */
//    PageDataResult getUserUseStatistic(GetStatisticsReqDTO reqDTO);
//
//    /**
//     * 获取用户答卷详细数据
//     *
//     * @param reqDTO
//     * @return
//     */
//    ResponseData getUserResponseList(GetStatisticsReqDTO reqDTO);
//
//    /**
//     * 获取门店信息列表
//     * @param reqDTO
//     * @return
//     */
//    ResponseData getStoreList(GetStatisticsReqDTO reqDTO);
//}
