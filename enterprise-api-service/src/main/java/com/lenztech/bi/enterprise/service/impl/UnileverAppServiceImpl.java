package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.comon.DataSourceEnums;
import com.lenztech.bi.enterprise.comon.RepeatStatusEnum;
import com.lenztech.bi.enterprise.dto.unileverapp.ApiImageResultDTO;
import com.lenztech.bi.enterprise.dto.unileverapp.ApiPatchResultDTO;
import com.lenztech.bi.enterprise.dto.unileverapp.ApiResultDTO;
import com.lenztech.bi.enterprise.dto.unileverapp.CoordinateDTO;
import com.lenztech.bi.enterprise.dto.unileverapp.StitchImageDTO;
import com.lenztech.bi.enterprise.entity.UnileverAppImageDetail;
import com.lenztech.bi.enterprise.entity.UnileverAppProductDetail;
import com.lenztech.bi.enterprise.entity.UnileverAppStitchImageDetail;
import com.lenztech.bi.enterprise.mapper.UnileverAppImageMapper;
import com.lenztech.bi.enterprise.mapper.UnileverAppProductMapper;
import com.lenztech.bi.enterprise.mapper.UnileverAppStitchImageDetailMapper;
import com.lenztech.bi.enterprise.service.ShardingService;
import com.lenztech.bi.enterprise.service.UnileverAppService;
import com.lenztech.bi.enterprise.utils.DateUtil;
import com.trax.lenz.common.core.id.SnowFlakeFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 联合利华App根据答卷id查询BI识别指标结果
 *
 * <AUTHOR>
 * @date 2022-01-17 17:44:19
 */
@Service
@Slf4j
@RefreshScope
public class UnileverAppServiceImpl implements UnileverAppService {


    @Autowired
    private UnileverAppImageMapper unileverAppImageMapper;

    @Autowired
    private UnileverAppProductMapper unileverAppProductMapper;

    @Autowired
    private UnileverAppStitchImageDetailMapper unileverAppStitchImageDetailMapper;

    @Autowired
    private ShardingService shardingService;

    @Value("${sharding.unileverEffectiveDay:''}")
    private String effectiveDay;

    @Autowired
    private SnowFlakeFactory snowFlakeFactory;

    /**
     * 根据答卷id查询BI结果
     *
     * @param responseId 答卷Id
     * @return ApiResultDTO
     */
    @Override
    public ApiResultDTO getBiTargetList(String responseId) {

        ApiResultDTO apiResultDTO = null;
        try {
            String shardingKey = shardingService.getShardingKey(responseId, effectiveDay);

            apiResultDTO = new ApiResultDTO();
            apiResultDTO.setResponseId(responseId);


            LambdaQueryWrapper<UnileverAppStitchImageDetail>  stitchImageDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
            stitchImageDetailLambdaQueryWrapper.eq(UnileverAppStitchImageDetail::getResponseId,responseId);
            List<UnileverAppStitchImageDetail> unileverAppStitchImageDetails = unileverAppStitchImageDetailMapper.selectList(stitchImageDetailLambdaQueryWrapper);

            List<StitchImageDTO> stitchImages  = new ArrayList<>();

            for (UnileverAppStitchImageDetail unileverAppStitchImageDetail : unileverAppStitchImageDetails) {
                StitchImageDTO stitchImageDTO = new StitchImageDTO();
                stitchImageDTO.setResponseId(unileverAppStitchImageDetail.getResponseId());
                stitchImageDTO.setGroupNo(unileverAppStitchImageDetail.getGroupNo());
                stitchImageDTO.setJointUrl(unileverAppStitchImageDetail.getJointUrl());
                stitchImageDTO.setQuestionId(unileverAppStitchImageDetail.getQuestionId());
                stitchImages.add(stitchImageDTO);
            }
            apiResultDTO.setStitchImages(stitchImages);

            // 查询所有图片信息
//        LambdaQueryWrapper<UnileverAppImageDetail> imageWrapper = new LambdaQueryWrapper<>();
//        imageWrapper.eq(UnileverAppImageDetail::getResponseId, responseId);
//        List<UnileverAppImageDetail> imageDetailList = unileverAppImageMapper.selectList(imageWrapper);

            String month = snowFlakeFactory.getDateMoth(responseId);
            String todayMonth = DateUtil.convert2String(new Date(), DateUtil.DTFormat.yyyyMM.getFormat());
            if (isSharding(Integer.valueOf(month), Integer.valueOf(todayMonth))) {
                log.info("联合利华项目查询历史答卷！切换数据源为polar-db！responseId={}, month={}", responseId, month);
                DynamicDataSourceContextHolder.push(DataSourceEnums.LENZ_BI_POLAR.getCode());
            }
            List<UnileverAppImageDetail> imageDetailList = unileverAppImageMapper.getList(responseId, shardingKey);

            if (CollectionUtils.isEmpty(imageDetailList)) {
                log.info("【查询BI识别结果集为空!】, responseId:{}", responseId);
                return apiResultDTO;
            }

            // 查询识别出所有SKU信息
//        LambdaQueryWrapper<UnileverAppProductDetail> productWrapper = new LambdaQueryWrapper<>();
//        productWrapper.eq(UnileverAppProductDetail::getResponseId, responseId);
//        List<UnileverAppProductDetail> productDetailList = unileverAppProductMapper.selectList(productWrapper);
            List<UnileverAppProductDetail> productDetailList = unileverAppProductMapper.getList(responseId, shardingKey);

            if (CollectionUtils.isEmpty(productDetailList)) {
                log.info("【查询BI识别明细表结果集为空!】, responseId:{}", responseId);
                return apiResultDTO;
            }

            // 根据ImageId分组
            Map<String, List<UnileverAppProductDetail>> productMap = productDetailList.stream().collect(Collectors.groupingBy(UnileverAppProductDetail::getImageId));

            // 构建识别明细
            List<ApiImageResultDTO> apiImageResultList = Lists.newArrayList();
            for (UnileverAppImageDetail imageDetail : imageDetailList) {
                ApiImageResultDTO imageResult = new ApiImageResultDTO();
                imageResult.setImageId(imageDetail.getImageId());
                imageResult.setRemake(imageDetail.getRemake());
                imageResult.setRemakeScore(imageDetail.getRemakeScore());
                imageResult.setRepeat(Objects.isNull(imageDetail.getRepeatGroup()) ? RepeatStatusEnum.NOT_REPEAT.getCode() : RepeatStatusEnum.REPEAT.getCode());
                imageResult.setRepeatGroup(imageDetail.getRepeatGroup());

                ArrayList<ApiPatchResultDTO> patchListList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(productMap.get(imageDetail.getImageId()))) {
                    for (UnileverAppProductDetail productDetail : productMap.get(imageDetail.getImageId())) {
                        ApiPatchResultDTO sku = new ApiPatchResultDTO();
                        CoordinateDTO coordinateDTO = new CoordinateDTO();
                        sku.setId(productDetail.getProductId());
                        sku.setSkuCode(productDetail.getCustomCode());
                        sku.setColumn(productDetail.getColumn());
                        sku.setLayer(productDetail.getLayer());
                        sku.setStitchDuplicateStatus(productDetail.getInarge());
                        coordinateDTO.setxMin(productDetail.getXmin());
                        coordinateDTO.setyMin(productDetail.getYmin());
                        coordinateDTO.setxMax(productDetail.getXmax());
                        coordinateDTO.setyMax(productDetail.getYmax());
                        sku.setCoordinate(coordinateDTO);
                        sku.setScene(productDetail.getScene());
                        sku.setPrice(productDetail.getPrice());
                        patchListList.add(sku);
                    }
                }

                imageResult.setPatches(patchListList);
                apiImageResultList.add(imageResult);
            }
            apiResultDTO.setAiResult(apiImageResultList);
        } catch (Exception e) {
            log.error("查询联合利华项目bi结果异常！", e);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
        return apiResultDTO;
    }

    private static boolean isSharding(Integer month1, Integer month2){
        return !month1.equals(month2) && (month2 - month1) != 89 && (month2 - month1) > 1;
    }

}