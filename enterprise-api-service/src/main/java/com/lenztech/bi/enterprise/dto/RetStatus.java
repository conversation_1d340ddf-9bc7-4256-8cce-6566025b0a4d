package com.lenztech.bi.enterprise.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 响应
 * @Author: z<PERSON><PERSON>e
 * @Date: 16/8/18 AM9:43
 */
@ApiModel("响应状态")
public class RetStatus {

    @ApiModelProperty("响应状态码：0成功；1参数错误；2服务器异常")
    private String retCode;

    @ApiModelProperty("响应消息")
    private String errMsg;


    public String getRetCode() {
        return retCode;
    }
    public void setRetCode(String retCode) {
        this.retCode = retCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public static RetStatus builder(){
        return new RetStatus();
    }

    public  RetStatus retCode(String retCode) {
        this.retCode = retCode;
        return this;
    }

    public  RetStatus ok() {
        this.retCode = IStatusMessage.SystemStatus.SUCCESS.getCode();
        this.errMsg = IStatusMessage.SystemStatus.SUCCESS.getMessage();
        return this;
    }

    public  RetStatus errMsg(String errMsg) {
        this.errMsg = errMsg;
        return this;
    }

    public  RetStatus status(IStatusMessage.SystemStatus systemStatus) {
        this.retCode = systemStatus.getCode();
        this.errMsg = systemStatus.getMessage();
        return this;
    }
}
