package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.entity.TImageProductTree;
import com.lenztech.bi.enterprise.mapper.task.TImageProductTreeMapper;
import com.lenztech.bi.enterprise.service.ITImageProductTreeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 图像模型树形结构 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-28
 */
@Service
public class TImageProductTreeServiceImpl extends ServiceImpl<TImageProductTreeMapper, TImageProductTree> implements ITImageProductTreeService {

}
