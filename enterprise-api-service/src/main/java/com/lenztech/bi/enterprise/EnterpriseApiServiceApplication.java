package com.lenztech.bi.enterprise;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@MapperScan(basePackages = "com.lenztech.bi.enterprise.mapper")
@EnableDiscoveryClient
@EnableFeignClients(basePackages ={"com.trax.lenz"})
@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class, SecurityAutoConfiguration.class, UserDetailsServiceAutoConfiguration.class})
public class EnterpriseApiServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(EnterpriseApiServiceApplication.class, args);
    }


}

