package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 魔爪品牌分销率
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-18
 */
public class MonsterDistributionByBrand extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 编号
     */
    private Integer noId;

    /**
     * 时间段
     */
    private String period;

    /**
     * BG
     */
    private String bg;

    /**
     * 城市
     */
    private String city;

    private String channel;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 分销率
     */
    private String distribution;

    /**
     * VS YA
     */
    private String vsYa;

    /**
     * VS PP
     */
    private String vsPp;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getNoId() {
        return noId;
    }

    public void setNoId(Integer noId) {
        this.noId = noId;
    }
    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }
    public String getBg() {
        return bg;
    }

    public void setBg(String bg) {
        this.bg = bg;
    }
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }
    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }
    public String getDistribution() {
        return distribution;
    }

    public void setDistribution(String distribution) {
        this.distribution = distribution;
    }

    public String getVsYa() {
        return vsYa;
    }

    public void setVsYa(String vsYa) {
        this.vsYa = vsYa;
    }

    public String getVsPp() {
        return vsPp;
    }

    public void setVsPp(String vsPp) {
        this.vsPp = vsPp;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "MonsterDistributionByBrand{" +
        "id=" + id +
        ", noId=" + noId +
        ", period=" + period +
        ", bg=" + bg +
        ", city=" + city +
        ", channel=" + channel +
        ", brand=" + brand +
        ", distribution=" + distribution +

        ", vsPp=" + vsPp +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
