package com.lenztech.bi.enterprise.controller;


import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.bi.MonsterBiResultResp;
import com.lenztech.bi.enterprise.service.MonsterBiResultService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 魔爪bi结果
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-11
 */
@RestController
@RequestMapping("/biResult/monster/")
public class MonsterBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(MonsterBiResultController.class);

    @Autowired
    private MonsterBiResultService monsterBiResultService;

    /**
     * 获取魔爪bi识别结果
     * @param responseIdList
     * @return
     */
    @PostMapping(value = "getReport")
    @ControllerAnnotation(use = "获取魔爪bi识别结果")
    public List<MonsterBiResultResp> getReport(@RequestBody List<String> responseIdList) {
        try {
            return monsterBiResultService.getReport(responseIdList);
        } catch (Exception e) {
            logger.error("/getReport========", e);
        }
        return new ArrayList<>();
    }

}