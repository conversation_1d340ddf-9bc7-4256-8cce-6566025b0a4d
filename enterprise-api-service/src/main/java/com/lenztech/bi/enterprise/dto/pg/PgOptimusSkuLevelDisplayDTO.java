package com.lenztech.bi.enterprise.dto.pg;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * SKU级别二陈数据DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
public class PgOptimusSkuLevelDisplayDTO {

    /**
     * 客户名称
     */
    private String banner;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店类型
     */
    private String storeType;
    
    /**
     * 执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date execDate;

    /**
     * 制造商
     */
    private String manufacture;

    /**
     * 品类名称
     */
    private String category;

    /**
     * 品牌名称
     */
    private String brand;

    /**
     * 产品形态
     */
    private String productForm;

    /**
     * 产品系列
     */
    private String lineUp;

    /**
     * 产品形式
     */
    private String productVariant;

    /**
     * EAN产品编码 69码
     */
    private String eanCode;

    /**
     * 产品名称
     */
    private String skuName;

    /**
     * 是否是宝洁，0非宝洁/1宝洁
     */
    private Boolean isPgProductFlag;

    /**
     * 拜访日期时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date visitDatetime;

    /**
     * 拜访第n次/月
     */
    private String visitCycle;

    /**
     * 拜访年份
     */
    private String visitYear;

    /**
     * 拜访月份
     */
    private String visitMonth;

    /**
     * 拜访周数
     */
    private String visitWeek;

    /**
     * group_id
     */
    private String responseGroupId;

    /**
     * rid
     */
    private String responseId;

    /**
     * display类型
     */
    private String displayType;

    /**
     * dispaly堆叠的面位数
     */
    private BigDecimal displayStackFacing;

    /**
     * display面积
     */
    private BigDecimal displaySize;

    /**
     * display个数
     */
    private BigDecimal displayCnt;

    /**
     * dispaly by category 堆叠的面位数
     */
    private BigDecimal displayStackFacingCat;

    /**
     * display by category 面积
     */
    private BigDecimal displaySizeCat;

    /**
     * dispaly by category/form 堆叠的面位数
     */
    private BigDecimal displayStackFacingForm;

    /**
     * display by category/form 面积
     */
    private BigDecimal displaySizeForm;


}
