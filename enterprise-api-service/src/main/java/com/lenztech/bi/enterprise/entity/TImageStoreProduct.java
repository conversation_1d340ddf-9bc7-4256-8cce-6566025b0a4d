package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;

import java.time.LocalDateTime;

/**
 * <p>
 * ??sku??
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public class TImageStoreProduct extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * ???
     */
    private String productName;

    /**
     * ??id
     */
    private Integer productId;

    /**
     * ????
     */
    private Integer productCount;

    /**
     * ????id
     */
    private String responseId;

    /**
     * ????
     */
    private Integer faceing;

    /**
     * ???????
     */
    private Integer faceingReview;

    /**
     * ????
     */
    private LocalDateTime createTime;

    /**
     * ??????
     */
    private LocalDateTime updateTime;

    /**
     * ??????????
     */
    private Integer appealProductCount;

    /**
     * 鐢宠堪鍚堟牸鍚庣殑鎷嶉潰鏁
     */
    private Double appealFaceing;

    /**
     * ????(0?, 1?)
     */
    private Integer isartificial;

    /**
     * ????
     */
    private Integer isExist;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }
    public Integer getProductCount() {
        return productCount;
    }

    public void setProductCount(Integer productCount) {
        this.productCount = productCount;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public Integer getFaceing() {
        return faceing;
    }

    public void setFaceing(Integer faceing) {
        this.faceing = faceing;
    }
    public Integer getFaceingReview() {
        return faceingReview;
    }

    public void setFaceingReview(Integer faceingReview) {
        this.faceingReview = faceingReview;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public Integer getAppealProductCount() {
        return appealProductCount;
    }

    public void setAppealProductCount(Integer appealProductCount) {
        this.appealProductCount = appealProductCount;
    }
    public Double getAppealFaceing() {
        return appealFaceing;
    }

    public void setAppealFaceing(Double appealFaceing) {
        this.appealFaceing = appealFaceing;
    }
    public Integer getIsartificial() {
        return isartificial;
    }

    public void setIsartificial(Integer isartificial) {
        this.isartificial = isartificial;
    }
    public Integer getIsExist() {
        return isExist;
    }

    public void setIsExist(Integer isExist) {
        this.isExist = isExist;
    }

    @Override
    public String toString() {
        return "TImageStoreProduct{" +
        "id=" + id +
        ", productName=" + productName +
        ", productId=" + productId +
        ", productCount=" + productCount +
        ", responseId=" + responseId +
        ", faceing=" + faceing +
        ", faceingReview=" + faceingReview +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", appealProductCount=" + appealProductCount +
        ", appealFaceing=" + appealFaceing +
        ", isartificial=" + isartificial +
        ", isExist=" + isExist +
        "}";
    }
}
