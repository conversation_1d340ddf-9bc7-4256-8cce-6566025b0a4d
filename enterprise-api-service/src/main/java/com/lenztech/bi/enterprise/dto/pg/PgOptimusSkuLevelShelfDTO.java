package com.lenztech.bi.enterprise.dto.pg;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * SKU级别货架数据DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
public class PgOptimusSkuLevelShelfDTO {

    /**
     * 客户名称
     */
    private String banner;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店类型
     */
    private String storeType;
    
    /**
     * 执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date execDate;

    /**
     * 制造商
     */
    private String manufacture;

    /**
     * 品类名称
     */
    private String category;

    /**
     * 品牌名称
     */
    private String brand;

    /**
     * 产品形态
     */
    private String productForm;

    /**
     * 产品系列
     */
    private String lineUp;

    /**
     * 产品形式
     */
    private String productVariant;

    /**
     * EAN产品编码 69码
     */
    private String eanCode;

    /**
     * 产品名称
     */
    private String skuName;

    /**
     * 是否是宝洁，0非宝洁/1宝洁
     */
    private Boolean isPgProductFlag;

    /**
     * 拜访日期时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date visitDatetime;

    /**
     * 拜访第n次/月
     */
    private String visitCycle;

    /**
     * 拜访年份
     */
    private String visitYear;

    /**
     * 拜访月份
     */
    private String visitMonth;

    /**
     * 拜访周数
     */
    private String visitWeek;

    /**
     * group_id
     */
    private String responseGroupId;

    /**
     * rid
     */
    private String responseId;

    /**
     * 面位数据
     */
    private BigDecimal skuFacing;

    /**
     * 堆叠的面位数
     */
    private BigDecimal skuStackFacing;

    /**
     * Bay# 组数
     */
    private BigDecimal skuBay;

    /**
     * SKU 长度
     */
    private BigDecimal skuLength;

    /**
     * category total的面位
     */
    private BigDecimal totalFacingCat;

    /**
     * category total的堆叠面位
     */
    private BigDecimal totalStackFacingCat;

    /**
     * category total的bay
     */
    private BigDecimal totalBayCat;

    /**
     * category total的长度
     */
    private BigDecimal totalLengthCat;

    /**
     * category/form total的面位
     */
    private BigDecimal totalFacingForm;

    /**
     * category/form total的堆叠面位
     */
    private BigDecimal totalStackFacingForm;

    /**
     * category/form total的bay
     */
    private BigDecimal totalBayForm;

    /**
     * category/form total的长度
     */
    private BigDecimal totalLengthForm;

}
