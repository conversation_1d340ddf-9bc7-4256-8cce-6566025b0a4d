package com.lenztech.bi.enterprise.entity;

import com.lenztech.bi.enterprise.entity.base.BaseEntity;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
public class UnileverStoreinfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 月份
     */
    private String month;

    private String companyCode;

    /**
     * 门店编号/storeCode
     */
    private String storeCode;

    /**
     * 区域/area
     */
    private String area;

    /**
     * 省/province
     */
    private String province;

    private String cluster;

    /**
     * 市/city
     */
    private String pcity;

    private String cityCode;

    private String city;

    private String cityLevel;

    private String townCode;

    private String town;

    /**
     * 门店名称/storeName
     */
    private String storeName;

    /**
     * 客户类型/customerType
     */
    private String customerType;

    /**
     * 客户名称/customerName
     */
    private String customerName;

    /**
     * 渠道/channelName
     */
    private String channelName;

    private String subChannel;

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }
    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }
    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }
    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }
    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }
    public String getCluster() {
        return cluster;
    }

    public void setCluster(String cluster) {
        this.cluster = cluster;
    }
    public String getPcity() {
        return pcity;
    }

    public void setPcity(String pcity) {
        this.pcity = pcity;
    }
    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }
    public String getCityLevel() {
        return cityLevel;
    }

    public void setCityLevel(String cityLevel) {
        this.cityLevel = cityLevel;
    }
    public String getTownCode() {
        return townCode;
    }

    public void setTownCode(String townCode) {
        this.townCode = townCode;
    }
    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }
    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }
    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }
    public String getSubChannel() {
        return subChannel;
    }

    public void setSubChannel(String subChannel) {
        this.subChannel = subChannel;
    }

    @Override
    public String toString() {
        return "UnileverStoreinfo{" +
        "month=" + month +
        ", companyCode=" + companyCode +
        ", storeCode=" + storeCode +
        ", area=" + area +
        ", province=" + province +
        ", cluster=" + cluster +
        ", pcity=" + pcity +
        ", cityCode=" + cityCode +
        ", city=" + city +
        ", cityLevel=" + cityLevel +
        ", townCode=" + townCode +
        ", town=" + town +
        ", storeName=" + storeName +
        ", customerType=" + customerType +
        ", customerName=" + customerName +
        ", channelName=" + channelName +
        ", subChannel=" + subChannel +
        "}";
    }
}
