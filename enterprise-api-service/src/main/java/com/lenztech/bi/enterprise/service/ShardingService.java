package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.utils.StringUtil;
import com.trax.lenz.common.core.id.SnowFlakeFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ShardingService {

    private final SnowFlakeFactory snowFlakeFactory;

    @Autowired
    public ShardingService(SnowFlakeFactory snowFlakeFactory) {
        this.snowFlakeFactory = snowFlakeFactory;
    }

    /**
     * 查询shardingKey
     * 当effectiveDay为空时，返回空，则不分表
     *
     * @param responseId
     * @param effectiveDay
     * @return String
     */
    public String getShardingKey(String responseId, String effectiveDay) {
        String shardingMonth = "";
        try {
            if (StringUtil.isNotBlank(effectiveDay)) {
                String day = snowFlakeFactory.getDateMothDay(responseId);
                if (StringUtil.isNotBlank(effectiveDay) && StringUtil.isNotBlank(day) && Long.parseLong(day) >= Long.parseLong(effectiveDay)) {
                    shardingMonth = "_" + day.substring(0, 6);
                }
            }
        } catch (Exception e) {
            log.error("sharding异常！responseId={}", responseId, e);
        }
        return shardingMonth;
    }
}
