//package com.lenztech.bi.enterprise.controller;
//
//import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.service.RecognitionDoneService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.io.IOException;
//
///**
// * @Description:BI报表重构设置
// * @Author: zhangjie
// * @Date: 3/18/20 AM10:30
// */
//@RestController
//@RequestMapping("/recognition")
//public class RecognitionDoneController {
//
//    @Autowired
//    private RecognitionDoneService recognitionDoneService;
//
//    /**
//     * 识别完成对外接口
//     *
//     * @param responseId
//     * @return
//     */
//    @RequestMapping(value = "/recognitionDone", method = RequestMethod.GET)
//    @ControllerAnnotation(use = "识别完成对外接口")
//    public ResponseData recognitionDone(String responseId) throws IOException {
//        if (responseId == null){
//            return ResponseData.failure();
//        }
//        recognitionDoneService.recognitionDone(responseId);
//        return ResponseData.success();
//    }
//
//
//}
