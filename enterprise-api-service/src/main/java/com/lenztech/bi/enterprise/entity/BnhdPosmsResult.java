package com.lenztech.bi.enterprise.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-23
 */
public class BnhdPosmsResult extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * rid
     */
    private String responseId;

    /**
     * POSM名称
     */
    private String posmName;

    /**
     * 有无(1/0)
     */
    private Integer exist;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public String getPosmName() {
        return posmName;
    }

    public void setPosmName(String posmName) {
        this.posmName = posmName;
    }
    public Integer getExist() {
        return exist;
    }

    public void setExist(Integer exist) {
        this.exist = exist;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BnhdPosmsResult{" +
        "id=" + id +
        ", responseId=" + responseId +
        ", posmName=" + posmName +
        ", exist=" + exist +
        ", updateTime=" + updateTime +
        "}";
    }
}
