//package com.lenztech.bi.enterprise.controller;
//
//import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
//import com.lenztech.bi.enterprise.dto.CompanyNameInfoResp;
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.service.ICompanyService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RestController;
//
//@RestController
//@RequestMapping("/biResult/company")
//public class CompanyController {
//
//    @Autowired
//    private ICompanyService iCompanyService;
//
//    /**
//     * 获取公司信息
//     *
//     * @return
//     */
//    @RequestMapping(value = "/getCompanyNameInfo", method = RequestMethod.GET)
//    @ControllerAnnotation(use = "获取公司信息")
//    public ResponseData getCompanyNameInfo() {
//        CompanyNameInfoResp resp = iCompanyService.getCompanyNameInfo();
//        return ResponseData.success(resp);
//    }
//}
