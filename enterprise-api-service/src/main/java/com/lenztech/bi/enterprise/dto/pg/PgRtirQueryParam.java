package com.lenztech.bi.enterprise.dto.pg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * PG RTIR 查询参数
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel("PG RTIR 查询参数")
public class PgRtirQueryParam {

    /**
     * GTIN编码
     */
    @ApiModelProperty("GTIN编码")
    private String gtinCode;

    /**
     * 品类编码
     */
    @ApiModelProperty("品类编码")
    private String categoryCode;

    /**
     * 品类名称
     */
    @ApiModelProperty("品类名称")
    private String categoryCn;

    /**
     * 品牌编码
     */
    @ApiModelProperty("品牌编码")
    private String brandCode;

    /**
     * 品牌名称
     */
    @ApiModelProperty("品牌名称")
    private String brandCn;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String productNameCn;

    /**
     * 是否本品（1是本品，0是竞品）
     */
    @ApiModelProperty("是否本品（1是本品，0是竞品）")
    private String isPgProduct;

    /**
     * 是否建模（1代表已建模）
     */
    @ApiModelProperty("是否建模（1代表已建模）")
    private String modeling;

    /**
     * 是否POSM（1代表是，0代表否）
     */
    @ApiModelProperty("是否POSM（1代表是，0代表否）")
    private String isPosm;

    /**
     * 页码
     */
    @ApiModelProperty("页码")
    private Integer pageNum;

    /**
     * 每页大小
     */
    @ApiModelProperty("每页大小")
    private Integer pageSize;
}
