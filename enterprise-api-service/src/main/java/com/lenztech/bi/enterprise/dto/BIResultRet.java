package com.lenztech.bi.enterprise.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Description: BI结果响应
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 28/12/18 下午2:14
 */
public class BIResultRet {

    private CustMessage custMessage;
    private BIResult BIResult;

    public CustMessage getCustMessage() {
        return custMessage;
    }

    public void setCustMessage(CustMessage custMessage) {
        this.custMessage = custMessage;
    }

    @JsonProperty("BIResult")
    public BIResult getBIResult() {
        return BIResult;
    }

    public void setBIResult(BIResult BIResult) {
        this.BIResult = BIResult;
    }
}
