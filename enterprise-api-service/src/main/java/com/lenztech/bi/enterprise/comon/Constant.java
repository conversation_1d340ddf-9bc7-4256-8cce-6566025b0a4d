package com.lenztech.bi.enterprise.comon;

/**
 * @description:系统常量类
 * <AUTHOR>
 */
public class Constant {

    /**
     * 合作商业务字段分隔符
     */
    public static final String DATA_SEPARATOR = "_lenz_";

    /**
     * 申诉名称默认前缀
     */
    public static final String APPEAL_NAME_PREFIX = "SDK申诉-";
    /**
     * 申诉默认进度
     */
    public static final Integer APPEAL_PROGRESS_DEFAULT = 1;
    /**
     * 申诉类型：图像识别
     */
    public static final int APPEAL_SHENSU_TYPE_IMAGE_RECOGNITION = 1;
    /**
     * 申诉审核状态：审核中
     */
    public static final int APPEAL_CHECKSTATE = 0;

    /**
     * 申诉类型：0分销 1面位
     */
    public static final int QUESTION_TYPE_EXIST = 0;
    /**
     * 申诉类型：0分销 1面位
     */
    public static final int QUESTION_TYPE_FACING = 1;

    /**
     * 空值
     */
    public static final String VALUE_BLANK ="";

    /**
     * 换行
     */
    public static final String VALUE_NEWLINE = "\r\n";

    /**
     * ip
     */
    public static final String KEY_IP ="ip";

    /**
     * 消耗时间
     */
    public static final String KEY_TIMEELAPSED ="time";

    /**
     * http访问路径
     */
    public static final String KEY_URL_PATH ="url_path_name";

    /**
     * 访问路径
     */
    public static final String KEY_URL_PATH_THRID ="url_name";

    /**
     * traceId
     */
    public static final String HEADER_TRACE_ID ="traceId";

    /**
     * responseId替换
     */
    public static final String REPLACER_RESPONSE_ID ="{responseId}";

    /**
     * taskId替换
     */
    public static final String REPLACER_TASK_ID ="{taskId}";

    /**
     * account替换
     */
    public static final String REPLACER_ACCOUNT ="{account}";

    /**
     * 青啤公司名称
     */
    public static final String QINGPI_FIRM = "Tsingtao";

    /**
     * 系统名称美宜佳
     */
    public static final String SYSTEM_NAME_MEIYIJIA = "美宜佳";

    /**
     * 横线
     */
    public static final String LINE_HORIZ = "-";

    /**
     * 其他sku
     */
    public static final String OTHER = "其他";

    /**
     * 其他sku编码
     */
    public static final String OTHER_CODE = "999999";
}
