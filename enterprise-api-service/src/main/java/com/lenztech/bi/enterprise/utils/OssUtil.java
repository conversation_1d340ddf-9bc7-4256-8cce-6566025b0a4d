package com.lenztech.bi.enterprise.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

public class OssUtil {

    private static String endpoint = "http://oss-cn-beijing.aliyuncs.com";
    public static String accessKeyId = "LTAI5tPYkxJkgNQzuW4wT5qz";
    public static String accessKeySecret = "******************************";
    public static String bucketName = "bi-excel-download";

    public static void downloadFile(String objectKey, String localFilePath) {

        // 创建 OSSClient 实例
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        // 创建文件夹
        File dir = new File(localFilePath.substring(0, localFilePath.lastIndexOf('/')));
        if (!dir.exists()) {
            dir.mkdirs();
        }

        // 下载文件到本地
        OSSObject ossObject = ossClient.getObject(bucketName, objectKey);
        InputStream inputStream = ossObject.getObjectContent();
        FileOutputStream outputStream = null;

        try {
            outputStream = new FileOutputStream(localFilePath);
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            System.out.println("File downloaded: " + objectKey);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            // 关闭 OSSClient
            ossClient.shutdown();
        }
    }

    /**
     * 获取目录下所有文件
     * @param directoryPrefix
     * @return
     */
    public static List<String> listFilesInDirectory(String directoryPrefix) {
        List<String> fileKeys = new ArrayList<>();

        // 创建 OSSClient 实例
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        // 分页列出目录下的所有对象
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName)
                .withPrefix(directoryPrefix + "/");
//                .withDelimiter("/");

        ObjectListing objectListing = ossClient.listObjects(listObjectsRequest);

        // 获取所有文件的objectKey列表
        List<OSSObjectSummary> summaries = objectListing.getObjectSummaries();

        for (OSSObjectSummary summary : summaries) {
            String objectKey = summary.getKey();
            fileKeys.add(objectKey);
        }

        // 关闭 OSSClient
        ossClient.shutdown();

        return fileKeys;
    }
}
