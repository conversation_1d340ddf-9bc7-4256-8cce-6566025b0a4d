package com.lenztech.bi.enterprise.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * Brand级别二陈数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
public class PgOptimusBrandLevelDisplayCount extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 执行时间
     */
    private Date execDate;

    /**
     * 客户名称
     */
    private String banner;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店类型
     */
    private String storeType;

    /**
     * group_id
     */
    private String responseGroupId;

    /**
     * rid
     */
    private String responseId;

    /**
     * 品牌名称
     */
    private String brand;

    /**
     * 拜访日期时间
     */
    private LocalDateTime visitDatetime;

    /**
     * 拜访第n次/月
     */
    private String visitCycle;

    /**
     * 拜访年份
     */
    private String visitYear;

    /**
     * 拜访月份
     */
    private String visitMonth;

    /**
     * 拜访周数
     */
    private String visitWeek;

    /**
     * display类型
     */
    private String displayType;

    /**
     * dispaly堆叠的面位数
     */
    private Integer displayStackFacing;

    /**
     * display个数
     */
    private Integer displayCnt;

    /**
     * ByBrand所有堆总的个数
     */
    private Integer totalDisplayCountBrand;

    /**
     * display占比
     */
    private BigDecimal displayCountShare;

    /**
     * 数据是否被抽取过，抽取次数
     */
    private Integer extract;

    /**
     * 最后一次抽取时间
     */
    private LocalDateTime lastExtractTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Date getExecDate() {
        return execDate;
    }

    public void setExecDate(Date execDate) {
        this.execDate = execDate;
    }

    public String getBanner() {
        return banner;
    }

    public void setBanner(String banner) {
        this.banner = banner;
    }
    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }
    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }
    public String getResponseGroupId() {
        return responseGroupId;
    }

    public void setResponseGroupId(String responseGroupId) {
        this.responseGroupId = responseGroupId;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }
    public LocalDateTime getVisitDatetime() {
        return visitDatetime;
    }

    public void setVisitDatetime(LocalDateTime visitDatetime) {
        this.visitDatetime = visitDatetime;
    }
    public String getVisitCycle() {
        return visitCycle;
    }

    public void setVisitCycle(String visitCycle) {
        this.visitCycle = visitCycle;
    }
    public String getVisitYear() {
        return visitYear;
    }

    public void setVisitYear(String visitYear) {
        this.visitYear = visitYear;
    }
    public String getVisitMonth() {
        return visitMonth;
    }

    public void setVisitMonth(String visitMonth) {
        this.visitMonth = visitMonth;
    }
    public String getVisitWeek() {
        return visitWeek;
    }

    public void setVisitWeek(String visitWeek) {
        this.visitWeek = visitWeek;
    }
    public String getDisplayType() {
        return displayType;
    }

    public void setDisplayType(String displayType) {
        this.displayType = displayType;
    }
    public Integer getDisplayStackFacing() {
        return displayStackFacing;
    }

    public void setDisplayStackFacing(Integer displayStackFacing) {
        this.displayStackFacing = displayStackFacing;
    }
    public Integer getDisplayCnt() {
        return displayCnt;
    }

    public void setDisplayCnt(Integer displayCnt) {
        this.displayCnt = displayCnt;
    }
    public Integer getTotalDisplayCountBrand() {
        return totalDisplayCountBrand;
    }

    public void setTotalDisplayCountBrand(Integer totalDisplayCountBrand) {
        this.totalDisplayCountBrand = totalDisplayCountBrand;
    }
    public BigDecimal getDisplayCountShare() {
        return displayCountShare;
    }

    public void setDisplayCountShare(BigDecimal displayCountShare) {
        this.displayCountShare = displayCountShare;
    }
    public Integer getExtract() {
        return extract;
    }

    public void setExtract(Integer extract) {
        this.extract = extract;
    }
    public LocalDateTime getLastExtractTime() {
        return lastExtractTime;
    }

    public void setLastExtractTime(LocalDateTime lastExtractTime) {
        this.lastExtractTime = lastExtractTime;
    }

    @Override
    public String toString() {
        return "PgOptimusBrandLevelDisplayCount{" +
            "id=" + id +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", execDate=" + execDate +
            ", banner=" + banner +
            ", storeCode=" + storeCode +
            ", storeType=" + storeType +
            ", responseGroupId=" + responseGroupId +
            ", responseId=" + responseId +
            ", brand=" + brand +
            ", visitDatetime=" + visitDatetime +
            ", visitCycle=" + visitCycle +
            ", visitYear=" + visitYear +
            ", visitMonth=" + visitMonth +
            ", visitWeek=" + visitWeek +
            ", displayType=" + displayType +
            ", displayStackFacing=" + displayStackFacing +
            ", displayCnt=" + displayCnt +
            ", totalDisplayCountBrand=" + totalDisplayCountBrand +
            ", displayCountShare=" + displayCountShare +
            ", extract=" + extract +
            ", lastExtractTime=" + lastExtractTime +
        "}";
    }
}
