package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.service.impl.PgDpbReportService;
import com.lenztech.bi.enterprise.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created with IntelliJ IDEA. User: sunqingyuan Date: 2021/06/22 Time: 17:19 类功能:
 * 宝洁dpb结果数据，结果需传到微软Azure Blob
 */
@RestController
@RequestMapping("/biResult/pgdpb/")
public class PgDpbBiResultController {

  public static final Logger logger = LoggerFactory.getLogger(PgDpbBiResultController.class);

  @Autowired private PgDpbReportService pgDpbReportService;

  ExecutorService executorService = Executors.newFixedThreadPool(1);

  /**
   * 获取并推送PgDpb FMOT结果
   *
   * @param searchDate
   * @return
   */
  @RequestMapping(value = "getPgDpbBiResult", method = RequestMethod.GET)
  @ControllerAnnotation(use = "报表-获取PgDpb结果")
  public void getPgDpbBiResult(String searchDate, String addressIDnum) {
    try {
      Runnable rn = new Runnable() {//异步处理数据
        public void run() {
          pgDpbReportService.getPgDpbBiResult(searchDate, addressIDnum);
        }
      };
      executorService.execute(rn);
      //pgDpbReportService.getPgDpbBiResult(searchDate, addressIDnum);

    } catch (Exception e) {
      logger.error("/getPgDpbBiResult========", e);
    }
  }

  /**
   * 获取并推送PgDpb价格结果
   *
   * @param searchDate
   * @return
   */
  @RequestMapping(value = "getPgDpbPriceBiResult", method = RequestMethod.GET)
  @ControllerAnnotation(use = "报表-获取PgDpb价格结果")
  public void getPgDpbPriceBiResult(String searchDate, String addressIDnum) {
    try {
      Runnable rn = new Runnable() {//异步处理数据
        public void run() {
          pgDpbReportService.getPgDpbPriceBiResult(searchDate, addressIDnum);
        }
      };
      executorService.execute(rn);
      //pgDpbReportService.getPgDpbBiResult(searchDate, addressIDnum);

    } catch (Exception e) {
      logger.error("/getPgDpbBiResult========", e);
    }
  }

  /**
   * 处理指定门店 临时用
   *
   * @param
   * @return
   */
  @RequestMapping(value = "processDesignatedStores", method = RequestMethod.GET)
  @ControllerAnnotation(use = "处理指定门店")
  public void processDesignatedStores() {
    try {
      // String[] addressIDnumArray =
      // {"H_1004101","H_1004906","H_1016613","H_10226446","H_10377847","H_10500735","H_10556834","H_10568514","H_10570707","H_10587574","H_10587575","H_10588978","H_10593956","H_10620860","H_10640397","H_10700608","H_1414980","H_1910463","H_1953667","H_2106223","H_2212916","H_2408072","H_2450523","H_2523487","H_257743","H_2710816","H_2915792","H_3087309","H_3087311","H_3087318","H_3087335","H_3092674","H_3092688","H_3116562","H_3348597","H_3381813","H_3425652","H_604075296","H_604081390","H_604093258","H_604093623","H_604095193","H_634324","L_10003753","L_10003755","L_10031014","L_10031304","L_1004043","L_10045381","L_10063516","L_10063517","L_10063521","L_10065309","L_10072898","L_10130906","L_10135594","L_10139444","L_10152405","L_1015832","L_10159237","L_1016614","L_10169338","L_10171213","L_10176802","L_10186580","L_10187257","L_10197227","L_10203815","L_10237848","L_10258881","L_10262261","L_10271679","L_10275121","L_10291424","L_10294910","L_10299866","L_10304053","L_10322965","L_10324602","L_10328431","L_10336981","L_10338503","L_10352237","L_10354111","L_10354112","L_10360948","L_10366282","L_10375813","L_10377849","L_10377852","L_10377865","L_10394438","L_10401237","L_10404171","L_10413984","L_10417383","L_10418142","L_10441694","L_10443631","L_10454940","L_10461907","L_10470000","L_10471680","L_10475652","L_10535155","L_10535255","L_10536516","L_10536605","L_10536733","L_10536736","L_10537280","L_10537281","L_10537282","L_10548907","L_10549340","L_10553877","L_10556326","L_10556327","L_10556328","L_10556329","L_10568460","L_10568464","L_10568466","L_10569746","L_10569747","L_10574619","L_10582793","L_10584585","L_10585302","L_10587512","L_10587513","L_10587515","L_10587518","L_10591392","L_10610527","L_10620460","L_10638901","L_10638903","L_10638904","L_10638913","L_10642446","L_10662570","L_10662581","L_10664580","L_10669521","L_10677798","L_10693675","L_10693676","L_10693677","L_10693678","L_10693707","L_10693709","L_10695849","L_10698593","L_10700124","L_10700624","L_10700643","L_1110821","L_1116603","L_120019","L_1311068","L_1375553","L_1375554","L_1408536","L_1438731","L_144208","L_1449137","L_1456024","L_1457334","L_1458150","L_1488667","L_1489689","L_162137","L_1702503","L_1749857","L_1801018","L_185307","L_1921597","L_1925900","L_1955764","L_200855","L_2213055","L_2267244","L_2471649","L_2514501","L_2534505","L_2556643","L_2606761","L_2623275","L_2720449","L_2771286","L_2771425","L_2771708","L_2780362","L_2941778","L_2977890","L_2978134","L_298416","L_298419","L_3010255","L_3018610","L_3027104","L_3037311","L_3046937","L_3047819","L_3084538","L_3084539","L_3085610","L_3087435","L_3092681","L_3100287","L_3100342","L_3121317","L_3132810","L_3170934","L_3174321","L_3175134","L_3184811","L_3187329","L_3214506","L_321541","L_3222590","L_3222679","L_3230769","L_3240778","L_3246762","L_3266388","L_327734","L_3303627","L_3313955","L_3315946","L_3318037","L_3364748","L_3372091","L_3374654","L_3379820","L_3392801","L_3404743","L_3406257","L_3413973","L_3429992","L_3440270","L_3454494","L_3454887","L_3455683","L_431606","L_467313","L_549280","L_550404","L_55450","L_594157","L_602693827","L_602694085","L_604062259","L_604071057","L_604074083","L_604079229","L_604085628","L_604086033","L_604086496","L_604087144","L_604087300","L_604091855","L_604091964","L_604092079","L_604092088","L_604092376","L_604092576","L_604093123","L_604093162","L_604093434","L_604093523","L_604093784","L_604093896","L_604093899","L_604094336","L_604094337","L_604096952","L_604100712","L_604100812","L_604100874","L_604103963","L_604106280","L_604107513","L_604111648","L_604112426","L_604114569","L_634321","L_976274","P_10457906","P_10461805","P_10552893","P_10565086","P_10653562","P_1195827","P_938427","S_10001117","S_10002496","S_1000859","S_10031027","S_10031095","S_10031282","S_10031283","S_10031291","S_10031892","S_1004130","S_1004132","S_1004133","S_1004135","S_1004154","S_10046711","S_10046732","S_1004881","S_1004916","S_10052904","S_10052905","S_10053903","S_1005601","S_10063069","S_10063519","S_10063978","S_10064200","S_10064209","S_10064379","S_10065703","S_10065707","S_100733","S_10073577","S_10073579","S_10073934","S_10073944","S_10116881","S_10119117","S_10123899","S_10124058","S_10131279","S_10131435","S_10139440","S_10145700","S_10148792","S_10154532","S_10156412","S_10156414","S_10165187","S_10166838","S_10168601","S_10187804","S_10191411","S_10191702","S_10191704","S_10192839","S_10193121","S_10193551","S_10197226","S_10210763","S_10226441","S_10226558","S_10227128","S_10233499","S_10233527","S_10249219","S_10257572","S_10260387","S_10261282","S_10262011","S_10271467","S_10272767","S_10272768","S_10272775","S_10272779","S_10272782","S_10272783","S_10272784","S_10272787","S_10274136","S_10275554","S_10276420","S_10276475","S_10277732","S_10277747","S_10284052","S_10284476","S_10287288","S_10288222","S_10290625","S_10290628","S_10290722","S_10290748","S_10291381","S_10291962","S_10291996","S_10297727","S_10298910","S_10299758","S_10299943","S_10299945","S_10300026","S_10300027","S_10303894","S_10303895","S_10304315","S_10304317","S_10304319","S_10304321","S_10304324","S_10306815","S_10310929","S_10310930","S_10310933","S_10313457","S_10313458","S_10314031","S_10320322","S_10320785","S_10327994","S_10328250","S_10328375","S_10329236","S_10331562","S_10332991","S_10332994","S_10333003","S_10333589","S_10334797","S_10334960","S_10334964","S_10334978","S_10338298","S_10338498","S_10340645","S_10340646","S_10340649","S_10340651","S_10340654","S_10340657","S_10340662","S_10340664","S_10343225","S_10343227","S_10344447","S_10344448","S_10344475","S_10344841","S_10348288","S_10352616","S_10353882","S_10354549","S_10354551","S_10359956","S_10359957","S_10359958","S_10359960","S_10359966","S_10359969","S_10359970","S_10359972","S_10359975","S_10360228","S_10360691","S_10360692","S_10360694","S_10360696","S_10360698","S_10360699","S_10360947","S_10361504","S_10362883","S_10363246","S_10364796","S_10366476","S_10367547","S_10368870","S_10369267","S_10369393","S_10369863","S_10369867","S_10370465","S_10370466","S_10373573","S_10373574","S_10373785","S_10374051","S_10374052","S_10374453","S_10374886","S_10375063","S_10375427","S_10375811","S_10375812","S_10377818","S_10377829","S_10377832","S_10377846","S_10377891","S_10378610","S_10378722","S_10381731","S_10381732","S_10381817","S_10381861","S_10387412","S_10388033","S_10390051","S_10391387","S_10393262","S_10397820","S_10400311","S_10405353","S_10407075","S_10407458","S_10411495","S_10411871","S_10412054","S_10414832","S_10416258","S_10416624","S_10416772","S_10417477","S_10418831","S_10422124","S_10424193","S_10424295","S_10424862","S_10425090","S_10426807","S_10427732","S_10427876","S_10427885","S_10429549","S_10430454","S_10430477","S_10430777","S_10431587","S_10435206","S_10435857","S_10435917","S_10438864","S_10438872","S_10440857","S_10441470","S_10441473","S_10442738","S_10442742","S_10442769","S_10442790","S_10442864","S_10446017","S_10446872","S_10447115","S_10448488","S_10453864","S_10453870","S_10453871","S_10455626","S_10456693","S_10456796","S_10456867","S_10456876","S_10457446","S_10457771","S_10459277","S_10460280","S_10460442","S_10461932","S_10464034","S_10464044","S_10465499","S_10465919","S_10466079","S_10466087","S_10466128","S_10466365","S_10467229","S_10467778","S_10467887","S_10469376","S_10469403","S_10470203","S_10470296","S_10472920","S_10475055","S_10475569","S_10475920","S_10475981","S_10479061","S_10479355","S_10479928","S_10480092","S_10480473","S_10480693","S_10480777","S_10480908","S_10481466","S_10489534","S_10489842","S_10490365","S_10490804","S_10491660","S_10492556","S_10493617","S_10494137","S_10494146","S_10494865","S_10495080","S_10496162","S_10496525","S_10496552","S_10499900","S_10499916","S_10499917","S_10499918","S_10499919","S_10500732","S_10505692","S_10505890","S_10506260","S_10506261","S_10506369","S_10506370","S_10507550","S_10508638","S_10509103","S_10511259","S_10511548","S_10512147","S_10512519","S_10516764","S_10516794","S_10516797","S_10516969","S_10517774","S_10518734","S_10519449","S_10519877","S_10519882","S_10522279","S_10522615","S_10522990","S_10523160","S_10523417","S_10523605","S_10529248","S_10529270","S_10529853","S_10529870","S_10530055","S_10530251","S_10531053","S_10531783","S_10532729","S_10532814","S_10533010","S_10534710","S_10534951","S_10535124","S_10535438","S_10535502","S_10535506","S_10535507","S_10536368","S_10537110","S_10537264","S_10537345","S_10537351","S_10538311","S_10538473","S_10538803","S_10538837","S_10538862","S_10538912","S_10541071","S_10541126","S_10545153","S_10545302","S_10545304","S_10545476","S_10545563","S_10545944","S_10546671","S_10548545","S_10548679","S_10548952","S_10549000","S_10549395","S_10549651","S_10549954","S_10550232","S_10551254","S_10551255","S_10551295","S_10551844","S_10551878","S_10552213","S_10552255","S_10552346","S_10552911","S_10553130","S_10553157","S_10553601","S_10553654","S_10553705","S_10553706","S_10553709","S_10553713","S_10553718","S_10553740","S_10554004","S_10554008","S_10554084","S_10554128","S_10554611","S_10554996","S_10555063","S_10556339","S_10556351","S_10556354","S_10556356","S_10560638","S_10560641","S_10560642","S_10563404","S_10563464","S_10565159","S_10568447","S_10568453","S_10568467","S_10568474","S_10568481","S_10568482","S_10568483","S_10568485","S_10568497","S_10568498","S_10568499","S_10568502","S_10569765","S_10570677","S_10570679","S_10570684","S_10570686","S_10570689","S_10570690","S_10570691","S_10570695","S_10576873","S_10576874","S_10577212","S_10578010","S_10578247","S_10582827","S_10582843","S_10582865","S_10582871","S_10582884","S_10582906","S_10582925","S_10583422","S_10583462","S_10583505","S_10583660","S_10584010","S_10584066","S_10584067","S_10584214","S_10584677","S_10584693","S_10584756","S_10584767","S_10585147","S_10587041","S_10587268","S_10587502","S_10587538","S_10587540","S_10587542","S_10587545","S_10587547","S_10587554","S_10587566","S_10587567","S_10587897","S_10587899","S_10587900","S_10587903","S_10587960","S_10588284","S_10588285","S_10588344","S_10588848","S_10588972","S_10590454","S_10591683","S_1059374","S_1059388","S_1059391","S_10593933","S_10594776","S_10594777","S_10598859","S_10601294","S_10601461","S_10601462","S_10605321","S_10605323","S_10608218","S_10609296","S_10610508","S_10611217","S_10612479","S_10612897","S_10619469","S_10619478","S_10619483","S_10619488","S_10619490","S_10619494","S_10619497","S_10619526","S_10619530","S_10620480","S_10620859","S_10620955","S_10625451","S_10633897","S_10633898","S_10634078","S_10634241","S_10634541","S_10634661","S_10635850","S_10635864","S_10635893","S_10636484","S_10638922","S_10638924","S_10638926","S_10638927","S_10638929","S_10638938","S_10638939","S_10638957","S_10638962","S_10638985","S_10639006","S_10639008","S_10639009","S_10639012","S_10639019","S_10639024","S_10639025","S_10639029","S_10639047","S_10639048","S_10639051","S_10639056","S_10639057","S_10639952","S_10639959","S_10639963","S_10639966","S_10639967","S_10639971","S_10639976","S_10639977","S_10639980","S_10639982","S_10639983","S_10639984","S_10639985","S_10639986","S_10639987","S_10639989","S_10639992","S_10639996","S_10639997","S_10640000","S_10640001","S_10640003","S_10642029","S_10642038","S_10642102","S_10643324","S_10643729","S_10644081","S_10645195","S_10645556","S_10645613","S_10645618","S_10645789","S_10645796","S_10645797","S_10645801","S_10646765","S_10646766","S_10647366","S_10647404","S_10647617","S_10647805","S_10648331","S_10648346","S_10649484","S_10649487","S_10649488","S_10649489","S_10649490","S_10649494","S_10649497","S_10649499","S_10649502","S_10649688","S_10649689","S_10649694","S_10649695","S_10649697","S_10649886","S_10651947","S_10652428","S_10652604","S_10652766","S_10653031","S_10653125","S_10653558","S_10653559","S_10653778","S_10653780","S_10654489","S_10654616","S_10654617","S_10656153","S_10657630","S_10658281","S_10658350","S_10658515","S_10658516","S_10658581","S_10658584","S_10658672","S_10658870","S_10658951","S_10658987","S_10659001","S_10659351","S_10659353","S_10659355","S_10659358","S_10659361","S_10659368","S_10659369","S_10659370","S_10659443","S_10660326","S_10660328","S_10660469","S_10661197","S_10661696","S_10662355","S_10662361","S_10663001","S_10663124","S_10663327","S_10664391","S_10664502","S_10664614","S_10664625","S_10664827","S_10664886","S_10673000","S_1067356","S_10674511","S_10675674","S_10678155","S_10680607","S_10681171","S_10681287","S_10681984","S_10682764","S_10682910","S_10683715","S_10683796","S_1068510","S_10688272","S_10688388","S_10688642","S_10689321","S_10689345","S_10690881","S_10690883","S_10692964","S_10693693","S_10693697","S_10693699","S_10693702","S_10693704","S_10693714","S_10693721","S_10698438","S_10700593","S_10700594","S_10700652","S_10700654","S_10702385","S_10703411","S_10703412","S_10703480","S_10704051","S_1073714","S_1110823","S_1116600","S_1119489","S_1119500","S_1119580","S_1130012","S_1152329","S_1161339","S_1180157","S_1189078","S_1189114","S_1197254","S_1213840","S_1222340","S_1227761","S_1265394","S_1287326","S_1314089","S_1356856","S_135725","S_135739","S_136739","S_1375880","S_1393495","S_1399094","S_1399186","S_141658","S_1420863","S_1432128","S_1435169","S_1435602","S_1435603","S_1438725","S_1438726","S_1438730","S_1439941","S_1442597","S_1442598","S_1442608","S_1454859","S_1456052","S_1460489","S_1470982","S_1473476","S_1481802","S_1484420","S_1485157","S_1485160","S_1485165","S_1506207","S_1517942","S_1522155","S_1522156","S_1538436","S_1538946","S_1555113","S_1564387","S_1594222","S_1596581","S_1604962","S_162125","S_162182","S_162594","S_1665004","S_1702504","S_1702514","S_1702529","S_1702541","S_1716722","S_1749858","S_1753897","S_1753942","S_1765501","S_1814121","S_1834156","S_1834158","S_1834161","S_1834167","S_1834170","S_1880980","S_1911221","S_1920271","S_1920274","S_1931251","S_1935776","S_1953666","S_1959814","S_1959819","S_1968336","S_1973187","S_1974067","S_1976746","S_1981619","S_1981625","S_1983367","S_1984889","S_1992949","S_1997289","S_1999800","S_1999807","S_1999810","S_1999821","S_203854","S_2101581","S_2114889","S_2126080","S_2127753","S_2127755","S_2127756","S_2129137","S_2135045","S_2155099","S_2165096","S_2196414","S_2197946","S_2202107","S_2212917","S_2213410","S_2233445","S_2261463","S_2281176","S_2282369","S_2284376","S_2284380","S_2284381","S_2292405","S_2303209","S_2347459","S_2364965","S_2390315","S_2391523","S_2403237","S_2404704","S_2408080","S_2442979","S_2450182","S_2450221","S_2451180","S_246445","S_2504038","S_2518452","S_2528459","S_2530045","S_2535947","S_2537550","S_2558845","S_2571553","S_2572618","S_2593499","S_2613472","S_2637622","S_2639447","S_2645766","S_2656116","S_2656119","S_2659815","S_2666939","S_2689330","S_2691625","S_2694895","S_2743742","S_2757312","S_2762644","S_2770758","S_2771287","S_2771417","S_2771690","S_2775738","S_2784953","S_2787452","S_2792357","S_2792585","S_2792629","S_2805758","S_2877082","S_2877643","S_2879082","S_2879916","S_2891464","S_2891792","S_2895660","S_2896424","S_2897121","S_289962","S_2901647","S_2910549","S_2915781","S_2915793","S_2916709","S_2917907","S_2920877","S_2921667","S_2929309","S_2931428","S_2936422","S_2946276","S_2946870","S_2948967","S_2960669","S_2968756","S_2969849","S_2970128","S_2970129","S_2981726","S_298411","S_298458","S_298891","S_2995289","S_2995673","S_2996076","S_3002719","S_3002723","S_3009473","S_3009717","S_3015731","S_3022024","S_3027366","S_3034586","S_3042534","S_3042872","S_3044392","S_3045225","S_3047447","S_3078176","S_3081921","S_3084086","S_308434","S_3084540","S_3087436","S_3092192","S_3092197","S_309700","S_3100803","S_3111134","S_311797","S_312006","S_312022","S_312167","S_312170","S_3122280","S_3122301","S_3144146","S_3144148","S_3147823","S_3148108","S_3171296","S_3175825","S_3183439","S_3183440","S_3187388","S_3195104","S_3210269","S_3211204","S_3213099","S_3213236","S_3218383","S_322592","S_322675","S_3227365","S_3233428","S_3237941","S_3238064","S_3239512","S_3239524","S_3241116","S_3255316","S_3261671","S_3262116","S_3262119","S_3262120","S_3262125","S_3262133","S_3262449","S_3264404","S_3264742","S_3266021","S_3266026","S_3277742","S_3285444","S_3291702","S_3301681","S_3303609","S_3303618","S_3303725","S_3305502","S_3343953","S_3346277","S_3348304","S_3350674","S_3352016","S_3372063","S_3372065","S_3372066","S_3372094","S_3378701","S_3379279","S_3380050","S_3380962","S_3381483","S_3381534","S_3381732","S_3381733","S_3381734","S_3381757","S_3381784","S_3386918","S_3402333","S_3402464","S_3404243","S_3404264","S_3404456","S_3405409","S_3408182","S_3416113","S_3418106","S_3420350","S_3420353","S_3434073","S_3434759","S_3435071","S_3440269","S_3440957","S_353642","S_381672","S_381757","S_382038","S_382042","S_382199","S_400406","S_405386","S_405477","S_405579","S_431444","S_431512","S_431523","S_431564","S_431569","S_431592","S_431984","S_441729","S_456528","S_476508","S_485385","S_493911","S_497885","S_515423","S_515511","S_522051","S_522089","S_522094","S_532814","S_534753","S_535861","S_535868","S_535870","S_550387","S_550388","S_550390","S_550392","S_550395","S_550398","S_550405","S_552953","S_576174","S_576182","S_595903","S_595906","S_602693649","S_602693696","S_602693715","S_602693790","S_602694271","S_602694690","S_602696473","S_602696543","S_602697447","S_602697453","S_602704101","S_602705166","S_602705174","S_602705199","S_602705259","S_602705260","S_602705331","S_602707860","S_602707878","S_602709593","S_602709666","S_602709771","S_602709880","S_602710174","S_602711294","S_602711299","S_602713925","S_602714599","S_602714705","S_602715299","S_602717128","S_602718590","S_604051651","S_604051852","S_604058036","S_604060350","S_604060406","S_604060469","S_604060778","S_604061239","S_604061401","S_604061716","S_604061744","S_604062566","S_604063214","S_604064066","S_604065785","S_604065834","S_604066033","S_604068184","S_604068469","S_604069101","S_604069288","S_604069664","S_604069885","S_604070354","S_604072623","S_604072722","S_604073058","S_604074023","S_604074078","S_604074110","S_604074266","S_604074900","S_604075303","S_604076135","S_604076161","S_604076769","S_604077415","S_604077705","S_604078687","S_604079034","S_604079241","S_604079530","S_604079725","S_604079836","S_604079840","S_604079997","S_604080028","S_604080474","S_604080610","S_604081289","S_604081486","S_604082088","S_604084322","S_604084421","S_604084697","S_604084819","S_604084926","S_604084927","S_604085093","S_604086051","S_604087686","S_604088151","S_604088179","S_604089145","S_604090037","S_604090101","S_604091511","S_604091592","S_604091595","S_604091637","S_604091642","S_604091709","S_604091750","S_604091779","S_604091801","S_604091863","S_604091890","S_604091987","S_604091992","S_604092029","S_604092035","S_604092077","S_604092154","S_604092181","S_604092263","S_604092322","S_604092324","S_604092397","S_604092438","S_604092479","S_604092552","S_604092583","S_604092605","S_604092664","S_604092796","S_604092803","S_604092818","S_604092914","S_604092969","S_604093030","S_604093069","S_604093163","S_604093275","S_604093276","S_604093318","S_604093384","S_604093425","S_604093549","S_604093694","S_604093700","S_604093733","S_604093755","S_604093854","S_604093935","S_604094016","S_604094249","S_604094344","S_604094766","S_604095509","S_604097908","S_604098083","S_604099139","S_604099640","S_604099822","S_604100585","S_604100590","S_604100600","S_604100608","S_604100609","S_604100614","S_604100619","S_604100620","S_604100635","S_604100717","S_604100721","S_604100726","S_604100736","S_604100740","S_604100768","S_604100785","S_604100787","S_604100791","S_604100792","S_604100796","S_604100797","S_604100800","S_604100806","S_604100819","S_604100835","S_604100837","S_604100848","S_604100857","S_604100860","S_604100881","S_604100884","S_604100897","S_604100905","S_604100921","S_604100949","S_604100954","S_604100956","S_604100961","S_604100963","S_604100970","S_604101048","S_604101236","S_604101381","S_604102159","S_604102911","S_604102963","S_604103546","S_604103621","S_604103814","S_604103946","S_604104263","S_604104535","S_604105558","S_604105815","S_604105930","S_604106007","S_604106105","S_604106218","S_604106284","S_604106387","S_604106426","S_604106528","S_604106678","S_604106700","S_604107179","S_604107204","S_604107539","S_604108549","S_604108837","S_604109334","S_604109342","S_604109485","S_604109571","S_604109855","S_604109912","S_604110382","S_604110465","S_604110499","S_604110502","S_604110601","S_604110670","S_604111089","S_604111488","S_604111495","S_604111780","S_604113333","S_604114613","S_604114623","S_604114656","S_604114680","S_604114686","S_604115569","S_604116130","S_604116477","S_604118750","S_604118868","S_604119190","S_604119261","S_604119263","S_604119293","S_604119304","S_604119306","S_604119311","S_604119315","S_604119318","S_604119330","S_604119342","S_604119348","S_604119349","S_604119371","S_604119380","S_604119468","S_604119483","S_604119486","S_604119495","S_604119509","S_604119517","S_604119527","S_604119533","S_604119776","S_604119792","S_604119841","S_604120067","S_604120292","S_618720","S_630764","S_630978","S_653400","S_727984","S_754042","S_777664","S_798845","S_821512","S_834829","S_859743","S_876164","S_877683","S_884910","S_894497","S_897549","S_960003","S_967851","S_970794","S_992239","S_99325","S_99450"};
      String[] addressIDnumArray = {};
      List<String> successList = new ArrayList<>();
      List<String> failList = new ArrayList<>();
      for (String addressIDnum : addressIDnumArray) {
        boolean uploadResult = pgDpbReportService.getPgDpbBiResultWithResult("", addressIDnum);
        if (uploadResult) {
          successList.add(addressIDnum);
        } else {
          failList.add(addressIDnum);
        }
      }
      String successListStr = String.join(",", successList);
      String failListStr = String.join(",", failList);
      logger.info("successListStr = " + successListStr);
      logger.info("failListStr = " + failListStr);
    } catch (Exception e) {
      logger.error("/processDesignatedStores========", e);
    }
  }

  /**
   * 处理指定门店 临时用
   *
   * @param
   * @return
   */
  @RequestMapping(value = "getBlobFileList", method = RequestMethod.GET)
  @ControllerAnnotation(use = "获取指定文件夹文件")
  public ResponseData getBlobFileList(String folderName) {
    try {
      List<String> blobFileList = pgDpbReportService.getBlobFileList(folderName);
      return ResponseData.success().data(blobFileList);
    } catch (Exception e) {
      logger.error("/getPgDpbBiResult========", e);
    }
    return ResponseData.failure();
  }

  /**
   * 推送门店状态标记文件
   * @param searchDate 2024-09-12
   */
  @RequestMapping(value = "getHsmStatusFile", method = RequestMethod.GET)
  @ControllerAnnotation(use = "获取并推送门店状态标记文件")
  public void getHsmStatusFile(String searchDate) {
    try {
      Runnable rn = new Runnable() {//异步处理数据
        public void run() {
          pgDpbReportService.getHsmStatusFile(searchDate);
        }
      };
      executorService.execute(rn);
    } catch (Exception e) {
      logger.error("/uploadHsmStatusFile========", e);
    }
  }

  /**
   * 获取并转换并推送云看店数据
   * @param month 202407
   */
  @RequestMapping(value = "getAndUploadCloudViewStoreFile", method = RequestMethod.GET)
  @ControllerAnnotation(use = "获取并转换并推送云看店数据")
  public void getAndUploadCloudViewStoreFile(String month) {
    try {
      Runnable rn = new Runnable() {//异步处理数据
        public void run() {
          if (StringUtil.isNotBlank(month)){
            pgDpbReportService.getAndUploadCloudViewStoreFile(month);
          }
        }
      };
      executorService.execute(rn);
    } catch (Exception e) {
      logger.error("/uploadHsmStatusFile========", e);
    }
  }

  /**
   * 获取PgDcp结果
   *
   * @param searchDate
   * @return
   */
  @RequestMapping(value = "getPgDcpBiResult", method = RequestMethod.GET)
  @ControllerAnnotation(use = "报表-获取PgDcp结果")
  public void getPgDcpBiResult(String searchDate, String addressIDnum) {
    try {
      Runnable rn = new Runnable() {//异步处理数据
        public void run() {
          pgDpbReportService.getPgDcpBiResult(searchDate, addressIDnum);
        }
      };
      executorService.execute(rn);
      //pgDpbReportService.getPgDcpBiResult(searchDate, addressIDnum);

    } catch (Exception e) {
      logger.error("/getPgDcpBiResult========", e);
    }
  }
}
