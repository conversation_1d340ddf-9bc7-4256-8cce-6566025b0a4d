package com.lenztech.bi.enterprise.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.lenztech.bi.enterprise.dto.SnowBeerCompleteStatusResp;
import com.lenztech.bi.enterprise.dto.hipac.HiPacBiTargetImage;
import com.lenztech.bi.enterprise.dto.hipac.HiPacBiTargetProduct;
import com.lenztech.bi.enterprise.dto.hipac.HiPacBiTargetResp;
import com.lenztech.bi.enterprise.dto.hipac.HiPacPatches;
import com.lenztech.bi.enterprise.dto.hipac.HipacCompleteStatusResp;
import com.lenztech.bi.enterprise.entity.BiResponseRecord;
import com.lenztech.bi.enterprise.entity.HipacPatchResult;
import com.lenztech.bi.enterprise.entity.HipacStitchResult;
import com.lenztech.bi.enterprise.mapper.lenzbi.BiResponseRecordMapper;
import com.lenztech.bi.enterprise.mapper.lenzbi.HiPacPatchReportMapper;
import com.lenztech.bi.enterprise.mapper.lenzbi.HiPacStitchReportMapper;
import com.lenztech.bi.enterprise.service.HiPacReportService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 海拍客根据答卷id查询BI识别指标结果
 *
 * <AUTHOR>
 * @date 2021-05-24 09:44:19
 */
@Service
public class HiPacReportServiceImpl implements HiPacReportService {

    public static final Logger logger = LoggerFactory.getLogger(HiPacReportServiceImpl.class);

    @Autowired
    private HiPacStitchReportMapper hiPacStitchReportMapper;

    @Autowired
    private HiPacPatchReportMapper hiPacPatchReportMapper;

    @Autowired
    private BiResponseRecordMapper biResponseRecordMapper;

    /**
     * 根据答卷id查询BI结果
     *
     * @param responseId 答卷Id
     * @return JzBiTargetResp
     */
    @Override
    public HiPacBiTargetResp getBiTargetList(String responseId) {
        logger.info("responseId:" + responseId);
        HiPacBiTargetResp hiPacBiTargetResp = new HiPacBiTargetResp();

        try {
            List<HipacStitchResult> imageList = hiPacStitchReportMapper.getImageList(responseId);
            List<HipacPatchResult> productList = hiPacPatchReportMapper.getProductList(responseId);

            List<HiPacBiTargetImage> hiPacBiTargetImageList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(imageList)) {
                for (HipacStitchResult hipacStitchResult : imageList) {
                    HiPacBiTargetImage hiPacBiTargetImage = new HiPacBiTargetImage();
                    hiPacBiTargetImage.setGroupId(hipacStitchResult.getGroupId());
                    hiPacBiTargetImage.setSourceImgIdList(hipacStitchResult.getSourceImgIdList());
                    hiPacBiTargetImage.setStitchImgUrl(hipacStitchResult.getStitchImgUrl());
                    hiPacBiTargetImage.setIsStitchGood(hipacStitchResult.getIsStitchGood());
                    hiPacBiTargetImageList.add(hiPacBiTargetImage);
                }
            }

            // imgId分组去重
            Set<String> imgIds = Sets.newHashSet();
            for (HipacPatchResult hipacPatchResult : productList) {
                imgIds.add(hipacPatchResult.getImgId());
            }

            List<HiPacBiTargetProduct> hiPacBiTargetProducts = Lists.newArrayList();
            HiPacBiTargetProduct hiPacBiTargetProduct;
            for (String imgId : imgIds) {
                hiPacBiTargetProduct = new HiPacBiTargetProduct();
                List<HipacPatchResult> collect = productList.stream().filter(obj -> obj.getImgId().equals(imgId)).collect(Collectors.toList());
                List<HiPacPatches> hiPacPatches = new ArrayList<>();
                HiPacPatches patches;
                for (HipacPatchResult hipacPatchResult : collect) {
                    patches = new HiPacPatches();
                    hiPacBiTargetProduct.setImgId(imgId);
                    patches.setSkuCode(hipacPatchResult.getSkuCode());
                    patches.setSkuName(hipacPatchResult.getSkuName());
                    patches.setCoordinate(hipacPatchResult.getCoordinate());
                    patches.setMosaicCoords(hipacPatchResult.getMosaicCoords());
                    patches.setFacingCount(hipacPatchResult.getFacingCount());
                    hiPacPatches.add(patches);
                }
                hiPacBiTargetProduct.setPatches(hiPacPatches);
                hiPacBiTargetProducts.add(hiPacBiTargetProduct);
            }


            hiPacBiTargetResp.setResponseId(responseId);
            hiPacBiTargetResp.setAiResult(hiPacBiTargetProducts);
            hiPacBiTargetResp.setAiStitchResult(hiPacBiTargetImageList);

        } catch (Exception e) {
            logger.error("/getBiTargetList========", e);
        }
        return hiPacBiTargetResp;
    }

    /**
     * 获取海拍客BI识别状态
     *
     * @param responseId
     * @return
     */
    @Override
    public HipacCompleteStatusResp getCompleteStatus(String responseId) {
        HipacCompleteStatusResp hipacCompleteStatusResp = new HipacCompleteStatusResp();
        List<BiResponseRecord> recordList = biResponseRecordMapper.getRecordList(responseId);
        if (org.springframework.util.CollectionUtils.isEmpty(recordList)) {
            hipacCompleteStatusResp.setCompleteStatus(false);
        } else {
            hipacCompleteStatusResp.setCompleteStatus(true);
        }
        return hipacCompleteStatusResp;
    }
}