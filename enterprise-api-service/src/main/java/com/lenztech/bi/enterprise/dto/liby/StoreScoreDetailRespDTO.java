package com.lenztech.bi.enterprise.dto.liby;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/14 10:59
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class StoreScoreDetailRespDTO {

    private String responseId;
    /**
     * 门店类型
     */
    private String storeType;
    /**
     * 拜访时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date visitTime;
    /**
     * 立白堆型
     */
    private Integer liByDisplayForm;
    /**
     * 立白地堆数量
     */
    private Integer liByDisplayNum;
    /**
     * 考核项-得分
     */
    private StoreScoreDetailExamineItemDTO storeScoreDetailExamineItemDTO;
    /**
     * 识别详情列表
     */
    private List<StoreScoreDetailRuleDTO> storeScoreDetailRuleDTOS;

}
