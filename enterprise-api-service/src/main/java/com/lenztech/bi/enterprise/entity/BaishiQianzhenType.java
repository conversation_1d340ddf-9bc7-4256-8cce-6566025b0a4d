package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 千镇门店含有的sku分销信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("baishi_qianzhen_type")
public class BaishiQianzhenType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    private Integer id;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 前缀
     */
    private String tablePrefix;

//    /**
//     * 类型：1千镇，2百事
//     */
//    private Integer type;


}
