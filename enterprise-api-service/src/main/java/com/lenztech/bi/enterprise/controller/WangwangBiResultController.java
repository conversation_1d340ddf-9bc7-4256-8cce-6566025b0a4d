package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.jml.JmlBiTargetResp;
import com.lenztech.bi.enterprise.dto.wangwang.WangWangBiTargetResp;
import com.lenztech.bi.enterprise.service.JmlReportService;
import com.lenztech.bi.enterprise.service.WangwangReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 旺旺bi指标Controller
 *
 * <AUTHOR>
 * @date 2019-10-17 15:44:19
 */
@RestController
@RequestMapping("/biResult/wangwang/")
public class WangwangBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(WangwangBiResultController.class);

    @Autowired
    private WangwangReportService wangwangReportService;

    /**
     * 获取指标列表
     *
     * @param responseId
     * @return ResponseData<WangWangBiTargetResp>
     */
    @RequestMapping(value = "getTargetList", method = RequestMethod.GET)
    public ResponseData<WangWangBiTargetResp> get(String responseId) {
        try {
            WangWangBiTargetResp wangWangBiTargetResp = wangwangReportService.getBiTargetList(responseId);
            return ResponseData.success().data(wangWangBiTargetResp);
        } catch (Exception e) {
            logger.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

}
