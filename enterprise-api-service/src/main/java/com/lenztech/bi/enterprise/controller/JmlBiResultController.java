package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.jml.JmlBiTargetResp;
import com.lenztech.bi.enterprise.service.JmlReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 今麦郎bi指标Controller
 *
 * <AUTHOR>
 * @date 2019-10-17 15:44:19
 */
@RestController
@RequestMapping("/biResult/jml/")
public class JmlBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(JmlBiResultController.class);

    @Autowired
    private JmlReportService jmlReportService;

    /**
     * 获取指标列表
     *
     * @param responseId
     * @return ResponseData<JmlBiTargetResp>
     */
    @RequestMapping(value = "getTargetList", method = RequestMethod.GET)
    public ResponseData<JmlBiTargetResp> get(String responseId) {
        try {
            JmlBiTargetResp jmlBiTargetResp = jmlReportService.getBiTargetList(responseId);
            return ResponseData.success().data(jmlBiTargetResp);
        } catch (Exception e) {
            logger.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取指标列表
     *
     * @param responseId
     * @return ResponseData<JmlBiTargetResp>
     */
    @RequestMapping(value = "getTargetListV2", method = RequestMethod.GET)
    public ResponseData<JmlBiTargetResp> getTargetList(String responseId) {
        try {
            JmlBiTargetResp jmlBiTargetResp = jmlReportService.getBiTargetListV2(responseId);
            return ResponseData.success().data(jmlBiTargetResp);
        } catch (Exception e) {
            logger.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

}
