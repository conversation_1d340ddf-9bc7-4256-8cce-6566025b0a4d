//package com.lenztech.bi.enterprise.service.impl;
//
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.lenztech.bi.enterprise.entity.TCustomQuestionRelation;
//import com.lenztech.bi.enterprise.mapper.task.TCustomQuestionRelationMapper;
//import com.lenztech.bi.enterprise.service.ITCustomQuestionRelationService;
//import org.springframework.stereotype.Service;
//
///**
// * description:  接入客户-题目id对应关系表 服务实现类
// * <AUTHOR>
// * @date  2019-09-11 13:24
// * @since 2019-09-11
// **/
//@Service
//public class TCustomQuestionRelationServiceImpl extends ServiceImpl<TCustomQuestionRelationMapper, TCustomQuestionRelation> implements ITCustomQuestionRelationService {
//
////    @Override
////    public Integer selectBICompanyId(String companyId) {
////        return baseMapper.selectBICompanyId(companyId);
////    }
////
////    @Override
////    public String selectResponseId(String dataId) {
////        return baseMapper.selectResponseId(dataId);
////    }
////
////    @Override
////    public String selectTaskIdFromResponse(String responseId) {
////        return baseMapper.selectTaskIdFromResponse(responseId);
////    }
//
//    @Override
//    public Integer selectBICompanyIdByTaskId(String taskId) {
//        return baseMapper.selectBICompanyIdByTaskId(taskId);
//    }
//}
