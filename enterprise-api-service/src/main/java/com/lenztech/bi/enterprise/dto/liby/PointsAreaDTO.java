package com.lenztech.bi.enterprise.dto.liby;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 扣分区域
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/13 18:51
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class PointsAreaDTO {

    private List<PointsAreaDetailDTO> pointsAreaDetailDTOS;

}
