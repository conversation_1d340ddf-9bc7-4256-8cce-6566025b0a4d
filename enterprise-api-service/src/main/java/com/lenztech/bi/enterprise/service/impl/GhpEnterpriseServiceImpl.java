package com.lenztech.bi.enterprise.service.impl;

import com.google.common.collect.ImmutableMap;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.ghp.GhpPocListDTO;
import com.lenztech.bi.enterprise.dto.pg.PgPocSkuNotExistListDTO;
import com.lenztech.bi.enterprise.entity.GhyPocEntity;
import com.lenztech.bi.enterprise.mapper.bienterprise.GhyPocMapper;
import com.lenztech.bi.enterprise.service.GhpEnterpriseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2019-11-07 15:19
 * @since JDK 1.8
 */
@Service
@Slf4j
public class GhpEnterpriseServiceImpl implements GhpEnterpriseService {

    @Autowired
    private GhyPocMapper ghyPocMapper;


    @Override
    public ResponseData<GhpPocListDTO> listGhyPocEntity(String responseId, String imageId) {
        List<GhyPocEntity> ghyPocEntityList = ghyPocMapper.listGhyPocEntityEntity(ImmutableMap.<String, Object>builder()
                .put("responseId", responseId)
                .put("imageId", imageId)
                .build());
        return ResponseData.success(new GhpPocListDTO(ghyPocEntityList));
    }
}
