package com.lenztech.bi.enterprise.dto;

/**
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 28/12/18 下午2:17
 */
public class BIResult {

    /**
     * 识别BI结果，4个可选整数，0->优秀，1->合格，2->待改善，3->不合格
     */
    private int custEval;

    /**
     * 对BI结果的详细描述，如不合格原因
     */
    private String custReason;


    public int getCustEval() {
        return custEval;
    }

    public void setCustEval(int custEval) {
        this.custEval = custEval;
    }

    public String getCustReason() {
        return custReason;
    }

    public void setCustReason(String custReason) {
        this.custReason = custReason;
    }
}
