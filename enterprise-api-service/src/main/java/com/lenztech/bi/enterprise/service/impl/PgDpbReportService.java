package com.lenztech.bi.enterprise.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.read.metadata.ReadWorkbook;
import com.azure.storage.blob.BlobClient;
import com.azure.storage.blob.BlobContainerClient;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import com.azure.storage.blob.models.BlobItem;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lenztech.bi.enterprise.dto.pg.*;
import com.lenztech.bi.enterprise.entity.*;
import com.lenztech.bi.enterprise.handler.HsmPskuHeaderAndDataHandler;
import com.lenztech.bi.enterprise.mapper.*;
import com.lenztech.bi.enterprise.service.IPgHsmStatusBiReportService;
import com.lenztech.bi.enterprise.utils.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/** Created with IntelliJ IDEA. User: sunqingyuan Date: 2021/06/16 Time: 15:38 类功能: 宝洁DPB相关接口逻辑 */
@Service
public class PgDpbReportService {

  @Autowired private PgHsmDetailBiReportMapper pgHsmDetailBiReportMapper;

  @Autowired private PgHsmDetailBiReportDcpMapper pgHsmDetailBiReportDcpMapper;

  @Autowired private PgHsmBiReportMapper pgHsmBiReportMapper;

  @Autowired private PgHsmBiReportDcpMapper pgHsmBiReportDcpMapper;

  @Autowired private PgDpbReportMapper pgDpbReportMapper;

  @Autowired private PgHsmStep2BiReportMapper pgHsmStep2BiReportMapper;

  @Autowired private PgHsmStep2BiReportDcpMapper pgHsmStep2BiReportDcpMapper;

  @Autowired private PgHsmStep2BiReportPriceMapper pgHsmStep2BiReportPriceMapper;

  @Autowired private PgHsmDetailBiReportDetailMapper pgHsmDetailBiReportDetailMapper;

  @Autowired private PgHsmDetailBiReportDetailDcpMapper pgHsmDetailBiReportDetailDcpMapper;

  @Autowired private PgHsmStoreStatusBiReportMapper pgHsmStatusBiReportMapper;

  @Autowired private TPgReportHsmSkuV7Mapper tPgReportHsmSkuV7Mapper;

  @Autowired private PgHsmSkupriceBiReportMapper pgHsmSkupriceBiReportMapper;

  @Autowired
  private PgHsmCdlPushDetailLogMapper pgHsmCdlPushDetailLogMapper;

  @Autowired
  private PgGeDpFmotPskuDetailMapper pgGeDpFmotPskuDetailMapper;

  @Autowired private IPgHsmStatusBiReportService iPgHsmStatusBiReportService;

//  public static final String BLOB_SAS_TOKEN =
//      "https://b2bcdlplandingprd01.blob.core.chinacloudapi.cn/ge-api?sv=2021-12-02&si=ge-api-rwdl&sr=c&sig=7FGnFwLUQ2U8usQaqw9Bc0XFlN7Mk4R%2BoWX2bhXDMWo%3D";

//  public static final String BLOB_CONTAINER = "ge-api/dpartner-ge/daily/";
//public static final String DCP_CDL_CALLBACK = "https://callback.sandbox.youxin.cloud/workflow/hooks/JHq4H3mran" 测试;
      @Value("${blob_sas_token}")
      public String BLOB_SAS_TOKEN;

      @Value("${blob_container}")
      public String BLOB_CONTAINER;

  @Value("${dcp_cdl_callback}")
  public String DCP_CDL_CALLBACK;

  @Value("${feishu_notice_url}")
  public String FEISHU_NOTICE_URL;
  //public static final String FEISHU_NOTICE_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/3b8e4227-c7e2-43c1-a719-a323b6e13b99";

  public static final String BLOB_CONTAINER_QUALITY = "ge-api/dpartner-ge/data_quality/";
  public static final String BLOB_CONTAINER_STORE_STATUS = "ge-api/dpartner-ge/Store_status/";
  public static final String BLOB_CONTAINER_RESTATEMENT = "ge-api/dpartner-ge/restatement/";
  public static final String BLOB_CONTAINER_DCP = "ge-api/dpartner-ge/dcp_daily/";
  public static final String BLOB_CONTAINER_PRICE = "ge-api/dpartner-ge/rpo_daily/";

  public static final String CDL_DAILY_FILES_COUNT = "CDL_Daily_files_count_";
  public static final String CDL_DAILY_FILES_DETAIL = "CDL_Daily_files_detail_";

  public static final String KPI_TYPE_BR_OTHER = "br_other";
  public static final String KPI_TYPE_DISPLAY_DISTRIBUTION = "display_distribution";
  public static final String KPI_TYPE_JOINT_DISPLAY = "joint_display";
  public static final String KPI_TYPE_OTHER_DISPLAY = "other_display";
  public static final String KPI_TYPE_OTHER_SHELF = "other_shelf";

  public static final String KPI_TYPE_PG_DISPLAY = "pg_display";
  public static final String KPI_TYPE_PG_SHELF = "pg_shelf";
  public static final String KPI_TYPE_PICTURE = "picture";
  public static final String KPI_TYPE_PSKU = "psku";
  public static final String KPI_TYPE_SAMPLE_DISPLAY = "sample_display";

  public static final String KPI_TYPE_SHELF_DISTRIBUTION = "shelf_distribution";
  public static final String KPI_TYPE_STORE_QUALITY = "store quality";
  public static final String KPI_TYPE_SUMMARY = "summary";
  public static final String KPI_TYPE_TOTAL_DISPLAY = "total_display";
  public static final String KPI_TYPE_TOTAL_SHELF = "total_shelf";

  public static final String KPI_TYPE_SEGMENT = "Segmentlist";

  public static final String CATEGORY_BABY = "Baby";
  public static final String CATEGORY_FABRIC = "Fabric";
  public static final String CATEGORY_FEM = "Fem";
  public static final String CATEGORY_HAIR = "Hair";
  public static final String CATEGORY_ORAL = "Oral";
  public static final String CATEGORY_PCC = "PCC";
  public static final String CATEGORY_SHAVE = "Shave";

  /**
   * 单次最多查询条数
   */
  private static int oneQueryLimit = 500;

  /**
   * 小文件阀值 PM定120KB
   */
  private static int SMALL_FILE_THRESHOLD = 120;

  public static final Logger logger = LoggerFactory.getLogger(PgDpbReportService.class);

  /**
   * 获取PgDpb结果
   *
   * @param searchDate 查询日期 形如2021-06-02，如无则默认前一天
   * @return
   */
  public void getPgDpbBiResult(String searchDate, String addressIDnum) {

    BlobServiceClient blobServiceClient =
        new BlobServiceClientBuilder().endpoint(BLOB_SAS_TOKEN).buildClient();

    // 要求是yyyyMMdd
    String folderName = "";
    DateTime nowTime = new DateTime();

    // 是否单独跑某一家门店
    boolean isOperateAlone = false;
    if (StringUtils.isNotBlank(addressIDnum)) {
      isOperateAlone = true;
    }

    if (StringUtils.isBlank(searchDate)) {
      searchDate = nowTime.minusDays(1).toString(DateUtil.DTFormat.yyyy_MM_dd.getFormat());
      folderName = nowTime.toString(DateUtil.DTFormat.yyyyMMdd.getFormat());
    } else {
      // 指定日期文件夹也为后一天日期
      DateTime searchDateDT = new DateTime(searchDate + "T00:00:00.000+08:00");
      folderName = searchDateDT.plusDays(1).toString(DateUtil.DTFormat.yyyyMMdd.getFormat());
    }
    // 单独门店放到新一天文件夹
    if (isOperateAlone) {
      folderName = nowTime.plusDays(1).toString(DateUtil.DTFormat.yyyyMMdd.getFormat());
    }

    BlobContainerClient blobContainerClient =
        blobServiceClient.getBlobContainerClient(BLOB_CONTAINER + folderName);
    BlobContainerClient blobContainerClientForPrice =
            blobServiceClient.getBlobContainerClient(BLOB_CONTAINER_PRICE + folderName);

    LambdaQueryWrapper<PgHsmStep2BiReport> pgHsmStep2BiReportLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    if (isOperateAlone) {
      pgHsmStep2BiReportLambdaQueryWrapper.eq(PgHsmStep2BiReport::getAddressIDnum, addressIDnum);
      if (StringUtils.isNotBlank(searchDate)){
//        pgHsmStep2BiReportLambdaQueryWrapper.eq(PgHsmStep2BiReport::getExecDate, searchDate);
        // 将 getExecDate 改为 getCreateTime，并设置时间范围为当天凌晨1点到次日凌晨1点
        pgHsmStep2BiReportLambdaQueryWrapper.ge(PgHsmStep2BiReport::getCreateTime, searchDate + " 01:00:00");
        pgHsmStep2BiReportLambdaQueryWrapper.lt(PgHsmStep2BiReport::getCreateTime, DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(searchDate).plusDays(1).toString("yyyy-MM-dd") + " 01:00:00");
      }
      pgHsmStep2BiReportLambdaQueryWrapper.orderByDesc(PgHsmStep2BiReport::getCreateTime);
      pgHsmStep2BiReportLambdaQueryWrapper.last(" limit 1");
    } else {
      //pgHsmStep2BiReportLambdaQueryWrapper.eq(PgHsmStep2BiReport::getExecDate, searchDate);
      pgHsmStep2BiReportLambdaQueryWrapper.ge(PgHsmStep2BiReport::getCreateTime, searchDate + " 01:00:00");
      pgHsmStep2BiReportLambdaQueryWrapper.lt(PgHsmStep2BiReport::getCreateTime, DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(searchDate).plusDays(1).toString("yyyy-MM-dd") + " 01:00:00");
      pgHsmStep2BiReportLambdaQueryWrapper.groupBy(PgHsmStep2BiReport::getAddressIDnum, PgHsmStep2BiReport::getMonth);
    }

    List<PgHsmStep2BiReport> pgHsmStep2BiReportList =
        pgHsmStep2BiReportMapper.selectList(pgHsmStep2BiReportLambdaQueryWrapper);

    //文件清单
    List<String> fileList = new ArrayList<>();
    List<String> smallFileList = new ArrayList<>();
    fileList.add("Store_Code_Seq");
    List<String> fileCountList = new ArrayList<>();
    for (PgHsmStep2BiReport pgHsmStep2BiReport : pgHsmStep2BiReportList) {

      // 一家完整门店，含其子问卷 七大品类合集
      PgDpbTotalResult pgDpbTotalResult = new PgDpbTotalResult();
      CglibCopyBeanUtil.basicCopyBean(pgHsmStep2BiReport, pgDpbTotalResult);
      pgDpbTotalResult.setDate(DateUtil.convert2String(pgHsmStep2BiReport.getRidUpdateTime(), DateUtil.DTFormat.yyyy_MM_dd.getFormat()));
      pgDpbTotalResult.setIs_dcp_flag(pgHsmStep2BiReport.getIsDcpFlag());

      PgDpbTotalResultForPrice pgDpbTotalResultForPrice = CglibCopyBeanUtil.doDeepClone(pgDpbTotalResult, PgDpbTotalResultForPrice.class);
      LambdaQueryWrapper<PgHsmStep2BiReport> pgHsmStep2BiReportLambdaQueryWrapper1 =
          new LambdaQueryWrapper<>();
      if (isOperateAlone) {
        pgHsmStep2BiReportLambdaQueryWrapper1.eq(PgHsmStep2BiReport::getExecDate, pgHsmStep2BiReport.getExecDate());
      } else {
        //pgHsmStep2BiReportLambdaQueryWrapper1.eq(PgHsmStep2BiReport::getExecDate, searchDate);
        pgHsmStep2BiReportLambdaQueryWrapper1.ge(PgHsmStep2BiReport::getCreateTime, searchDate + " 01:00:00");
        pgHsmStep2BiReportLambdaQueryWrapper1.lt(PgHsmStep2BiReport::getCreateTime, DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(searchDate).plusDays(1).toString("yyyy-MM-dd") + " 01:00:00");
      }
      pgHsmStep2BiReportLambdaQueryWrapper1.eq(
          PgHsmStep2BiReport::getAddressIDnum, pgHsmStep2BiReport.getAddressIDnum());
      pgHsmStep2BiReportLambdaQueryWrapper1.eq(PgHsmStep2BiReport::getMonth, pgHsmStep2BiReport.getMonth());
      pgHsmStep2BiReportLambdaQueryWrapper1.groupBy(PgHsmStep2BiReport::getCategory);
      List<PgHsmStep2BiReport> pgHsmStep2BiReportList1 =
          pgHsmStep2BiReportMapper.selectList(pgHsmStep2BiReportLambdaQueryWrapper1);
      List<PgHsmCdlPushDetailLog> pgHsmCdlPushDetailLogList = Lists.newArrayList();
      boolean hasPriceNode = false;
      for (PgHsmStep2BiReport pgHsmStep2BiReport1 : pgHsmStep2BiReportList1) {

        List<PgHsmSkuDetail> pgHsmDetailDTOList = new ArrayList<>();
        pgHsmDetailDTOList =
            pgDpbReportMapper.getPgHsmDetailDTOListByCategory(
                pgHsmStep2BiReport1.getCategory(), pgHsmStep2BiReport1.getResponseId(),
                pgHsmStep2BiReport1.getMonth(),
                pgHsmStep2BiReport1.getDate());

        List<PgHsmCdlPushDetailLog> pgHsmCdlPushDetailLogList1 = CglibCopyBeanUtil.doBatchClone(pgHsmDetailDTOList, PgHsmCdlPushDetailLog.class);
        if (CollectionUtils.isNotEmpty(pgHsmCdlPushDetailLogList1)){
          pgHsmCdlPushDetailLogList1.forEach(pgHsmCdlPushDetailLog -> {
            pgHsmCdlPushDetailLog.setId(null);
            pgHsmCdlPushDetailLog.setExecDate(pgHsmStep2BiReport1.getExecDate());
            pgHsmCdlPushDetailLog.setTaskId(pgHsmStep2BiReport1.getTaskId());
            pgHsmCdlPushDetailLog.setResponseId(pgHsmStep2BiReport1.getResponseId());
            pgHsmCdlPushDetailLog.setAddressIDnum(pgHsmStep2BiReport1.getAddressIDnum());
            pgHsmCdlPushDetailLog.setMonth(pgHsmStep2BiReport1.getMonth());
            pgHsmCdlPushDetailLog.setDate(pgHsmStep2BiReport1.getDate());
            pgHsmCdlPushDetailLog.setCustomerType(0);
          });
          pgHsmCdlPushDetailLogList.addAll(pgHsmCdlPushDetailLogList1);
        }


        // 按kpi type分组
        Map<String, List<PgHsmSkuDetail>> collect =
            pgHsmDetailDTOList.stream()
                .collect(Collectors.groupingBy(p -> p.getKpiType(), Collectors.toList()));

        PgDpbCategoryDetail pgDpbCategoryDetail = new PgDpbCategoryDetail();
        CglibCopyBeanUtil.basicCopyBean(pgHsmStep2BiReport1, pgDpbCategoryDetail);

        PgDpbCategoryDetailForPrice pgDpbCategoryDetailForPrice = CglibCopyBeanUtil.doClone(pgDpbCategoryDetail, PgDpbCategoryDetailForPrice.class);
        pgDpbCategoryDetail = getGroupDataByKpiType(collect, pgDpbCategoryDetail, pgHsmStep2BiReport1.getDate(), false);

//        //添加价格节点
//        List<PgHsmSkupriceBiReport> pgHsmSkupriceBiReportList = pgHsmSkupriceBiReportMapper.selectList(new LambdaQueryWrapper<PgHsmSkupriceBiReport>().eq(PgHsmSkupriceBiReport::getCategory, pgHsmStep2BiReport1.getCategory()).eq(PgHsmSkupriceBiReport::getTaskAddressId, pgHsmStep2BiReport1.getResponseId()));
//        if (CollectionUtils.isNotEmpty(pgHsmSkupriceBiReportList)){
//          hasPriceNode = true;
//          List<PgPriceSkuDetail> pgPriceSkuDetailList = Lists.newArrayList();
//          pgHsmSkupriceBiReportList.forEach(pgPriceSkuDetail -> {
//            PgPriceSkuDetail pgPriceSkuDetail1 = new PgPriceSkuDetail();
//            pgPriceSkuDetail1.setId(pgPriceSkuDetail.getId());
//            pgPriceSkuDetail1.setKpiName(pgPriceSkuDetail.getSkuName());
//            pgPriceSkuDetail1.setBarCode(pgPriceSkuDetail.getBarcode());
//            pgPriceSkuDetail1.setKpiValue(pgPriceSkuDetail.getPrice());
//            pgPriceSkuDetail1.setCategory(pgPriceSkuDetail.getCategory());
//            pgPriceSkuDetail1.setKpiType(pgPriceSkuDetail.getType());
//            pgPriceSkuDetailList.add(pgPriceSkuDetail1);
//          });
//          pgDpbCategoryDetailForPrice.getPgHsmDetail().setPriceList(pgPriceSkuDetailList);
//        }


        pgDpbTotalResult
            .getKPIData()
            .add(classifyCategory(pgHsmStep2BiReport1.getCategory(), pgDpbCategoryDetail));

        pgDpbTotalResultForPrice
                .getKPIData()
                .add(classifyCategoryForPrice(pgHsmStep2BiReport1.getCategory(), pgDpbCategoryDetailForPrice));
      }
      String resultJson = JsonUtil.toJsonString(pgDpbTotalResult);
      String fileName = pgDpbTotalResult.getAddressIDnum()
              + "_"
              + pgHsmStep2BiReport.getStoreCodeSeq()
              + "_"
              + new DateTime().toString(DateUtil.DTFormat.yyyyMMddHHmmss.getFormat());
      int startSmallFileCount = smallFileList.size();
      uploadAzureBlobForSmallFileList(smallFileList, pgHsmStep2BiReport, resultJson, fileName, blobContainerClient);
      int endSmallFileCount = smallFileList.size();
      //没有添加小文件不传
      if (startSmallFileCount == endSmallFileCount){
        fileList.add(pgHsmStep2BiReport.getStoreCodeSeq());
      }
      try {
        //批量插入日志记录
        insertBatch(pgHsmCdlPushDetailLogList);
      } catch (Exception e) {
        logger.error("GE 日志写入失败:" + e.toString());
      }
      //价格节点传输
//      if (hasPriceNode){
//        String resultJsonForPrice = JsonUtil.toJsonString(pgDpbTotalResultForPrice);
//        String fileNameForPrice = pgDpbTotalResult.getAddressIDnum()
//                + "_price_"
//                + new DateTime().toString(DateUtil.DTFormat.yyyyMMddHHmmss.getFormat());
//        uploadAzureBlob(resultJsonForPrice, fileNameForPrice, blobContainerClientForPrice);
//      }

    }

    //上传统计文件
    BlobContainerClient blobContainerClientStatisticsFile =
            blobServiceClient.getBlobContainerClient(BLOB_CONTAINER_QUALITY);
    uploadStatisticsFile(fileList, CDL_DAILY_FILES_DETAIL + folderName, blobContainerClientStatisticsFile);
    fileCountList.add(String.valueOf(fileList.size() - 1));
    uploadStatisticsFile(fileCountList, CDL_DAILY_FILES_COUNT + folderName, blobContainerClientStatisticsFile);
    if (CollectionUtils.isNotEmpty(fileList) && (fileList.size() == 1)){
      sendFeiShuNotice("GE执行日期:" + searchDate + ", 没有数据, 未推送CDL");
    }
    if (CollectionUtils.isNotEmpty(smallFileList)){
      String noticeStr = StringUtils.join(smallFileList, ",") + "文件小于" + SMALL_FILE_THRESHOLD + "KB, 未推送CDL";
      logger.error("noticeStr = " + noticeStr);
      sendFeiShuNotice(noticeStr);
    }
  }

  /**
   * 获取并推送PgDpb价格结果
   *
   * @param searchDate 查询日期 形如2025-03-17，如无则默认前一天
   * @return
   */
  public void getPgDpbPriceBiResult(String searchDate, String addressIDnum){

    BlobServiceClient blobServiceClient =
            new BlobServiceClientBuilder().endpoint(BLOB_SAS_TOKEN).buildClient();

    // 要求是yyyyMMdd
    String folderName = "";
    DateTime nowTime = new DateTime();

    // 是否单独跑某一家门店
    boolean isOperateAlone = false;
    if (StringUtils.isNotBlank(addressIDnum)) {
      isOperateAlone = true;
    }

    if (StringUtils.isBlank(searchDate)) {
      searchDate = nowTime.minusDays(1).toString(DateUtil.DTFormat.yyyy_MM_dd.getFormat());
      folderName = nowTime.toString(DateUtil.DTFormat.yyyyMMdd.getFormat());
    } else {
      // 指定日期文件夹也为后一天日期
      DateTime searchDateDT = new DateTime(searchDate + "T00:00:00.000+08:00");
      folderName = searchDateDT.plusDays(1).toString(DateUtil.DTFormat.yyyyMMdd.getFormat());
    }
    // 单独门店放到新一天文件夹
    if (isOperateAlone) {
      folderName = nowTime.plusDays(1).toString(DateUtil.DTFormat.yyyyMMdd.getFormat());
    }

    BlobContainerClient blobContainerClient =
            blobServiceClient.getBlobContainerClient(BLOB_CONTAINER + folderName);
    BlobContainerClient blobContainerClientForPrice =
            blobServiceClient.getBlobContainerClient(BLOB_CONTAINER_PRICE + folderName);

    LambdaQueryWrapper<PgHsmStep2BiReportPrice> pgHsmStep2BiReportPriceLambdaQueryWrapper =
            new LambdaQueryWrapper<>();
    if (isOperateAlone) {
      pgHsmStep2BiReportPriceLambdaQueryWrapper.eq(PgHsmStep2BiReportPrice::getAddressIDnum, addressIDnum);
      if (StringUtils.isNotBlank(searchDate)){
//        pgHsmStep2BiReportLambdaQueryWrapper.eq(PgHsmStep2BiReport::getExecDate, searchDate);
        // 将 getExecDate 改为 getCreateTime，并设置时间范围为当天凌晨1点到次日凌晨1点
        pgHsmStep2BiReportPriceLambdaQueryWrapper.ge(PgHsmStep2BiReportPrice::getCreateTime, searchDate + " 01:00:00");
        pgHsmStep2BiReportPriceLambdaQueryWrapper.lt(PgHsmStep2BiReportPrice::getCreateTime, DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(searchDate).plusDays(1).toString("yyyy-MM-dd") + " 01:00:00");
      }
      pgHsmStep2BiReportPriceLambdaQueryWrapper.orderByDesc(PgHsmStep2BiReportPrice::getCreateTime);
      pgHsmStep2BiReportPriceLambdaQueryWrapper.last(" limit 1");
    } else {
      //pgHsmStep2BiReportLambdaQueryWrapper.eq(PgHsmStep2BiReport::getExecDate, searchDate);
      pgHsmStep2BiReportPriceLambdaQueryWrapper.ge(PgHsmStep2BiReportPrice::getCreateTime, searchDate + " 01:00:00");
      pgHsmStep2BiReportPriceLambdaQueryWrapper.lt(PgHsmStep2BiReportPrice::getCreateTime, DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(searchDate).plusDays(1).toString("yyyy-MM-dd") + " 01:00:00");
      pgHsmStep2BiReportPriceLambdaQueryWrapper.groupBy(PgHsmStep2BiReportPrice::getAddressIDnum, PgHsmStep2BiReportPrice::getMonth);
    }

    List<PgHsmStep2BiReportPrice> pgHsmStep2BiReportPriceList =
            pgHsmStep2BiReportPriceMapper.selectList(pgHsmStep2BiReportPriceLambdaQueryWrapper);

    //文件清单
    List<String> fileList = new ArrayList<>();
    fileList.add("Store_Code_Seq");
    List<String> fileCountList = new ArrayList<>();
    for (PgHsmStep2BiReportPrice pgHsmStep2BiReportPrice : pgHsmStep2BiReportPriceList) {

      // 一家完整门店，含其子问卷 七大品类合集
      PgDpbTotalResult pgDpbTotalResult = new PgDpbTotalResult();
      CglibCopyBeanUtil.basicCopyBean(pgHsmStep2BiReportPrice, pgDpbTotalResult);
      pgDpbTotalResult.setDate(DateUtil.convert2String(pgHsmStep2BiReportPrice.getRidUpdateTime(), DateUtil.DTFormat.yyyy_MM_dd.getFormat()));
      pgDpbTotalResult.setIs_dcp_flag(pgHsmStep2BiReportPrice.getIsDcpFlag());

      PgDpbTotalResultForPrice pgDpbTotalResultForPrice = CglibCopyBeanUtil.doDeepClone(pgDpbTotalResult, PgDpbTotalResultForPrice.class);
      LambdaQueryWrapper<PgHsmStep2BiReportPrice> pgHsmStep2BiReportPriceLambdaQueryWrapper1 =
              new LambdaQueryWrapper<>();
      if (isOperateAlone) {
        pgHsmStep2BiReportPriceLambdaQueryWrapper1.eq(PgHsmStep2BiReportPrice::getExecDate, pgHsmStep2BiReportPrice.getExecDate());
      } else {
        //pgHsmStep2BiReportLambdaQueryWrapper1.eq(PgHsmStep2BiReport::getExecDate, searchDate);
        pgHsmStep2BiReportPriceLambdaQueryWrapper1.ge(PgHsmStep2BiReportPrice::getCreateTime, searchDate + " 01:00:00");
        pgHsmStep2BiReportPriceLambdaQueryWrapper1.lt(PgHsmStep2BiReportPrice::getCreateTime, DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(searchDate).plusDays(1).toString("yyyy-MM-dd") + " 01:00:00");
      }
      pgHsmStep2BiReportPriceLambdaQueryWrapper1.eq(
              PgHsmStep2BiReportPrice::getAddressIDnum, pgHsmStep2BiReportPrice.getAddressIDnum());
      pgHsmStep2BiReportPriceLambdaQueryWrapper1.eq(PgHsmStep2BiReportPrice::getMonth, pgHsmStep2BiReportPrice.getMonth());
      pgHsmStep2BiReportPriceLambdaQueryWrapper1.groupBy(PgHsmStep2BiReportPrice::getCategory);
      List<PgHsmStep2BiReportPrice> pgHsmStep2BiReportPriceList1 =
              pgHsmStep2BiReportPriceMapper.selectList(pgHsmStep2BiReportPriceLambdaQueryWrapper1);
      boolean hasPriceNode = false;
      for (PgHsmStep2BiReportPrice pgHsmStep2BiReportPrice1 : pgHsmStep2BiReportPriceList1) {

        List<PgHsmSkuDetail> pgHsmDetailDTOList = new ArrayList<>();
        pgHsmDetailDTOList =
                pgDpbReportMapper.getPgHsmDetailDTOListByCategory(
                        pgHsmStep2BiReportPrice1.getCategory(), pgHsmStep2BiReportPrice1.getResponseId(),
                        pgHsmStep2BiReportPrice1.getMonth(),
                        pgHsmStep2BiReportPrice1.getDate());

        // 按kpi type分组
        Map<String, List<PgHsmSkuDetail>> collect =
                pgHsmDetailDTOList.stream()
                        .collect(Collectors.groupingBy(p -> p.getKpiType(), Collectors.toList()));

        PgDpbCategoryDetail pgDpbCategoryDetail = new PgDpbCategoryDetail();
        CglibCopyBeanUtil.basicCopyBean(pgHsmStep2BiReportPrice1, pgDpbCategoryDetail);

        PgDpbCategoryDetailForPrice pgDpbCategoryDetailForPrice = CglibCopyBeanUtil.doClone(pgDpbCategoryDetail, PgDpbCategoryDetailForPrice.class);
        pgDpbCategoryDetail = getGroupDataByKpiType(collect, pgDpbCategoryDetail, pgHsmStep2BiReportPrice1.getDate(), false);

        //添加价格节点
        List<PgHsmSkupriceBiReport> pgHsmSkupriceBiReportList = pgHsmSkupriceBiReportMapper.selectList(new LambdaQueryWrapper<PgHsmSkupriceBiReport>().eq(PgHsmSkupriceBiReport::getCategory, pgHsmStep2BiReportPrice1.getCategory()).eq(PgHsmSkupriceBiReport::getTaskAddressId, pgHsmStep2BiReportPrice1.getResponseId()));
        if (CollectionUtils.isNotEmpty(pgHsmSkupriceBiReportList)){
          hasPriceNode = true;
          List<PgPriceSkuDetail> pgPriceSkuDetailList = Lists.newArrayList();
          pgHsmSkupriceBiReportList.forEach(pgPriceSkuDetail -> {
            PgPriceSkuDetail pgPriceSkuDetail1 = new PgPriceSkuDetail();
            pgPriceSkuDetail1.setId(pgPriceSkuDetail.getId());
            pgPriceSkuDetail1.setKpiName(pgPriceSkuDetail.getSkuName());
            pgPriceSkuDetail1.setBarCode(pgPriceSkuDetail.getBarcode());
            pgPriceSkuDetail1.setKpiValue(pgPriceSkuDetail.getPrice());
            pgPriceSkuDetail1.setCategory(pgPriceSkuDetail.getCategory());
            pgPriceSkuDetail1.setKpiType(pgPriceSkuDetail.getType());
            pgPriceSkuDetailList.add(pgPriceSkuDetail1);
          });
          pgDpbCategoryDetailForPrice.getPgHsmDetail().setPriceList(pgPriceSkuDetailList);
        }
        pgDpbTotalResult
                .getKPIData()
                .add(classifyCategory(pgHsmStep2BiReportPrice1.getCategory(), pgDpbCategoryDetail));

        pgDpbTotalResultForPrice
                .getKPIData()
                .add(classifyCategoryForPrice(pgHsmStep2BiReportPrice1.getCategory(), pgDpbCategoryDetailForPrice));
      }
      //价格节点传输
      if (hasPriceNode){
        String resultJsonForPrice = JsonUtil.toJsonString(pgDpbTotalResultForPrice);
        String fileNameForPrice = pgDpbTotalResult.getAddressIDnum()
                + "_price_"
                + new DateTime().toString(DateUtil.DTFormat.yyyyMMddHHmmss.getFormat());
        uploadAzureBlob(resultJsonForPrice, fileNameForPrice, blobContainerClientForPrice);
      }
    }
  }

  /**
   * 获取PgDcp结果
   *
   * @param searchDate 查询日期 形如2021-06-02，如无则默认前一天
   * @return
   */
  public void getPgDcpBiResult(String searchDate, String addressIDnum) {

    BlobServiceClient blobServiceClient =
            new BlobServiceClientBuilder().endpoint(BLOB_SAS_TOKEN).buildClient();

    // 要求是yyyyMMdd
    String folderName = "";
    DateTime nowTime = new DateTime();

    // 是否单独跑某一家门店
    boolean isOperateAlone = false;
    if (StringUtils.isNotBlank(addressIDnum)) {
      isOperateAlone = true;
    }

    if (StringUtils.isBlank(searchDate)) {
      searchDate = nowTime.minusDays(1).toString(DateUtil.DTFormat.yyyy_MM_dd.getFormat());
      folderName = nowTime.toString(DateUtil.DTFormat.yyyyMMdd.getFormat());
    } else {
      // 指定日期文件夹也为后一天日期
      DateTime searchDateDT = new DateTime(searchDate + "T00:00:00.000+08:00");
      folderName = searchDateDT.plusDays(1).toString(DateUtil.DTFormat.yyyyMMdd.getFormat());
    }
    // 单独门店放到新一天文件夹
    if (isOperateAlone) {
      folderName = nowTime.plusDays(1).toString(DateUtil.DTFormat.yyyyMMdd.getFormat());
    }

    BlobContainerClient blobContainerClient =
            blobServiceClient.getBlobContainerClient(BLOB_CONTAINER_DCP + folderName);
    LambdaQueryWrapper<PgHsmStep2BiReportDcp> pgHsmStep2BiReportDcpLambdaQueryWrapper =
            new LambdaQueryWrapper<>();
    if (isOperateAlone) {
      pgHsmStep2BiReportDcpLambdaQueryWrapper.eq(PgHsmStep2BiReportDcp::getAddressIDnum, addressIDnum);
      if (StringUtils.isNotBlank(searchDate)){
        //pgHsmStep2BiReportDcpLambdaQueryWrapper.eq(PgHsmStep2BiReportDcp::getExecDate, searchDate);
        // 将 getExecDate 改为 getCreateTime，并设置时间范围为当天凌晨1点到次日凌晨1点
        pgHsmStep2BiReportDcpLambdaQueryWrapper.ge(PgHsmStep2BiReportDcp::getCreateTime, searchDate + " 01:00:00");
        pgHsmStep2BiReportDcpLambdaQueryWrapper.lt(PgHsmStep2BiReportDcp::getCreateTime, DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(searchDate).plusDays(1).toString("yyyy-MM-dd") + " 01:00:00");
      }
      pgHsmStep2BiReportDcpLambdaQueryWrapper.orderByDesc(PgHsmStep2BiReportDcp::getCreateTime);
      //pgHsmStep2BiReportDcpLambdaQueryWrapper.last(" limit 1");
    } else {
      //pgHsmStep2BiReportDcpLambdaQueryWrapper.eq(PgHsmStep2BiReportDcp::getExecDate, searchDate);
      pgHsmStep2BiReportDcpLambdaQueryWrapper.ge(PgHsmStep2BiReportDcp::getCreateTime, searchDate + " 01:00:00");
      pgHsmStep2BiReportDcpLambdaQueryWrapper.lt(PgHsmStep2BiReportDcp::getCreateTime, DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(searchDate).plusDays(1).toString("yyyy-MM-dd") + " 01:00:00");
      pgHsmStep2BiReportDcpLambdaQueryWrapper.groupBy(PgHsmStep2BiReportDcp::getResponseId, PgHsmStep2BiReportDcp::getMonth);
    }

    List<PgHsmStep2BiReportDcp> pgHsmStep2BiReportDcpList =
            pgHsmStep2BiReportDcpMapper.selectList(pgHsmStep2BiReportDcpLambdaQueryWrapper);

    //文件清单
    List<String> fileList = new ArrayList<>();
    fileList.add("Store_Code_Seq");
    List<String> fileCountList = new ArrayList<>();
    for (PgHsmStep2BiReportDcp pgHsmStep2BiReportDcp : pgHsmStep2BiReportDcpList) {
      // 一家完整门店，含其子问卷 七大品类合集
      PgDpbTotalResult pgDpbTotalResult = new PgDpbTotalResult();
      CglibCopyBeanUtil.basicCopyBean(pgHsmStep2BiReportDcp, pgDpbTotalResult);
      pgDpbTotalResult.setDate(DateUtil.convert2String(pgHsmStep2BiReportDcp.getRidUpdateTime(), DateUtil.DTFormat.yyyy_MM_dd.getFormat()));
      pgDpbTotalResult.setMonth(DateUtil.convert2String(pgHsmStep2BiReportDcp.getRidUpdateTime(), DateUtil.DTFormat.yyyyMM.getFormat()));
      pgDpbTotalResult.setIs_dcp_flag(pgHsmStep2BiReportDcp.getIsDcpFlag());
      LambdaQueryWrapper<PgHsmStep2BiReportDcp> pgHsmStep2BiReportLambdaQueryWrapper1 =
              new LambdaQueryWrapper<>();
      if (isOperateAlone) {
        pgHsmStep2BiReportLambdaQueryWrapper1.eq(PgHsmStep2BiReportDcp::getExecDate, pgHsmStep2BiReportDcp.getExecDate());
      } else {
        //pgHsmStep2BiReportLambdaQueryWrapper1.eq(PgHsmStep2BiReportDcp::getExecDate, searchDate);
        pgHsmStep2BiReportLambdaQueryWrapper1.ge(PgHsmStep2BiReportDcp::getCreateTime, searchDate + " 01:00:00");
        pgHsmStep2BiReportLambdaQueryWrapper1.lt(PgHsmStep2BiReportDcp::getCreateTime, DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(searchDate).plusDays(1).toString("yyyy-MM-dd") + " 01:00:00");
      }
      pgHsmStep2BiReportLambdaQueryWrapper1.eq(
              PgHsmStep2BiReportDcp::getAddressIDnum, pgHsmStep2BiReportDcp.getAddressIDnum());
      pgHsmStep2BiReportLambdaQueryWrapper1.eq(PgHsmStep2BiReportDcp::getMonth, pgHsmStep2BiReportDcp.getMonth());
      pgHsmStep2BiReportLambdaQueryWrapper1.eq(PgHsmStep2BiReportDcp::getResponseId, pgHsmStep2BiReportDcp.getResponseId());
      pgHsmStep2BiReportLambdaQueryWrapper1.groupBy(PgHsmStep2BiReportDcp::getCategory);
      List<PgHsmStep2BiReportDcp> pgHsmStep2BiReportDcpList1 =
              pgHsmStep2BiReportDcpMapper.selectList(pgHsmStep2BiReportLambdaQueryWrapper1);
      List<PgHsmCdlPushDetailLog> pgHsmCdlPushDetailLogList = Lists.newArrayList();
      for (PgHsmStep2BiReportDcp pgHsmStep2BiReportDcp1 : pgHsmStep2BiReportDcpList1) {
        List<PgHsmSkuDetail> pgHsmDetailDTOList = new ArrayList<>();
        pgHsmDetailDTOList =
                pgDpbReportMapper.getPgHsmDetailDTOListByCategoryDcp(
                        pgHsmStep2BiReportDcp1.getCategory(), pgHsmStep2BiReportDcp1.getResponseId(),
                        pgHsmStep2BiReportDcp1.getMonth(),
                        pgHsmStep2BiReportDcp1.getDate());

        List<PgHsmCdlPushDetailLog> pgHsmCdlPushDetailLogList1 = CglibCopyBeanUtil.doBatchClone(pgHsmDetailDTOList, PgHsmCdlPushDetailLog.class);
        if (CollectionUtils.isNotEmpty(pgHsmCdlPushDetailLogList1)){
          pgHsmCdlPushDetailLogList1.forEach(pgHsmCdlPushDetailLog -> {
            pgHsmCdlPushDetailLog.setId(null);
            pgHsmCdlPushDetailLog.setExecDate(pgHsmStep2BiReportDcp1.getExecDate());
            pgHsmCdlPushDetailLog.setTaskId(pgHsmStep2BiReportDcp1.getTaskId());
            pgHsmCdlPushDetailLog.setResponseId(pgHsmStep2BiReportDcp1.getResponseId());
            pgHsmCdlPushDetailLog.setAddressIDnum(pgHsmStep2BiReportDcp1.getAddressIDnum());
            pgHsmCdlPushDetailLog.setMonth(pgHsmStep2BiReportDcp1.getMonth());
            pgHsmCdlPushDetailLog.setDate(pgHsmStep2BiReportDcp1.getDate());
            pgHsmCdlPushDetailLog.setCustomerType(1);
          });
          pgHsmCdlPushDetailLogList.addAll(pgHsmCdlPushDetailLogList1);
        }

        // 按kpi type分组
        Map<String, List<PgHsmSkuDetail>> collect =
                pgHsmDetailDTOList.stream()
                        .collect(Collectors.groupingBy(p -> p.getKpiType(), Collectors.toList()));

        PgDpbCategoryDetail pgDpbCategoryDetail = new PgDpbCategoryDetail();
        CglibCopyBeanUtil.basicCopyBean(pgHsmStep2BiReportDcp1, pgDpbCategoryDetail);
        pgDpbCategoryDetail = getGroupDataByKpiType(collect, pgDpbCategoryDetail, pgHsmStep2BiReportDcp1.getDate(), true);

        pgDpbTotalResult
                .getKPIData()
                .add(classifyCategory(pgHsmStep2BiReportDcp1.getCategory(), pgDpbCategoryDetail));
      }
      String resultJson = JsonUtil.toJsonString(pgDpbTotalResult);
      String fileName = pgHsmStep2BiReportDcp.getAddressIDnum()
              + "_"
              + pgHsmStep2BiReportDcp.getResponseId()
              + "_"
              + new DateTime().toString(DateUtil.DTFormat.yyyyMMddHHmmss.getFormat());
      boolean uploadResult = uploadAzureBlob(resultJson, fileName, blobContainerClient);
      if (uploadResult){
        try {
          String callbackResult = HttpConnectionUtils.postRequest(DCP_CDL_CALLBACK, resultJson, 30000);
          logger.info("result = " + callbackResult);
        } catch (Exception e) {
          logger.error("dcp 回调失败， url =" + DCP_CDL_CALLBACK + ",json =" + resultJson);
        }
      }
      try {
        //批量插入日志记录
        insertBatch(pgHsmCdlPushDetailLogList);
      } catch (Exception e) {
        logger.error("dcp 日志写入失败:" + e.toString());
      }
    }

  }

  /**
   * 获取PgDpb结果 带单店返回结果，帮pm做统计临时用
   *
   * @param searchDate
   * @param addressIDnum
   * @return
   */
  public boolean getPgDpbBiResultWithResult(String searchDate, String addressIDnum) {

    BlobServiceClient blobServiceClient =
        new BlobServiceClientBuilder().endpoint(BLOB_SAS_TOKEN).buildClient();

    // 要求是yyyyMMdd
    String folderName = "";
    DateTime nowTime = new DateTime();

    // 是否单独跑某一家门店
    boolean isOperateAlone = false;
    if (StringUtils.isNotBlank(addressIDnum)) {
      isOperateAlone = true;
    }

    if (StringUtils.isBlank(searchDate)) {
      searchDate = nowTime.minusDays(1).toString(DateUtil.DTFormat.yyyy_MM_dd.getFormat());
      folderName = nowTime.toString(DateUtil.DTFormat.yyyyMMdd.getFormat());
    } else {
      // 指定日期文件夹也为后一天日期
      DateTime searchDateDT = new DateTime(searchDate + "T00:00:00.000+08:00");
      folderName = searchDateDT.plusDays(1).toString(DateUtil.DTFormat.yyyyMMdd.getFormat());
    }
    // 单独门店放到新一天文件夹
    if (isOperateAlone) {
      folderName = nowTime.plusDays(1).toString(DateUtil.DTFormat.yyyyMMdd.getFormat());
    }

    BlobContainerClient blobContainerClient =
        blobServiceClient.getBlobContainerClient(BLOB_CONTAINER + folderName);

    LambdaQueryWrapper<PgHsmStep2BiReport> pgHsmStep2BiReportLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    if (isOperateAlone) {
      pgHsmStep2BiReportLambdaQueryWrapper.eq(PgHsmStep2BiReport::getAddressIDnum, addressIDnum);
      pgHsmStep2BiReportLambdaQueryWrapper.orderByDesc(PgHsmStep2BiReport::getCreateTime);
      // pgHsmStep2BiReportLambdaQueryWrapper.lt(PgHsmStep2BiReport::getCreateTime, "2021-07-28");
      pgHsmStep2BiReportLambdaQueryWrapper.last(" limit 1");
    } else {
//      pgHsmStep2BiReportLambdaQueryWrapper.eq(PgHsmStep2BiReport::getExecDate, searchDate);
      pgHsmStep2BiReportLambdaQueryWrapper.ge(PgHsmStep2BiReport::getCreateTime, searchDate + " 01:00:00");
      pgHsmStep2BiReportLambdaQueryWrapper.lt(PgHsmStep2BiReport::getCreateTime, DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(searchDate).plusDays(1).toString("yyyy-MM-dd") + " 01:00:00");
      pgHsmStep2BiReportLambdaQueryWrapper.groupBy(PgHsmStep2BiReport::getAddressIDnum);
    }
    List<String> dateList = pgDpbReportMapper.getPgHsmStep2BiReportDateList(searchDate);

    List<PgHsmStep2BiReport> pgHsmStep2BiReportList =
        pgHsmStep2BiReportMapper.selectList(pgHsmStep2BiReportLambdaQueryWrapper);

    for (PgHsmStep2BiReport pgHsmStep2BiReport : pgHsmStep2BiReportList) {
      // 一家完整门店，含其子问卷 七大品类合集
      PgDpbTotalResult pgDpbTotalResult = new PgDpbTotalResult();
      CglibCopyBeanUtil.basicCopyBean(pgHsmStep2BiReport, pgDpbTotalResult);

      LambdaQueryWrapper<PgHsmStep2BiReport> pgHsmStep2BiReportLambdaQueryWrapper1 =
          new LambdaQueryWrapper<>();
      if (isOperateAlone) {
        pgHsmStep2BiReportLambdaQueryWrapper1.eq(
            PgHsmStep2BiReport::getExecDate, pgHsmStep2BiReport.getExecDate());
        // pgHsmStep2BiReportLambdaQueryWrapper.lt(PgHsmStep2BiReport::getCreateTime, "2021-07-28");
      } else {
        //pgHsmStep2BiReportLambdaQueryWrapper1.eq(PgHsmStep2BiReport::getExecDate, searchDate);
        pgHsmStep2BiReportLambdaQueryWrapper1.ge(PgHsmStep2BiReport::getCreateTime, searchDate + " 01:00:00");
        pgHsmStep2BiReportLambdaQueryWrapper1.lt(PgHsmStep2BiReport::getCreateTime, DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(searchDate).plusDays(1).toString("yyyy-MM-dd") + " 01:00:00");
      }
      pgHsmStep2BiReportLambdaQueryWrapper1.eq(
          PgHsmStep2BiReport::getAddressIDnum, pgHsmStep2BiReport.getAddressIDnum());
      pgHsmStep2BiReportLambdaQueryWrapper1.groupBy(PgHsmStep2BiReport::getResponseId);
      List<PgHsmStep2BiReport> pgHsmStep2BiReportList1 =
          pgHsmStep2BiReportMapper.selectList(pgHsmStep2BiReportLambdaQueryWrapper1);

      for (PgHsmStep2BiReport pgHsmStep2BiReport1 : pgHsmStep2BiReportList1) {

        List<PgHsmSkuDetail> pgHsmDetailDTOList = new ArrayList<>();
        pgHsmDetailDTOList =
            pgDpbReportMapper.getPgHsmDetailDTOList(
                pgHsmStep2BiReport1.getResponseId(),
                pgHsmStep2BiReport1.getMonth(),
                pgHsmStep2BiReport1.getDate());

        // 按kpi type分组
        Map<String, List<PgHsmSkuDetail>> collect =
            pgHsmDetailDTOList.stream()
                .collect(Collectors.groupingBy(p -> p.getKpiType(), Collectors.toList()));

        PgDpbCategoryDetail pgDpbCategoryDetail = new PgDpbCategoryDetail();
        CglibCopyBeanUtil.basicCopyBean(pgHsmStep2BiReport1, pgDpbCategoryDetail);

        pgDpbCategoryDetail = getGroupDataByKpiType(collect, pgDpbCategoryDetail, pgHsmStep2BiReport1.getDate(), false);

        pgDpbTotalResult
            .getKPIData()
            .add(classifyCategory(pgHsmStep2BiReport1.getCategory(), pgDpbCategoryDetail));
      }
      String resultJson = JsonUtil.toJsonString(pgDpbTotalResult);
      String fileName = pgDpbTotalResult.getAddressIDnum()
              + "_"
              + pgHsmStep2BiReport.getStoreCodeSeq()
              + "_"
              + new DateTime().toString(DateUtil.DTFormat.yyyyMMddHHmmss.getFormat());
      uploadAzureBlob(resultJson, fileName, blobContainerClient);
    }
    if (CollectionUtils.isNotEmpty(pgHsmStep2BiReportList)) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * 根据KpiType分组
   *
   * @param collect
   * @return
   */
  public PgDpbCategoryDetail getGroupDataByKpiType(
      Map<String, List<PgHsmSkuDetail>> collect, PgDpbCategoryDetail pgDpbCategoryDetail, String date, boolean isDcp) {

    if (collect != null && collect.size() > 0) {
      for (String key : collect.keySet()) {
        switch (key) {
          case KPI_TYPE_BR_OTHER:
            pgDpbCategoryDetail.getPgHsmDetail().setBrOtherList(collect.get(KPI_TYPE_BR_OTHER));
            break;
          case KPI_TYPE_DISPLAY_DISTRIBUTION:
            pgDpbCategoryDetail
                .getPgHsmDetail()
                .setDisplayDistributionList(collect.get(KPI_TYPE_DISPLAY_DISTRIBUTION));
            break;
          case KPI_TYPE_JOINT_DISPLAY:
            pgDpbCategoryDetail
                .getPgHsmDetail()
                .setJointDisplayList(collect.get(KPI_TYPE_JOINT_DISPLAY));
            break;
          case KPI_TYPE_OTHER_DISPLAY:
            pgDpbCategoryDetail
                .getPgHsmDetail()
                .setOtherDisplayList(collect.get(KPI_TYPE_OTHER_DISPLAY));
            break;
          case KPI_TYPE_OTHER_SHELF:
            pgDpbCategoryDetail
                .getPgHsmDetail()
                .setOtherShelfList(collect.get(KPI_TYPE_OTHER_SHELF));
            break;
          case KPI_TYPE_PG_DISPLAY:
            pgDpbCategoryDetail.getPgHsmDetail().setPgDisplayList(collect.get(KPI_TYPE_PG_DISPLAY));
            break;
          case KPI_TYPE_PG_SHELF:
            pgDpbCategoryDetail.getPgHsmDetail().setPgShelfList(collect.get(KPI_TYPE_PG_SHELF));
            break;
          case KPI_TYPE_PICTURE:
            pgDpbCategoryDetail.getPgHsmDetail().setPictureList(collect.get(KPI_TYPE_PICTURE));
            break;
          case KPI_TYPE_PSKU:
            List<PgHsmSkuDetail> pskuList = collect.get(KPI_TYPE_PSKU);
            pgDpbCategoryDetail.getPgHsmDetail().setPskuList(collect.get(KPI_TYPE_PSKU));
            if (CollectionUtils.isNotEmpty(pskuList)){
              List<PskuListKpi> listPskuListKpi = null;
              if (isDcp){
                listPskuListKpi = listPskuListKpiDcp(pgDpbCategoryDetail, date);
              }else {
                listPskuListKpi = listPskuListKpi(pgDpbCategoryDetail, date);
              }
              pgDpbCategoryDetail.getPgHsmDetail().setPskuListKpi(listPskuListKpi);
            }
            break;
          case KPI_TYPE_SAMPLE_DISPLAY:
            pgDpbCategoryDetail
                .getPgHsmDetail()
                .setSampleDisplayList(collect.get(KPI_TYPE_SAMPLE_DISPLAY));
            break;
          case KPI_TYPE_SHELF_DISTRIBUTION:
            pgDpbCategoryDetail
                .getPgHsmDetail()
                .setShelfDistributionList(collect.get(KPI_TYPE_SHELF_DISTRIBUTION));
            break;
          case KPI_TYPE_STORE_QUALITY:
            pgDpbCategoryDetail
                .getPgHsmDetail()
                .setStoreQualityList(collect.get(KPI_TYPE_STORE_QUALITY));
            break;
          case KPI_TYPE_SUMMARY: // 需要单独拿出放到另一层级
            pgDpbCategoryDetail.getPgHsmSummary().setSummaryList(collect.get(KPI_TYPE_SUMMARY));
            break;
          case KPI_TYPE_TOTAL_DISPLAY:
            pgDpbCategoryDetail
                .getPgHsmDetail()
                .setTotalDisplayList(collect.get(KPI_TYPE_TOTAL_DISPLAY));
            break;
          case KPI_TYPE_TOTAL_SHELF:
            pgDpbCategoryDetail
                .getPgHsmDetail()
                .setTotalShelfList(collect.get(KPI_TYPE_TOTAL_SHELF));
            break;
          case KPI_TYPE_SEGMENT:
            pgDpbCategoryDetail
                    .getPgHsmDetail()
                    .setSegmentList(collect.get(KPI_TYPE_SEGMENT));
            break;
        }
      }
    }

    return pgDpbCategoryDetail;
  }

  /**
   * 封装PskuListKpi数据
   * @param pgDpbCategoryDetail
   * @param
   * @return
   */
  public List<PskuListKpi> listPskuListKpi(PgDpbCategoryDetail pgDpbCategoryDetail, String date){
    List<PskuListKpi> listPskuListKpi = Lists.newArrayList();

    List<String> kpiNameList = Lists.newArrayList();
    List<PgHsmDetailBiReportDetail> pgHsmDetailBiReportDetailTotalList = Lists.newArrayList();
    Map<String, List<PgHsmDetailBiReportDetail>> mapKeyPgHsmDetailBiReportDetailList = Maps.newHashMap();
    if (CollectionUtils.isNotEmpty(pgDpbCategoryDetail.getPgHsmDetail().getPskuList())){
      kpiNameList = pgDpbCategoryDetail.getPgHsmDetail().getPskuList().stream().map(PgHsmSkuDetail::getKpiName).collect(Collectors.toList());
    }
    List<List<String>> kpiNameGroupList = Lists.partition(kpiNameList, 100);
    for (List<String> kpiNames : kpiNameGroupList){
      LambdaQueryWrapper<PgHsmDetailBiReportDetail> pgHsmDetailBiReportDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
      pgHsmDetailBiReportDetailLambdaQueryWrapper.in(PgHsmDetailBiReportDetail::getKpiName, kpiNames);
      pgHsmDetailBiReportDetailLambdaQueryWrapper.eq(PgHsmDetailBiReportDetail::getDate, date);
      pgHsmDetailBiReportDetailLambdaQueryWrapper.eq(PgHsmDetailBiReportDetail::getResponseId, pgDpbCategoryDetail.getResponseId());
      List<PgHsmDetailBiReportDetail> pgHsmDetailBiReportDetailList = pgHsmDetailBiReportDetailMapper.selectList(pgHsmDetailBiReportDetailLambdaQueryWrapper);
      if (CollectionUtils.isNotEmpty(pgHsmDetailBiReportDetailList)){
        pgHsmDetailBiReportDetailTotalList.addAll(pgHsmDetailBiReportDetailList);
      }
    }
    //response_id+kpi_name+date  分组
    mapKeyPgHsmDetailBiReportDetailList = pgHsmDetailBiReportDetailTotalList.stream()
            .collect(Collectors.groupingBy(p -> p.getResponseId() + p.getKpiName() + p.getDate(), Collectors.toList()));

    for (PgHsmSkuDetail pgHsmSkuDetail : pgDpbCategoryDetail.getPgHsmDetail().getPskuList()){
      PskuListKpi pskuListKpi = new PskuListKpi();
      CglibCopyBeanUtil.basicCopyBean(pgHsmSkuDetail, pskuListKpi);
      List<PskuListKpiSkuDetails> skuDetailsList = Lists.newArrayList();

      List<PgHsmDetailBiReportDetail> pgHsmDetailBiReportDetailList = mapKeyPgHsmDetailBiReportDetailList.get(pgDpbCategoryDetail.getResponseId() + pgHsmSkuDetail.getKpiName() + date);
      if (CollectionUtils.isNotEmpty(pgHsmDetailBiReportDetailList)){
          for (PgHsmDetailBiReportDetail pgHsmDetailBiReportDetail : pgHsmDetailBiReportDetailList){
            PskuListKpiSkuDetails pskuListKpiSkuDetails = new PskuListKpiSkuDetails();
            pskuListKpiSkuDetails.setId(pgHsmDetailBiReportDetail.getId());
            pskuListKpiSkuDetails.setKpiName(pgHsmDetailBiReportDetail.getKpiNameDetail());
            pskuListKpiSkuDetails.setCategory(pgHsmDetailBiReportDetail.getCategory());
            pskuListKpiSkuDetails.setKpiType(KPI_TYPE_PSKU);
            String kpiValue = "";
            if (StringUtils.isNotBlank(pskuListKpi.getKpiValue())){
              kpiValue = Objects.isNull(pgHsmDetailBiReportDetail.getKpiValueDetail()) ? "" : pgHsmDetailBiReportDetail.getKpiValueDetail();
            }
            pskuListKpiSkuDetails.setKpiValue(kpiValue);
            pskuListKpiSkuDetails.setBarCode(pgHsmDetailBiReportDetail.getBarCodeDetail());
            skuDetailsList.add(pskuListKpiSkuDetails);
          }
      }
      pskuListKpi.setKpiType(KPI_TYPE_PSKU);
      pskuListKpi.setSkuDetails(skuDetailsList);
      listPskuListKpi.add(pskuListKpi);
    }
    return listPskuListKpi;
  }

  /**
   * 封装DCP PskuListKpi数据
   * @param pgDpbCategoryDetail
   * @param date
   * @return
   */
  public List<PskuListKpi> listPskuListKpiDcp(PgDpbCategoryDetail pgDpbCategoryDetail, String date){

    List<PskuListKpi> listPskuListKpi = Lists.newArrayList();

    List<String> kpiNameList = Lists.newArrayList();
    List<PgHsmDetailBiReportDetailDcp> pgHsmDetailBiReportDetailTotalList = Lists.newArrayList();
    Map<String, List<PgHsmDetailBiReportDetailDcp>> mapKeyPgHsmDetailBiReportDetailList = Maps.newHashMap();
    if (CollectionUtils.isNotEmpty(pgDpbCategoryDetail.getPgHsmDetail().getPskuList())){
      kpiNameList = pgDpbCategoryDetail.getPgHsmDetail().getPskuList().stream().map(PgHsmSkuDetail::getKpiName).collect(Collectors.toList());
    }
    List<List<String>> kpiNameGroupList = Lists.partition(kpiNameList, 100);
    for (List<String> kpiNames : kpiNameGroupList){
      LambdaQueryWrapper<PgHsmDetailBiReportDetailDcp> pgHsmDetailBiReportDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
      pgHsmDetailBiReportDetailLambdaQueryWrapper.in(PgHsmDetailBiReportDetailDcp::getKpiName, kpiNames);
      pgHsmDetailBiReportDetailLambdaQueryWrapper.eq(PgHsmDetailBiReportDetailDcp::getDate, date);
      pgHsmDetailBiReportDetailLambdaQueryWrapper.eq(PgHsmDetailBiReportDetailDcp::getResponseId, pgDpbCategoryDetail.getResponseId());
      List<PgHsmDetailBiReportDetailDcp> pgHsmDetailBiReportDetailList = pgHsmDetailBiReportDetailDcpMapper.selectList(pgHsmDetailBiReportDetailLambdaQueryWrapper);
      if (CollectionUtils.isNotEmpty(pgHsmDetailBiReportDetailList)){
        pgHsmDetailBiReportDetailTotalList.addAll(pgHsmDetailBiReportDetailList);
      }
    }
    //response_id+kpi_name+date  分组
    mapKeyPgHsmDetailBiReportDetailList = pgHsmDetailBiReportDetailTotalList.stream()
            .collect(Collectors.groupingBy(p -> p.getResponseId() + p.getKpiName() + p.getDate(), Collectors.toList()));

    for (PgHsmSkuDetail pgHsmSkuDetail : pgDpbCategoryDetail.getPgHsmDetail().getPskuList()){
      PskuListKpi pskuListKpi = new PskuListKpi();
      CglibCopyBeanUtil.basicCopyBean(pgHsmSkuDetail, pskuListKpi);
      List<PskuListKpiSkuDetails> skuDetailsList = Lists.newArrayList();

      List<PgHsmDetailBiReportDetailDcp> pgHsmDetailBiReportDetailList = mapKeyPgHsmDetailBiReportDetailList.get(pgDpbCategoryDetail.getResponseId() + pgHsmSkuDetail.getKpiName() + date);
      if (CollectionUtils.isNotEmpty(pgHsmDetailBiReportDetailList)){
        for (PgHsmDetailBiReportDetailDcp pgHsmDetailBiReportDetail : pgHsmDetailBiReportDetailList){
          PskuListKpiSkuDetails pskuListKpiSkuDetails = new PskuListKpiSkuDetails();
          pskuListKpiSkuDetails.setId(pgHsmDetailBiReportDetail.getId());
          pskuListKpiSkuDetails.setKpiName(pgHsmDetailBiReportDetail.getKpiNameDetail());
          pskuListKpiSkuDetails.setCategory(pgHsmDetailBiReportDetail.getCategory());
          pskuListKpiSkuDetails.setKpiType(KPI_TYPE_PSKU);
          String kpiValue = "";
          if (StringUtils.isNotBlank(pskuListKpi.getKpiValue())){
            kpiValue = Objects.isNull(pgHsmDetailBiReportDetail.getKpiValueDetail()) ? "" : pgHsmDetailBiReportDetail.getKpiValueDetail();
          }
          pskuListKpiSkuDetails.setKpiValue(kpiValue);
          pskuListKpiSkuDetails.setBarCode(pgHsmDetailBiReportDetail.getBarCodeDetail());
          skuDetailsList.add(pskuListKpiSkuDetails);
        }
      }
      pskuListKpi.setKpiType(KPI_TYPE_PSKU);
      pskuListKpi.setSkuDetails(skuDetailsList);
      listPskuListKpi.add(pskuListKpi);
    }
    return listPskuListKpi;
  }

  /**
   * 根据七大品类类型对数据分类
   *
   * @param category
   * @param pgDpbCategoryDetail
   * @return
   */
  public PgDpbKpiData classifyCategory(String category, PgDpbCategoryDetail pgDpbCategoryDetail) {

    PgDpbKpiData pgDpbKpiData = new PgDpbKpiData();
    List<PgDpbCategoryDetail> pgDpbCategoryDetailList = new ArrayList<>();
    pgDpbCategoryDetailList.add(pgDpbCategoryDetail);

    switch (category) {
      case CATEGORY_BABY:
        pgDpbKpiData.setCategory(CATEGORY_BABY);
        pgDpbKpiData.setData(pgDpbCategoryDetailList);
        break;
      case CATEGORY_FABRIC:
        pgDpbKpiData.setCategory(CATEGORY_FABRIC);
        pgDpbKpiData.setData(pgDpbCategoryDetailList);
        break;
      case CATEGORY_FEM:
        pgDpbKpiData.setCategory(CATEGORY_FEM);
        pgDpbKpiData.setData(pgDpbCategoryDetailList);
        break;
      case CATEGORY_HAIR:
        pgDpbKpiData.setCategory(CATEGORY_HAIR);
        pgDpbKpiData.setData(pgDpbCategoryDetailList);
        break;
      case CATEGORY_ORAL:
        pgDpbKpiData.setCategory(CATEGORY_ORAL);
        pgDpbKpiData.setData(pgDpbCategoryDetailList);
        break;
      case CATEGORY_PCC:
        pgDpbKpiData.setCategory(CATEGORY_PCC);
        pgDpbKpiData.setData(pgDpbCategoryDetailList);
        break;
      case CATEGORY_SHAVE:
        pgDpbKpiData.setCategory(CATEGORY_SHAVE);
        pgDpbKpiData.setData(pgDpbCategoryDetailList);
        break;
    }

    return pgDpbKpiData;
  }

  public PgDpbKpiDataForPrice classifyCategoryForPrice(String category, PgDpbCategoryDetailForPrice pgDpbCategoryDetail) {

    PgDpbKpiDataForPrice pgDpbKpiData = new PgDpbKpiDataForPrice();
    List<PgDpbCategoryDetailForPrice> pgDpbCategoryDetailList = new ArrayList<>();
    pgDpbCategoryDetailList.add(pgDpbCategoryDetail);

    switch (category) {
      case CATEGORY_BABY:
        pgDpbKpiData.setCategory(CATEGORY_BABY);
        pgDpbKpiData.setData(pgDpbCategoryDetailList);
        break;
      case CATEGORY_FABRIC:
        pgDpbKpiData.setCategory(CATEGORY_FABRIC);
        pgDpbKpiData.setData(pgDpbCategoryDetailList);
        break;
      case CATEGORY_FEM:
        pgDpbKpiData.setCategory(CATEGORY_FEM);
        pgDpbKpiData.setData(pgDpbCategoryDetailList);
        break;
      case CATEGORY_HAIR:
        pgDpbKpiData.setCategory(CATEGORY_HAIR);
        pgDpbKpiData.setData(pgDpbCategoryDetailList);
        break;
      case CATEGORY_ORAL:
        pgDpbKpiData.setCategory(CATEGORY_ORAL);
        pgDpbKpiData.setData(pgDpbCategoryDetailList);
        break;
      case CATEGORY_PCC:
        pgDpbKpiData.setCategory(CATEGORY_PCC);
        pgDpbKpiData.setData(pgDpbCategoryDetailList);
        break;
      case CATEGORY_SHAVE:
        pgDpbKpiData.setCategory(CATEGORY_SHAVE);
        pgDpbKpiData.setData(pgDpbCategoryDetailList);
        break;
    }

    return pgDpbKpiData;
  }

  /**
   * 上传文件到微软AzureBlob
   *
   * @param jsonString
   * @param fileName
   * @param blobContainerClient
   */
  public boolean uploadAzureBlob(
      String jsonString, String fileName, BlobContainerClient blobContainerClient) {

    /* 此处长度有限制, 部分文件会超限, 故不采取流的方式
    BlockBlobClient blockBlobClient = blobContainerClient.getBlobClient(fileName).getBlockBlobClient();
    try (ByteArrayInputStream dataStream = new ByteArrayInputStream(jsonString.getBytes())) {
        blockBlobClient.upload(dataStream, jsonString.length());
    } catch (IOException e) {
        e.printStackTrace();
    }*/
    boolean result = true;
    BlobClient blobClient = blobContainerClient.getBlobClient(fileName + ".json");
    File sourceFile = null;
    try {
      String tempFilePath = "./data/";
      String filePath = tempFilePath + fileName + ".json";
      sourceFile = new File(filePath);
      if (sourceFile.exists()) {
        sourceFile.delete();
      }
      sourceFile.createNewFile();

      FileWriter writer = new FileWriter(filePath, true);
      writer.write(jsonString);
      writer.close();
      blobClient.uploadFromFile(filePath);
      sourceFile.delete();
    } catch (Exception e) {
      logger.error("/uploadAzureBlob========", e);
      result = false;
    }
    return result;
  }

  public List<String> uploadAzureBlobForSmallFileList(List<String> smallFileList, PgHsmStep2BiReport pgHsmStep2BiReport, String jsonString, String fileName, BlobContainerClient blobContainerClient){

    BlobClient blobClient = blobContainerClient.getBlobClient(fileName + ".json");
    File sourceFile = null;
    try {
      String tempFilePath = "./data/";
      String filePath = tempFilePath + fileName + ".json";
      sourceFile = new File(filePath);
      if (sourceFile.exists()) {
        sourceFile.delete();
      }
      sourceFile.createNewFile();

      FileWriter writer = new FileWriter(filePath, true);
      writer.write(jsonString);
      writer.close();
      if (Objects.nonNull(sourceFile)){
        // 获取文件大小
        long fileSizeInBytes = sourceFile.length();
        long fileSizeInKB = fileSizeInBytes / 1024;
        //大于最小文件阀值才上传
        if (fileSizeInKB < SMALL_FILE_THRESHOLD){
          String tipsStr = pgHsmStep2BiReport.getResponseId() + "|" + pgHsmStep2BiReport.getCategory();
          smallFileList.add(tipsStr);
        }else {
          blobClient.uploadFromFile(filePath);
        }
      }
      sourceFile.delete();
    } catch (Exception e) {
      logger.error("/uploadAzureBlob========", e);
    }
    return smallFileList;
  }

  public List<String> getBlobFileList(String folderName) {

    List<String> blobFileList = new ArrayList<>();
    BlobServiceClient blobServiceClient =
        new BlobServiceClientBuilder().endpoint(BLOB_SAS_TOKEN).buildClient();
    BlobContainerClient blobContainerClient =
        blobServiceClient.getBlobContainerClient(BLOB_CONTAINER + folderName);

    // List the blob(s) in the container.
    for (BlobItem blobItem : blobContainerClient.listBlobs()) {
      blobFileList.add(blobItem.getName());
    }
    return blobFileList;
  }

  /**
   * 上传统计文件
   * @param contents
   * @param fileName
   * @return
   */
  public boolean uploadStatisticsFile(List<String> contents, String fileName, BlobContainerClient blobContainerClientStatisticsFile){

    boolean result = true;
    BlobClient blobClient = blobContainerClientStatisticsFile.getBlobClient(fileName + ".txt");
    File sourceFile = null;
    try {
      String tempFilePath = "./data/";
      String filePath = tempFilePath + fileName + ".txt";
      sourceFile = new File(filePath);
      if (sourceFile.exists()) {
        sourceFile.delete();
      }
      sourceFile.createNewFile();

      BufferedWriter bw = new BufferedWriter(new FileWriter(sourceFile, false));
      for (String str : contents){
        bw.write(str);
        bw.newLine();
      }
      bw.close();
      blobClient.uploadFromFile(filePath);
      sourceFile.delete();
    } catch (Exception e) {
      logger.error("/uploadStatisticsFile========", e);
      result = false;
    }

    return result;
  }

  /**
   * 添加门店状态标题
   * @return
   */
  public List<String> addStoreStatusHead(){

    List<String> cdlStoreStatusHeadList = Lists.newArrayList();

    cdlStoreStatusHeadList.add("month");
    cdlStoreStatusHeadList.add("Store_Tag");
    cdlStoreStatusHeadList.add("addressIDnum_new");
    cdlStoreStatusHeadList.add("distributor_id");
    cdlStoreStatusHeadList.add("distributor_store_id");
    cdlStoreStatusHeadList.add("store_seq_code");

    cdlStoreStatusHeadList.add("Division");
    cdlStoreStatusHeadList.add("Market");
    cdlStoreStatusHeadList.add("RD");
    cdlStoreStatusHeadList.add("Province");
    cdlStoreStatusHeadList.add("City");
    cdlStoreStatusHeadList.add("Store_Type");

    cdlStoreStatusHeadList.add("BANNER");
    cdlStoreStatusHeadList.add("Store_Name");
    cdlStoreStatusHeadList.add("Address");
    cdlStoreStatusHeadList.add("top_banner");
    cdlStoreStatusHeadList.add("Is_dcp_Flag");
    cdlStoreStatusHeadList.add("addtion_info1");
    cdlStoreStatusHeadList.add("addtion_info2");

    cdlStoreStatusHeadList.add("GE_store_visit_date");
    cdlStoreStatusHeadList.add("GE_store_visit_status");
//    cdlStoreStatusHeadList.add("dcp_id");
//    cdlStoreStatusHeadList.add("dcp_store_id");
//    cdlStoreStatusHeadList.add("Is_dcp_Flag");
    cdlStoreStatusHeadList.add("StoreFront_photo");

    cdlStoreStatusHeadList.add("Filed_feedback_date");
    cdlStoreStatusHeadList.add("Filed_feedback_Status");
    cdlStoreStatusHeadList.add("Filed_feedback_Comments");


    return cdlStoreStatusHeadList;
  }

  /**
   * 添加psku标题
   * @return
   */
  public List<String> addPSkuHead(){

    List<String> pSkuHeadList = Lists.newArrayList();
    pSkuHeadList.add("Month");
    pSkuHeadList.add("Date");
    pSkuHeadList.add("addressID");
    pSkuHeadList.add("distributor_id");
    pSkuHeadList.add("distributor_store_id");
    pSkuHeadList.add("store_seq_code");
    pSkuHeadList.add("Is_dcp_Flag");
    pSkuHeadList.add("Category");
    pSkuHeadList.add("PSKU_actual");
    pSkuHeadList.add("SKU_name");
    pSkuHeadList.add("SKU_Barcode");
    pSkuHeadList.add("SKU_distribution");
    pSkuHeadList.add("store_type");
    pSkuHeadList.add("sub_banner_name");
    pSkuHeadList.add("category_code");

    return pSkuHeadList;
  }

  /**
   * 获取并推送门店状态标记文件
   * @param searchDate
   */
  public void getHsmStatusFile(String searchDate){

    DateTime nowTime = new DateTime();

    if (StringUtils.isBlank(searchDate)) {
      searchDate = nowTime.minusDays(1).toString(DateUtil.DTFormat.yyyy_MM_dd.getFormat());
    }
    BlobServiceClient blobServiceClient =
            new BlobServiceClientBuilder().endpoint(BLOB_SAS_TOKEN).buildClient();
    LambdaQueryWrapper<PgHsmStoreStatusBiReport> hsmStatusBiReportLambdaQueryWrapper = new LambdaQueryWrapper<>();

    hsmStatusBiReportLambdaQueryWrapper.eq(PgHsmStoreStatusBiReport::getDate, searchDate);
    hsmStatusBiReportLambdaQueryWrapper.orderByAsc(PgHsmStoreStatusBiReport::getId);

    String maxMonth = "";
    PgHsmStoreStatusBiReport pgHsmStatusBiReport = pgHsmStatusBiReportMapper.selectOne(new LambdaQueryWrapper<PgHsmStoreStatusBiReport>().orderByDesc(PgHsmStoreStatusBiReport::getMonth).last("LIMIT 1"));
    if (Objects.nonNull(pgHsmStatusBiReport)){
      maxMonth = pgHsmStatusBiReport.getMonth();
    }

    List<PgHsmStoreStatusBiReport> pgHsmStatusBiReportList = Lists.newArrayList();

    // 计算总的记录数
    int countByExample = pgHsmStatusBiReportMapper.selectCount(hsmStatusBiReportLambdaQueryWrapper);

    // 计算总的页数
    int pageCount = (int) Math.ceil((double) countByExample / oneQueryLimit);


// 循环查询所有数据
    for (int i = 0; i < pageCount; i++) {
      // 创建分页对象
      IPage<PgHsmStoreStatusBiReport> page = new Page<>(i + 1, oneQueryLimit); // 注意这里的 i+1 是因为 MyBatis Plus 的分页是从 1 开始的
      page = pgHsmStatusBiReportMapper.selectPage(page, hsmStatusBiReportLambdaQueryWrapper);
      if (CollectionUtils.isNotEmpty(page.getRecords())){
        // 执行分页查询
        List<PgHsmStoreStatusBiReport> pgHsmStatusBiReportList1 = page.getRecords();

        // 添加到总列表中
        pgHsmStatusBiReportList.addAll(pgHsmStatusBiReportList1);
      }
    }

    String path = "./data/";
    String fileName = "DPB_GE_Store_status_" + maxMonth + "_" + new DateTime().toString(DateUtil.DTFormat.yyyyMMdd.getFormat());
    File csvFile = CsvUtils.createCSVFile(addStoreStatusHead(), pocketHsmStatusCsvData(pgHsmStatusBiReportList), path, fileName);
    BlobContainerClient blobContainerClientHsmStatusFile =
            blobServiceClient.getBlobContainerClient(BLOB_CONTAINER_STORE_STATUS);
    if (Objects.nonNull(csvFile)){
      uploadHsmStatusFile(csvFile, fileName, blobContainerClientHsmStatusFile);
    }
  }

  public boolean uploadHsmStatusFile(
          File csvFile, String fileName, BlobContainerClient blobContainerClient) {
    boolean result = true;
    BlobClient blobClient = blobContainerClient.getBlobClient(fileName + ".csv");

    try {
      String tempFilePath = "./data/";
      String filePath = tempFilePath + fileName + ".csv";

      blobClient.uploadFromFile(filePath);
      csvFile.delete();
    } catch (Exception e) {
      logger.error("/uploadHsmStatusFile========", e);
      result = false;
    }
    return result;
  }

  public List<List<String>> pocketHsmStatusCsvData(List<PgHsmStoreStatusBiReport> pgHsmStatusBiReportList){

    List<List<String>> hsmStatusCsvDataLists = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(pgHsmStatusBiReportList)){
      pgHsmStatusBiReportList.forEach(pgHsmStatusBiReport -> {

        if (StringUtils.isNotBlank(getSafeString(pgHsmStatusBiReport.getGeStoreVisitDate())) && !"未关联".equals(getSafeString(pgHsmStatusBiReport.getGeStoreVisitStatus())) && !"已关联-无答案".equals(getSafeString(pgHsmStatusBiReport.getGeStoreVisitStatus()))){
          List<String> hsmStatusCsvDataList = Lists.newArrayList();

          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getMonth()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getStoreTag()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getAddressidnumNew()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getDistributorId()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getDistributorStoreId()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getStoreSeqCode()));

          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getDivision()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getMarket()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getRd()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getProvince()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getCity()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getStoreType()));

          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getBanner()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getStoreName()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getAddress()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getTopBanner()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getIsDcpFlag()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getAddtionInfo1()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getAddtionInfo2()));

          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getGeStoreVisitDate()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getGeStoreVisitStatus()));

          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getStorefrontPhoto()));

          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getFiledFeedbackDate()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getFiledFeedbackStatus()));
          hsmStatusCsvDataList.add(getSafeString(pgHsmStatusBiReport.getFiledFeedbackComments()));

          hsmStatusCsvDataLists.add(hsmStatusCsvDataList);
        }
      });
    }
    return hsmStatusCsvDataLists;
  }

  public String getSafeString(Object value) {
    return value == null ? "" : value.toString();
  }

  public void getAndUploadCloudViewStoreFile(String month){

    String directoryPrefix = "PG_DPB_HSM/PG_DPB_HSM_PSKU/raw_data/" + month;
    String tempFilePath = "./data/";
    String localFilePath = tempFilePath + new DateTime().toString(DateUtil.DTFormat.yyyyMMddHHmmss.getFormat()) + ".xlsx";
    List<String> fileKeys = OssUtil.listFilesInDirectory(directoryPrefix);
    if (CollectionUtils.isNotEmpty(fileKeys)){
      String objectKey = fileKeys.get(fileKeys.size() - 1);
      OssUtil.downloadFile(objectKey, localFilePath);
    }

//    String localFilePath = "./data/8月 pSKU detail 1025 Final.xlsx";

    // 创建一个listener对象
    HsmPskuHeaderAndDataHandler listener = new HsmPskuHeaderAndDataHandler();

    // 创建 ReadWorkbook 实例
    ExcelReader readWorkbook = EasyExcel.read(localFilePath).build();

    // 假设我们知道文件中有多个Sheet，我们可以手动创建ReadSheet对象
    // 这里我们假设文件中至少有一个Sheet
    int numberOfSheets = 7; // 或者通过其他方式确定Sheet的数量

    // 获取工作簿中的所有Sheet数量
    try (InputStream inputStream = new FileInputStream(localFilePath)) {
      Workbook workbook = WorkbookFactory.create(inputStream);
      numberOfSheets = workbook.getNumberOfSheets();
    } catch (IOException e) {
      throw new RuntimeException("Error reading the number of sheets", e);
    }

    // 遍历每个Sheet
    for (int i = 0; i < numberOfSheets; i++) {
      listener.setCurrentSheetIndex(i); // 设置当前sheet的索引

      // 创建 ReadSheet 实例并设置监听器
      ReadSheet readSheet = EasyExcel.readSheet(i)
              .headRowNumber(0) // 如果表头在第一行
              .registerReadListener(listener)
              .build();

      // 读取指定的Sheet
      readWorkbook.read(new ReadSheet[] { readSheet });
    }

    // 获取所有sheet的数据列表
    Map<Integer, List<Object>> allDataListMaps = listener.getAllDataLists();
    // 获取所有sheet的表头列表
    Map<Integer, List<String>> allHeaderListMaps = listener.getAllHeaders();

    Map<String, TPgReportHsmSkuV7> skuNameTPgReportHsmSkuV7Map = mapSkuNameTPgReportHsmSkuV7(month);
    Map<String, PgHsmStoreStatusBiReport> addressIDnumPgHsmStoreStatusBiReportMap = mapAddressIDnumPgHsmStoreStatusBiReport(month);
    Map<String, String> categoryCategoryCodeMap = initCategoryCategoryCodeMap();

    logger.info("skuNameTPgReportHsmSkuV7Map.size() = " + skuNameTPgReportHsmSkuV7Map.size());
    logger.info("addressIDnumPgHsmStoreStatusBiReportMap.size() = " + addressIDnumPgHsmStoreStatusBiReportMap.size());
    List<List<String>> allPocketExpandDataList = Lists.newArrayList();
    // 遍历每个sheet的数据
    for (Map.Entry<Integer, List<Object>> entry : allDataListMaps.entrySet()) {
      Integer sheetIndex = entry.getKey();
      List<Object> dataList = entry.getValue();
      logger.info("Sheet " + sheetIndex + ":");
      List<String> sheetHeaderList = allHeaderListMaps.get(sheetIndex);
      for (Object data : dataList) {
        // 将 data 转换为 Map<Integer, String>
        Map<Integer, String> rowData = (Map<Integer, String>) data;

        List<List<String>> pocketExpandDataLists = pocketExpandData(rowData, sheetHeaderList, skuNameTPgReportHsmSkuV7Map, addressIDnumPgHsmStoreStatusBiReportMap, categoryCategoryCodeMap);
        allPocketExpandDataList.addAll(pocketExpandDataLists);
      }
    }

    // 关闭读取器
    readWorkbook.finish();
    //释放资源
    //FileUtils.deleteFile(localFilePath);
    if (Objects.nonNull(allDataListMaps) && allDataListMaps.size() > 0){
      allDataListMaps.clear();
    }
    if (Objects.nonNull(allHeaderListMaps) && allHeaderListMaps.size() > 0){
      allHeaderListMaps.clear();
    }

    logger.info("allPocketExpandDataList.size() = " + allPocketExpandDataList.size());

    String path = "./data/";
    String fileName = "ge_dp_fmot_psku_details_" + new DateTime().toString(DateUtil.DTFormat.yyyyMMdd.getFormat());
    File csvFile = CsvUtils.createCSVFile(addPSkuHead(), allPocketExpandDataList, path, fileName);
//    BlobContainerClient blobContainerClientHsmStatusFile =
//            blobServiceClient.getBlobContainerClient(BLOB_CONTAINER_RESTATEMENT);
//    if (Objects.nonNull(csvFile)){
//      uploadHsmStatusFile(csvFile, fileName, blobContainerClientHsmStatusFile);
//    }
  }

  /**
   * 封装psku展开数据
   * @param rowData 一行数据
   * @param sheetHeaderList
   * @return
   */
  public List<List<String>> pocketExpandData(Map<Integer, String> rowData, List<String> sheetHeaderList, Map<String, TPgReportHsmSkuV7> skuNameTPgReportHsmSkuV7Map, Map<String, PgHsmStoreStatusBiReport> addressIDnumPgHsmStoreStatusBiReportMap, Map<String, String> categoryCategoryCodeMap){
    List<List<String>> pocketExpandDataLists = Lists.newArrayList();
    List<PgGeDpFmotPskuDetail> pgGeDpFmotPskuDetailList = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(sheetHeaderList)){
      for (int i = 4; i < sheetHeaderList.size(); i ++) {
        String skuDistribution = getSafeString(rowData.get(i));
        if (StringUtils.isBlank(skuDistribution)){
          continue;
        }

        String addressIDnum = rowData.get(2);
        String header = sheetHeaderList.get(i);
        String barcode = "";
        if (skuNameTPgReportHsmSkuV7Map.size() > 0){
          TPgReportHsmSkuV7 tPgReportHsmSkuV7 = skuNameTPgReportHsmSkuV7Map.get(header);
          if (Objects.nonNull(tPgReportHsmSkuV7)){
            barcode = tPgReportHsmSkuV7.getBarcode();
          }
        }
        PgHsmStoreStatusBiReport pgHsmStoreStatusBiReport = null;
        if (addressIDnumPgHsmStoreStatusBiReportMap.size() > 0){
          pgHsmStoreStatusBiReport = addressIDnumPgHsmStoreStatusBiReportMap.get(addressIDnum);
        }
        List<String>  pocketExpandDataList = Lists.newArrayList();
        String month = Objects.nonNull(pgHsmStoreStatusBiReport) && StringUtils.isNotBlank(pgHsmStoreStatusBiReport.getMonth()) ? pgHsmStoreStatusBiReport.getMonth() : "";
        pocketExpandDataList.add(month);//month
        String date = Objects.nonNull(pgHsmStoreStatusBiReport) && Objects.nonNull(pgHsmStoreStatusBiReport.getGeStoreVisitDate()) ? pgHsmStoreStatusBiReport.getGeStoreVisitDate().toString() : "";
        pocketExpandDataList.add(date);//date
        pocketExpandDataList.add(getSafeString(addressIDnum));//addressID
        String distributorId = Objects.nonNull(pgHsmStoreStatusBiReport) && StringUtils.isNotBlank(pgHsmStoreStatusBiReport.getDistributorId()) ? pgHsmStoreStatusBiReport.getDistributorId() : "";
        pocketExpandDataList.add(distributorId);//distributor_id
        String distributorStoreId = Objects.nonNull(pgHsmStoreStatusBiReport) && StringUtils.isNotBlank(pgHsmStoreStatusBiReport.getDistributorStoreId()) ? pgHsmStoreStatusBiReport.getDistributorStoreId() : "";
        pocketExpandDataList.add(distributorStoreId);//distributor_store_id
        String storeSeqCode = Objects.nonNull(pgHsmStoreStatusBiReport) && StringUtils.isNotBlank(pgHsmStoreStatusBiReport.getStoreSeqCode()) ? pgHsmStoreStatusBiReport.getStoreSeqCode() : "";
        pocketExpandDataList.add(storeSeqCode);//store_seq_code
        String isDcpFlag = Objects.nonNull(pgHsmStoreStatusBiReport) && StringUtils.isNotBlank(pgHsmStoreStatusBiReport.getIsDcpFlag()) ? pgHsmStoreStatusBiReport.getIsDcpFlag() : "";
        pocketExpandDataList.add(isDcpFlag);//Is_dcp_Flag
        String category = getSafeString(rowData.get(1));
        pocketExpandDataList.add(category);//Category
        String pskuActual = getSafeString(rowData.get(3));
        pocketExpandDataList.add(pskuActual);//PSKU_actual
        pocketExpandDataList.add(header);//SKU_name
        pocketExpandDataList.add(barcode);//SKU_Barcode
        pocketExpandDataList.add(skuDistribution);//SKU_distribution

        String storeType = Objects.nonNull(pgHsmStoreStatusBiReport) && StringUtils.isNotBlank(pgHsmStoreStatusBiReport.getStoreType()) ? pgHsmStoreStatusBiReport.getStoreType() : "";
        pocketExpandDataList.add(storeType);//store_type
        String subBannerName = Objects.nonNull(pgHsmStoreStatusBiReport) && StringUtils.isNotBlank(pgHsmStoreStatusBiReport.getBanner()) ? pgHsmStoreStatusBiReport.getBanner() : "";
        pocketExpandDataList.add(subBannerName);//sub_banner_name
        String categoryCode = categoryCategoryCodeMap.get(category.toUpperCase());
        categoryCode = Objects.nonNull(categoryCode) ? categoryCode : "";
        pocketExpandDataList.add(categoryCode);//category_code

        pocketExpandDataLists.add(pocketExpandDataList);

        PgGeDpFmotPskuDetail pgGeDpFmotPskuDetail = new PgGeDpFmotPskuDetail();
        pgGeDpFmotPskuDetail.setMonth(month);
        pgGeDpFmotPskuDetail.setDate(date);
        pgGeDpFmotPskuDetail.setAddressId(addressIDnum);
        pgGeDpFmotPskuDetail.setDistributorId(distributorId);
        pgGeDpFmotPskuDetail.setDistributorStoreId(distributorStoreId);
        pgGeDpFmotPskuDetail.setStoreSeqCode(storeSeqCode);
        pgGeDpFmotPskuDetail.setIsDcpFlag(isDcpFlag);
        pgGeDpFmotPskuDetail.setCategory(category);
        pgGeDpFmotPskuDetail.setPskuActual(pskuActual);
        pgGeDpFmotPskuDetail.setSkuName(header);
        pgGeDpFmotPskuDetail.setSkuBarcode(barcode);
        pgGeDpFmotPskuDetail.setSkuDistribution(skuDistribution);
        pgGeDpFmotPskuDetail.setStoreType(storeType);
        pgGeDpFmotPskuDetail.setSubBannerName(subBannerName);
        pgGeDpFmotPskuDetail.setCategoryCode(categoryCode);
        pgGeDpFmotPskuDetailList.add(pgGeDpFmotPskuDetail);
      }
    }

    if (CollectionUtils.isNotEmpty(pgGeDpFmotPskuDetailList)){
      insertBatchPskuDetail(pgGeDpFmotPskuDetailList);
    }

    return pocketExpandDataLists;
  }

  /**
   * 获取PgHsmStoreStatusBiReport
   * @param month
   * @return
   */
  public Map<String, PgHsmStoreStatusBiReport> mapAddressIDnumPgHsmStoreStatusBiReport(String month){

    Map<String, PgHsmStoreStatusBiReport> addressIDnumPgHsmStoreStatusBiReportMap = Maps.newHashMap();
    List<PgHsmStoreStatusBiReport> pgHsmStoreStatusBiReportList = pgHsmStatusBiReportMapper.selectList(new LambdaQueryWrapper<PgHsmStoreStatusBiReport>().select(PgHsmStoreStatusBiReport::getMonth, PgHsmStoreStatusBiReport::getGeStoreVisitDate, PgHsmStoreStatusBiReport::getDistributorId
    , PgHsmStoreStatusBiReport::getDistributorStoreId, PgHsmStoreStatusBiReport::getStoreSeqCode, PgHsmStoreStatusBiReport::getIsDcpFlag, PgHsmStoreStatusBiReport::getAddressidnumNew, PgHsmStoreStatusBiReport::getStoreType, PgHsmStoreStatusBiReport::getBanner).eq(PgHsmStoreStatusBiReport::getMonth, month));
    if (CollectionUtils.isNotEmpty(pgHsmStoreStatusBiReportList)){
      addressIDnumPgHsmStoreStatusBiReportMap = pgHsmStoreStatusBiReportList.stream().collect(Collectors.toMap(
              PgHsmStoreStatusBiReport::getAddressidnumNew,
              obj -> obj,
              (oldValue, newValue) -> newValue
      ));
    }
    return  addressIDnumPgHsmStoreStatusBiReportMap;
  }

  /**
   * 获取TPgReportHsmSkuV7
   * @param month
   * @return
   */
  public Map<String, TPgReportHsmSkuV7> mapSkuNameTPgReportHsmSkuV7(String month){

    Map<String, TPgReportHsmSkuV7> skuNameTPgReportHsmSkuV7Map = Maps.newHashMap();
    List<TPgReportHsmSkuV7> tPgReportHsmSkuV7List = tPgReportHsmSkuV7Mapper.selectList(new LambdaQueryWrapper<TPgReportHsmSkuV7>().select(TPgReportHsmSkuV7::getProductName, TPgReportHsmSkuV7::getBarcode).eq(TPgReportHsmSkuV7::getMonth, month));
    if (CollectionUtils.isNotEmpty(tPgReportHsmSkuV7List)){
      skuNameTPgReportHsmSkuV7Map = tPgReportHsmSkuV7List.stream().collect(Collectors.toMap(
              TPgReportHsmSkuV7::getProductName,
              obj -> obj,
              (oldValue, newValue) -> newValue
      ));
    }
    return skuNameTPgReportHsmSkuV7Map;
  }

  /**
   * 批量插入推送明细记录
   * @param list
   */
  public void insertBatch(List<PgHsmCdlPushDetailLog> list) {
    if (list == null || list.isEmpty()) {
      return;
    }
    int batchSize = 500;
    for (int i = 0; i < list.size(); i += batchSize) {
      int end = Math.min(i + batchSize, list.size());
      List<PgHsmCdlPushDetailLog> subList = list.subList(i, end);
      pgDpbReportMapper.batchInsert(subList);
    }
  }

  /**
   * 批量插入psku明细
   * @param list
   */
  public void insertBatchPskuDetail(List<PgGeDpFmotPskuDetail> list) {
    if (list == null || list.isEmpty()) {
      return;
    }
    int batchSize = 500;
    for (int i = 0; i < list.size(); i += batchSize) {
      int end = Math.min(i + batchSize, list.size());
      List<PgGeDpFmotPskuDetail> subList = list.subList(i, end);
      pgDpbReportMapper.insertBatchPskuDetail(subList);
    }
  }

  public void sendFeiShuNotice(String content){
    FeiShuRequest feiShuRequest = new FeiShuRequest();
    feiShuRequest.setMsg_type("text");
    FeiShuRequest.ContentBean contentBean = new FeiShuRequest.ContentBean();
    contentBean.setText(content);
    feiShuRequest.setContent(contentBean);
    String resultJson = JsonUtil.toJsonString(feiShuRequest);
    try {
      String callbackResult = HttpConnectionUtils.postRequest(FEISHU_NOTICE_URL, resultJson, 30000);
      System.out.println(callbackResult);
    } catch (Exception e) {

    }
  }

  /**
   * 初始化品类及品类编码对应关系
   * @return
   */
  public Map<String, String> initCategoryCategoryCodeMap(){

    Map<String, String> categoryCategoryCodeMap = Maps.newHashMap();
    Map<String, String> originalMap = Maps.newHashMap();
    originalMap.put("Baby", "206000002");
    originalMap.put("fabric", "206000003");
    originalMap.put("laundry", "206000003");
    originalMap.put("Fem", "206000005");
    originalMap.put("Hair", "206000006");
    originalMap.put("Oral", "206000007");
    originalMap.put("PCC", "206000008");
    originalMap.put("br", "206000010");
    originalMap.put("shave", "206000010");
    originalMap.put("b&r", "206000010");
    originalMap.forEach((key, value) -> categoryCategoryCodeMap.put(key.toUpperCase(), value));

    return categoryCategoryCodeMap;

  }

}


