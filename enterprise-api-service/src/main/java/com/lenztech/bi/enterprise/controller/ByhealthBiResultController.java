package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.byhealth.ByhealthBiTargetResp;
import com.lenztech.bi.enterprise.service.ByhealthReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 汤臣倍健bi指标Controller
 *
 * <AUTHOR>
 * @date 2021-08-19 13:44:19
 */
@Slf4j
@RestController
@RequestMapping("/biResult/byhealth/")
public class ByhealthBiResultController {

    @Autowired
    private ByhealthReportService byhealthReportService;

    /**
     * 获取指标列表
     *
     * @param responseId
     * @return ResponseData<ByhealthBiTargetResp>
     */
    @RequestMapping(value = "getTargetList", method = RequestMethod.GET)
    public ResponseData<ByhealthBiTargetResp> get(String responseId) {
        try {
            ByhealthBiTargetResp byhealthBiTargetResp = byhealthReportService.getBiTargetList(responseId);
            return ResponseData.success().data(byhealthBiTargetResp);
        } catch (Exception e) {
            log.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

}
