package com.lenztech.bi.enterprise.service.demo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.comon.HandleWayEnum;
import com.lenztech.bi.enterprise.comon.QuestionEnum;
import com.lenztech.bi.enterprise.dto.demo.CategoryDetailResp;
import com.lenztech.bi.enterprise.dto.demo.CategoryListResp;
import com.lenztech.bi.enterprise.dto.demo.CompareDetailResp;
import com.lenztech.bi.enterprise.dto.demo.FaceTrendResp;
import com.lenztech.bi.enterprise.dto.demo.GetRecordDetailResp;
import com.lenztech.bi.enterprise.dto.demo.GetShelfSituationReq;
import com.lenztech.bi.enterprise.dto.demo.HandleQuestionReq;
import com.lenztech.bi.enterprise.dto.demo.HandleRecordListResp;
import com.lenztech.bi.enterprise.dto.demo.ReasonListResp;
import com.lenztech.bi.enterprise.dto.demo.ShelfSituationResp;
import com.lenztech.bi.enterprise.dto.demo.WaitHandleListResp;
import com.lenztech.bi.enterprise.entity.DemoMay2021Kpi;
import com.lenztech.bi.enterprise.entity.DemoMay2021SkuDetail;
import com.lenztech.bi.enterprise.entity.ImageProductTree;
import com.lenztech.bi.enterprise.entity.TCategory;
import com.lenztech.bi.enterprise.entity.THandleQuestion;
import com.lenztech.bi.enterprise.entity.TReason;
import com.lenztech.bi.enterprise.mapper.DemoMay2021KpiMapper;
import com.lenztech.bi.enterprise.mapper.DemoMay2021SkuDetailMapper;
import com.lenztech.bi.enterprise.mapper.ImageProductTreeMapper;
import com.lenztech.bi.enterprise.mapper.TCategoryMapper;
import com.lenztech.bi.enterprise.mapper.THandleQuestionMapper;
import com.lenztech.bi.enterprise.mapper.TReasonMapper;
import com.lenztech.bi.enterprise.utils.DateTimeString;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
public class WechatApiService {

    @Autowired
    private DemoMay2021KpiMapper demoMay2021KpiMapper;

    @Autowired
    private DemoMay2021SkuDetailMapper demoMay2021SkuDetailMapper;

    @Autowired
    private TCategoryMapper tCategoryMapper;

    @Autowired
    private ImageProductTreeMapper imageProductTreeMapper;

    @Autowired
    private TReasonMapper tReasonMapper;

    @Autowired
    private THandleQuestionMapper tHandleQuestionMapper;

    /**
     * 获取品类列表
     *
     * @return
     */
    public CategoryListResp getCategoryList() {
        CategoryListResp resp = new CategoryListResp();
        List<CategoryListResp.CategoryDTO> dtoList = Lists.newArrayList();
        LambdaQueryWrapper<TCategory> categoryWrapper = new LambdaQueryWrapper<>();
        List<TCategory> categoryList = tCategoryMapper.selectList(categoryWrapper);

        categoryList.stream().forEach(category -> {
            CategoryListResp.CategoryDTO dto = new CategoryListResp.CategoryDTO();
            dto.setCategoryId(category.getQuestionId());
            dto.setCategoryName(category.getCategoryName());
            dtoList.add(dto);
        });

        resp.setCategoryList(dtoList);
        return resp;
    }

    /**
     * 获取货架情况列表
     *
     * @return
     */
    public ShelfSituationResp getShelfSituationList(GetShelfSituationReq getShelfSituationReq) {
        ShelfSituationResp resp = new ShelfSituationResp();
        LambdaQueryWrapper<TCategory> categoryWrapper = new LambdaQueryWrapper<>();
        List<TCategory> categoryList = tCategoryMapper.selectList(categoryWrapper);

        Map<String, String> categoryMap = categoryList.stream().collect(Collectors.toMap(TCategory::getQuestionId, TCategory::getCategoryName));

        List<ShelfSituationResp.CategorySituation> categorySituationList = Lists.newArrayList();
        if (!Objects.isNull(getShelfSituationReq) && !CollectionUtils.isEmpty(getShelfSituationReq.getCategoryIdList())) {
            for (String questionId : getShelfSituationReq.getCategoryIdList()) {
                ShelfSituationResp.CategorySituation categorySituation = getCategorySituation(questionId, categoryMap);
                categorySituationList.add(categorySituation);
            }
        } else {
            for (String questionId : categoryMap.keySet()) {
                ShelfSituationResp.CategorySituation categorySituation = getCategorySituation(questionId, categoryMap);
                categorySituationList.add(categorySituation);
            }
        }

        resp.setCategorySituation(categorySituationList);
        return resp;
    }

    private ShelfSituationResp.CategorySituation getCategorySituation(String questionId, Map<String, String> categoryMap) {
        LambdaQueryWrapper<DemoMay2021Kpi> kpiLambdaQueryWrapper = new LambdaQueryWrapper<>();
        kpiLambdaQueryWrapper.eq(DemoMay2021Kpi::getQuestionId, questionId);
        kpiLambdaQueryWrapper.orderByDesc(DemoMay2021Kpi::getCreateTime);
        kpiLambdaQueryWrapper.last("LIMIT 0,2");
        List<DemoMay2021Kpi> kpiList = demoMay2021KpiMapper.selectList(kpiLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(kpiList)) {
            return null;
        }
        DemoMay2021Kpi firstKpi = kpiList.get(0);
        DemoMay2021Kpi secondKpi = null;
        if (kpiList.size() > 1) {
            secondKpi = kpiList.get(1);
        }
        LambdaQueryWrapper<DemoMay2021SkuDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(DemoMay2021SkuDetail::getResponseId, firstKpi.getResponseId());
        List<DemoMay2021SkuDetail> firstDetailList = demoMay2021SkuDetailMapper.selectList(detailWrapper);
        Integer firstFaceCount = firstDetailList.stream().mapToInt(DemoMay2021SkuDetail::getFacingCount).sum();

        Integer secondFaceCount = 0;
        if (!Objects.isNull(secondKpi)) {
            LambdaQueryWrapper<DemoMay2021SkuDetail> secondDetailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.eq(DemoMay2021SkuDetail::getResponseId, secondKpi.getResponseId());
            List<DemoMay2021SkuDetail> secondDetailList = demoMay2021SkuDetailMapper.selectList(secondDetailWrapper);
            secondFaceCount = secondDetailList.stream().mapToInt(DemoMay2021SkuDetail::getFacingCount).sum();
        }

        ShelfSituationResp.CategorySituation categorySituation = new ShelfSituationResp.CategorySituation();

        categorySituation.setReportTime(DateTimeString.DATE_TIME_WITHOUT_SECONDS.toString(firstKpi.getCreateTime()));
        categorySituation.setSubmitId(firstKpi.getResponseId());
        categorySituation.setCategoryName(categoryMap.get(firstKpi.getQuestionId()));

        ShelfSituationResp.Situation shelfOccupySituation = getSituation(new BigDecimal(1).subtract(firstKpi.getShortageRatio()).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_DOWN), Objects.isNull(secondKpi) ? null : new BigDecimal(1).subtract(secondKpi.getShortageRatio()).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_DOWN));
        categorySituation.setShelfOccupyRate(shelfOccupySituation);

        ShelfSituationResp.Situation totalFaceSituation = getSituation(new BigDecimal(firstFaceCount), Objects.isNull(secondKpi) ? null : new BigDecimal(secondFaceCount));
        categorySituation.setTotalFaceNum(totalFaceSituation);

        ShelfSituationResp.Situation skuStockOutSituation = getSituation(new BigDecimal(firstKpi.getShortageSkuCount()), Objects.isNull(secondKpi) ? null : new BigDecimal(secondKpi.getShortageSkuCount()));
        categorySituation.setSkuStockOut(skuStockOutSituation);

        ShelfSituationResp.Situation coreProductStockOutSituation = getSituation(firstKpi.getPskuShortageRatio().multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_DOWN), Objects.isNull(secondKpi)? null : secondKpi.getPskuShortageRatio().multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_DOWN));
        categorySituation.setCoreProductStockOutRate(coreProductStockOutSituation);

        ShelfSituationResp.Situation coreProductPriceErrorSituation = getSituation(new BigDecimal(firstKpi.getWrongPriceSkuCount()), Objects.isNull(secondKpi)? null : new BigDecimal(secondKpi.getWrongPriceSkuCount()));
        categorySituation.setCoreProductPriceError(coreProductPriceErrorSituation);

        ShelfSituationResp.Situation coreProductLowFaceSituation = getSituation(new BigDecimal(firstKpi.getLowFacingPskuCount()), Objects.isNull(secondKpi)? null : new BigDecimal(secondKpi.getLowFacingPskuCount()));
        categorySituation.setCoreProductLowFace(coreProductLowFaceSituation);
        return categorySituation;
    }

    private ShelfSituationResp.Situation getSituation(BigDecimal now, BigDecimal last) {
        ShelfSituationResp.Situation situation = new ShelfSituationResp.Situation();
        situation.setValue(now.toString());
        if (Objects.isNull(last) || now.compareTo(last) == 0) {
            situation.setTrend("fair");
        } else if (now.compareTo(last) > 0) {
            situation.setTrend("up");
        } else if (now.compareTo(last) < 0) {
            situation.setTrend("down");
        }
        return situation;
    }

    /**
     * 获取货架详情
     *
     * @param submitId
     * @return
     */
    public CategoryDetailResp getCategoryDetailList(String submitId) {
        LambdaQueryWrapper<DemoMay2021Kpi> kpiLambdaQueryWrapper = new LambdaQueryWrapper<>();
        kpiLambdaQueryWrapper.eq(DemoMay2021Kpi::getResponseId, submitId);
        List<DemoMay2021Kpi> kpiList = demoMay2021KpiMapper.selectList(kpiLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(kpiList)) {
            return null;
        }
        DemoMay2021Kpi kpi = kpiList.get(0);

        LambdaQueryWrapper<THandleQuestion> handleQuestionWrapper = new LambdaQueryWrapper<>();
        handleQuestionWrapper.eq(THandleQuestion::getResponseId, kpi.getResponseId());
        List<THandleQuestion> handleQuestionList = tHandleQuestionMapper.selectList(handleQuestionWrapper);
        List<Integer> handleProductIdList = handleQuestionList.stream().map(THandleQuestion::getProductId).distinct().collect(Collectors.toList());

        LambdaQueryWrapper<DemoMay2021SkuDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(DemoMay2021SkuDetail::getResponseId, kpi.getResponseId());
        List<DemoMay2021SkuDetail> detailList = demoMay2021SkuDetailMapper.selectList(detailWrapper);
        List<DemoMay2021SkuDetail> handleDetailList = detailList.stream().filter(detail -> !handleProductIdList.contains(detail.getProductId())).collect(Collectors.toList());
        List<Integer> productIdList = handleDetailList.stream().map(DemoMay2021SkuDetail::getProductId).collect(Collectors.toList());

        List<ImageProductTree> productList = imageProductTreeMapper.getImageProductTreeByProductIdList(productIdList);
        Map<String, String> productMap = productList.stream().collect(Collectors.toMap(ImageProductTree::getId, ImageProductTree::getImageUrl));

        CategoryDetailResp resp = new CategoryDetailResp();
        resp.setSubmitId(submitId);
        resp.setShelfOccupyRate(new BigDecimal(1).subtract(kpi.getShortageRatio()).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_DOWN).toString());
        resp.setReportTime(DateTimeString.DATE_TIME_WITHOUT_SECONDS.toString(kpi.getCreateTime()));

        List<DemoMay2021SkuDetail> stockOutList = handleDetailList.stream().filter(detail -> detail.getFacingCount() == 0).collect(Collectors.toList());
        resp.setStockOutList(getMergeList(stockOutList, productMap));
        List<DemoMay2021SkuDetail> lowFaceList = handleDetailList.stream().filter(detail -> detail.getFacingCount() > 0 && detail.getFacingCount() < 3).collect(Collectors.toList());
        resp.setLowFaceList(getMergeList(lowFaceList, productMap));
        List<DemoMay2021SkuDetail> priceErrorList = handleDetailList.stream().filter(detail -> detail.getIfWrongPrice() == 1).collect(Collectors.toList());
        resp.setPriceErrorList(getMergeList(priceErrorList, productMap));

        Integer waiteHandleTaskNum = stockOutList.size() + lowFaceList.size() + priceErrorList.size();
        resp.setWaiteHandleTaskNum(String.valueOf(waiteHandleTaskNum));
        return resp;
    }

    private List<CategoryDetailResp.ProductDetail> getMergeList(List<DemoMay2021SkuDetail> detailList, Map<String, String> productMap) {
        List<CategoryDetailResp.ProductDetail> mergeList = Lists.newArrayList();
        for (DemoMay2021SkuDetail detail : detailList) {
            CategoryDetailResp.ProductDetail productDetail = new CategoryDetailResp.ProductDetail();
            productDetail.setImageUrl("http://prod-app.ppznet.com" + productMap.get(String.valueOf(detail.getProductId())));
            productDetail.setProductId(String.valueOf(detail.getProductId()));
            productDetail.setProductName(detail.getProductName());
            List<String> questionList = Lists.newArrayList();
            if (detail.getFacingCount() == 0) {
                questionList.add(QuestionEnum.STOCK_OUT.getQuestion());
            }
            if (detail.getFacingCount() < 3) {
                questionList.add(QuestionEnum.LOW_FACE.getQuestion());
            }
            if (detail.getIfWrongPrice() == 1) {
                questionList.add(QuestionEnum.ERROR_PRICE.getQuestion());
            }
            productDetail.setQuestionList(questionList);
            productDetail.setFloorNum(detail.getLevel());
            productDetail.setCoreProductStatus(detail.getIfPsku() == 1);
            mergeList.add(productDetail);
        }
        return mergeList;
    }

    /**
     * 已完成或上报
     *
     * @param handleQuestionReq
     * @return
     */
    public void handleQuestion(HandleQuestionReq handleQuestionReq) {
        LambdaQueryWrapper<THandleQuestion> handleQuestionWrapper = new LambdaQueryWrapper<>();
        handleQuestionWrapper.eq(THandleQuestion::getResponseId, handleQuestionReq.getSubmitId());
        handleQuestionWrapper.eq(THandleQuestion::getProductId, handleQuestionReq.getProductId());
        List<THandleQuestion> handleQuestionList = tHandleQuestionMapper.selectList(handleQuestionWrapper);
        if (!CollectionUtils.isEmpty(handleQuestionList)) {
            return;
        }

        THandleQuestion handleQuestion = new THandleQuestion();
        handleQuestion.setResponseId(handleQuestionReq.getSubmitId());
        handleQuestion.setProductId(Integer.valueOf(handleQuestionReq.getProductId()));
        handleQuestion.setReasonCode(handleQuestionReq.getHandleCode());
        tHandleQuestionMapper.insert(handleQuestion);
    }

    /**
     * 获取上报原因列表
     *
     * @return
     */
    public ReasonListResp getReasonList() {
        ReasonListResp resp = new ReasonListResp();
        List<ReasonListResp.HandleReason> handleReasonList = Lists.newArrayList();
        LambdaQueryWrapper<TReason> reasonWrapper = new LambdaQueryWrapper<>();
        reasonWrapper.ne(TReason::getReasonCode, "success");
        List<TReason> reasonList = tReasonMapper.selectList(reasonWrapper);
        for (TReason reason : reasonList) {
            ReasonListResp.HandleReason handleReason = new ReasonListResp.HandleReason();
            handleReason.setReasonCode(reason.getReasonCode());
            handleReason.setReasonContent(reason.getReasonContent());
            handleReasonList.add(handleReason);
        }
        resp.setReasonList(handleReasonList);
        return resp;
    }

    /**
     * 待处理任务品类列表
     *
     * @return
     */
    public WaitHandleListResp getWaitHandleList() {
        LambdaQueryWrapper<TCategory> categoryWrapper = new LambdaQueryWrapper<>();
        List<TCategory> categoryList = tCategoryMapper.selectList(categoryWrapper);
        Map<String, String> categoryMap = categoryList.stream().collect(Collectors.toMap(TCategory::getQuestionId, TCategory::getCategoryName));

        List<WaitHandleListResp.WaitHandle> waitHandleList = Lists.newArrayList();
        for (String categoryId : categoryMap.keySet()) {
            WaitHandleListResp.WaitHandle waitHandle = getWaitHandle(categoryId, categoryMap);
            waitHandleList.add(waitHandle);
        }
        WaitHandleListResp resp = new WaitHandleListResp();
        resp.setWaitHandleList(waitHandleList);
        return resp;
    }

    private WaitHandleListResp.WaitHandle getWaitHandle(String categoryId, Map<String, String> categoryMap) {
        LambdaQueryWrapper<DemoMay2021Kpi> kpiLambdaQueryWrapper = new LambdaQueryWrapper<>();
        kpiLambdaQueryWrapper.eq(DemoMay2021Kpi::getQuestionId, categoryId);
        kpiLambdaQueryWrapper.orderByDesc(DemoMay2021Kpi::getCreateTime);
        kpiLambdaQueryWrapper.last("LIMIT 0,1");
        List<DemoMay2021Kpi> kpiList = demoMay2021KpiMapper.selectList(kpiLambdaQueryWrapper);

        if (CollectionUtils.isEmpty(kpiList)) {
            return null;
        }

        DemoMay2021Kpi kpi = kpiList.get(0);

        LambdaQueryWrapper<THandleQuestion> handleQuestionWrapper = new LambdaQueryWrapper<>();
        handleQuestionWrapper.eq(THandleQuestion::getResponseId, kpi.getResponseId());
        List<THandleQuestion> handleQuestionList = tHandleQuestionMapper.selectList(handleQuestionWrapper);
        List<Integer> handleProductIdList = handleQuestionList.stream().map(THandleQuestion::getProductId).distinct().collect(Collectors.toList());

        LambdaQueryWrapper<DemoMay2021SkuDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(DemoMay2021SkuDetail::getResponseId, kpi.getResponseId());
        List<DemoMay2021SkuDetail> detailList = demoMay2021SkuDetailMapper.selectList(detailWrapper);

        List<DemoMay2021SkuDetail> waitHandleQuestionList = detailList.stream()
                .filter(detail -> detail.getFacingCount() == 0 || detail.getFacingCount() > 0 && detail.getFacingCount() < 3 || detail.getIfWrongPrice() == 1)
                .filter(detail -> !handleProductIdList.contains(detail.getProductId()))
                .collect(Collectors.toList());
        List<Integer> productIdList = waitHandleQuestionList.stream().map(DemoMay2021SkuDetail::getProductId).distinct().collect(Collectors.toList());
        List<Integer> coreProductIdList = waitHandleQuestionList.stream().filter(detail -> detail.getIfPsku() == 1).map(DemoMay2021SkuDetail::getProductId).distinct().collect(Collectors.toList());
        WaitHandleListResp.WaitHandle waitHandle = new WaitHandleListResp.WaitHandle();
        waitHandle.setSubmitId(kpi.getResponseId());
        waitHandle.setCategoryId(categoryId);
        waitHandle.setCategoryName(categoryMap.get(categoryId));
        waitHandle.setReportTime(DateTimeString.DATE_TIME_WITHOUT_SECONDS.toString(kpi.getCreateTime()));
        waitHandle.setNeedHandleTaskNum(String.valueOf(productIdList.size()));
        waitHandle.setCoreProductNum(String.valueOf(coreProductIdList.size()));
        return waitHandle;
    }

    /**
     * 品类内待处理任务列表
     *
     * @return
     */
    public HandleRecordListResp getHandleRecordList() {

        LambdaQueryWrapper<THandleQuestion> handleQuestionWrapper = new LambdaQueryWrapper<>();
        List<THandleQuestion> handleQuestionList = tHandleQuestionMapper.selectList(handleQuestionWrapper);

        if (CollectionUtils.isEmpty(handleQuestionList)) {
            return null;
        }

        List<ReasonListResp.HandleReason> handleReasonList = Lists.newArrayList();
        LambdaQueryWrapper<TReason> reasonWrapper = new LambdaQueryWrapper<>();
        reasonWrapper.ne(TReason::getReasonCode, "success");
        List<TReason> reasonList = tReasonMapper.selectList(reasonWrapper);
        Map<String, String> reasonMap = reasonList.stream().collect(Collectors.toMap(TReason::getReasonCode, TReason::getReasonContent));

        List<Integer> productIdList = handleQuestionList.stream().map(THandleQuestion::getProductId).distinct().collect(Collectors.toList());
        List<ImageProductTree> productList = imageProductTreeMapper.getImageProductTreeByProductIdList(productIdList);
        Map<String, String> productMap = productList.stream().collect(Collectors.toMap(ImageProductTree::getId, ImageProductTree::getImageUrl));

        Map<String, List<THandleQuestion>> handleQuestionMap = handleQuestionList.stream().collect(Collectors.groupingBy(THandleQuestion::getResponseId));

        LambdaQueryWrapper<DemoMay2021SkuDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.in(DemoMay2021SkuDetail::getResponseId, handleQuestionMap.keySet());
        List<DemoMay2021SkuDetail> detailList = demoMay2021SkuDetailMapper.selectList(detailWrapper);
        Map<String, List<DemoMay2021SkuDetail>> detailMap = detailList.stream().collect(Collectors.groupingBy(DemoMay2021SkuDetail::getResponseId));

        List<HandleRecordListResp.HandleRecord> stockOutRecordList = Lists.newArrayList();
        List<HandleRecordListResp.HandleRecord> lowFaceRecordList = Lists.newArrayList();
        List<HandleRecordListResp.HandleRecord> priceErrorRecordList = Lists.newArrayList();

        for (String responseId : handleQuestionMap.keySet()) {
            Map<Integer, List<THandleQuestion>> productHandleQuestionMap = handleQuestionMap.get(responseId).stream().collect(Collectors.groupingBy(THandleQuestion::getProductId));
            Map<Integer, DemoMay2021SkuDetail> productDetailMap = detailMap.get(responseId).stream().collect(Collectors.toMap(DemoMay2021SkuDetail::getProductId, detail -> detail));

            for (Integer productId : productHandleQuestionMap.keySet()) {
                DemoMay2021SkuDetail detail = productDetailMap.get(productId);
                List<THandleQuestion> handleQuestions = productHandleQuestionMap.get(productId);
                HandleRecordListResp.HandleRecord handleRecord = new HandleRecordListResp.HandleRecord();
                handleRecord.setSubmitId(responseId);
                handleRecord.setImageUrl("http://prod-app.ppznet.com" + productMap.get(String.valueOf(productId)));
                handleRecord.setProductId(String.valueOf(productId));
                handleRecord.setProductName(detail.getProductName());
                List<String> questionList = Lists.newArrayList();
                handleRecord.setQuestionList(questionList);
                handleRecord.setFloorNum(detail.getLevel());
                handleRecord.setReportTime(DateTimeString.DATE_TIME.toString(handleQuestions.get(0).getCreateTime()));
                HandleRecordListResp.HandleSituation handleSituation = new HandleRecordListResp.HandleSituation();
                if (StringUtils.equals(handleQuestions.get(0).getReasonCode(), "success")) {
                    handleSituation.setHandleWay(HandleWayEnum.HANDLE.getWay());
                } else {
                    handleSituation.setHandleWay(HandleWayEnum.REPORT.getWay());
                }
                handleSituation.setQuestionContent(reasonMap.get(handleQuestions.get(0).getReasonCode()));
                handleRecord.setHandleSituation(handleSituation);
                if (detail.getFacingCount() == 0) {
                    questionList.add(QuestionEnum.STOCK_OUT.getQuestion());
                    stockOutRecordList.add(handleRecord);
                }
                if (detail.getFacingCount() > 0 && detail.getFacingCount() < 3) {
                    questionList.add(QuestionEnum.LOW_FACE.getQuestion());
                    lowFaceRecordList.add(handleRecord);
                }
                if (detail.getIfWrongPrice() == 1) {
                    questionList.add(QuestionEnum.ERROR_PRICE.getQuestion());
                    priceErrorRecordList.add(handleRecord);
                }
            }
        }

        HandleRecordListResp resp = new HandleRecordListResp();
        resp.setStockOutRecordList(stockOutRecordList);
        resp.setLowFaceRecordList(lowFaceRecordList);
        resp.setPriceErrorRecordList(priceErrorRecordList);
        return resp;
    }

    /**
     * 商品详情
     *
     * @param productId
     * @return
     */
    public GetRecordDetailResp getRecordDetail(String productId, String submitId) {
        GetRecordDetailResp resp = new GetRecordDetailResp();
        LambdaQueryWrapper<DemoMay2021SkuDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(DemoMay2021SkuDetail::getProductId, productId);
        detailWrapper.eq(DemoMay2021SkuDetail::getResponseId, submitId);
        DemoMay2021SkuDetail detail = demoMay2021SkuDetailMapper.selectOne(detailWrapper);

        LambdaQueryWrapper<DemoMay2021Kpi> kpiWrapper = new LambdaQueryWrapper<>();
        kpiWrapper.eq(DemoMay2021Kpi::getResponseId, submitId);
        DemoMay2021Kpi kpi = demoMay2021KpiMapper.selectOne(kpiWrapper);
        if (Objects.isNull(detail) || Objects.isNull(kpi)) {
            return null;
        }

        List<ImageProductTree> productList = imageProductTreeMapper.getImageProductTreeByProductIdList(Arrays.asList(detail.getProductId()));
        Map<String, String> productMap = productList.stream().collect(Collectors.toMap(ImageProductTree::getId, ImageProductTree::getImageUrl));

        resp.setReportTime(DateTimeString.DATE_TIME_WITHOUT_SECONDS.toString(kpi.getCreateTime()));
        resp.setImageUrl("http://prod-app.ppznet.com" + productMap.get(String.valueOf(detail.getProductId())));
        resp.setProductName(detail.getProductName());
        List<String> questionList = Lists.newArrayList();
        if (detail.getFacingCount() == 0) {
            questionList.add(QuestionEnum.STOCK_OUT.getQuestion());
        }
        if (detail.getFacingCount() > 0 && detail.getFacingCount() < 3) {
            questionList.add(QuestionEnum.LOW_FACE.getQuestion());
        }
        if (detail.getIfWrongPrice() == 1) {
            questionList.add(QuestionEnum.ERROR_PRICE.getQuestion());
        }
        resp.setQuestionList(questionList);
        resp.setFloorNum(detail.getLevel());
        resp.setCoreProductStatus(detail.getIfPsku() == 1);
        resp.setProposalFace(String.valueOf(detail.getStandardFacingCount()));
        resp.setFactFace(String.valueOf(detail.getFacingCount()));
        return resp;
    }

    /**
     * 对比表格
     *
     * @param productId
     * @return
     */
    public CompareDetailResp getCompareDetail(String productId) {

        List<DemoMay2021SkuDetail> detailList = demoMay2021SkuDetailMapper.getDetailListByProductId(productId, DateTimeString.DATE.toString(LocalDateTime.now()) + " 00:00:00");
        if (CollectionUtils.isEmpty(detailList)) {
            return null;
        }

        Integer inspectNum = detailList.stream().map(DemoMay2021SkuDetail::getResponseId).distinct().collect(Collectors.toList()).size();
        Integer stockOutNum = detailList.stream().filter(detail -> detail.getFacingCount() == 0).map(DemoMay2021SkuDetail::getProductId).distinct().collect(Collectors.toList()).size();
        Integer lowFaceNum = detailList.stream().filter(detail -> detail.getFacingCount() > 0 && detail.getFacingCount() < 3).map(DemoMay2021SkuDetail::getProductId).distinct().collect(Collectors.toList()).size();
        Integer priceErrorNum = detailList.stream().filter(detail -> detail.getIfWrongPrice() == 1).map(DemoMay2021SkuDetail::getProductId).distinct().collect(Collectors.toList()).size();

        CompareDetailResp.TimeDetail inspect = new CompareDetailResp.TimeDetail();
        inspect.setNowDay(String.valueOf(inspectNum));
        inspect.setNowMonth("12");
        inspect.setLastMonth("12");

        CompareDetailResp.TimeDetail stockOut = new CompareDetailResp.TimeDetail();
        stockOut.setNowDay(String.valueOf(stockOutNum));
        stockOut.setNowMonth("2");
        stockOut.setLastMonth("2");

        CompareDetailResp.TimeDetail lowFace = new CompareDetailResp.TimeDetail();
        lowFace.setNowDay(String.valueOf(lowFaceNum));
        lowFace.setNowMonth("4");
        lowFace.setLastMonth("4");

        CompareDetailResp.TimeDetail priceError = new CompareDetailResp.TimeDetail();
        priceError.setNowDay(String.valueOf(priceErrorNum));
        priceError.setNowMonth("6");
        priceError.setLastMonth("6");

        CompareDetailResp resp = new CompareDetailResp();
        resp.setInspect(inspect);
        resp.setStockOut(stockOut);
        resp.setLowFace(lowFace);
        resp.setPriceError(priceError);
        return resp;
    }

    /**
     * 排面走势
     *
     * @param productId
     * @return
     */
    public FaceTrendResp geFaceTrend(String productId) {
        List<DemoMay2021SkuDetail> detailList = demoMay2021SkuDetailMapper.getTrendDetailListByProductId(productId);
        List<FaceTrendResp.MonthNum> monthNumList = Lists.newArrayList();
        for (DemoMay2021SkuDetail detail : detailList) {
            FaceTrendResp.MonthNum monthNum = new FaceTrendResp.MonthNum();
            monthNum.setFaceNum(String.valueOf(detail.getFacingCount()));
            monthNum.setReportTime(DateTimeString.DATE_TIME.toString(detail.getUpdateTime()));
            monthNumList.add(monthNum);
        }
        FaceTrendResp resp = new FaceTrendResp();
        resp.setMonthNumList(monthNumList);
        return resp;
    }
}
