package com.lenztech.bi.enterprise.dto.bi;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * @Author: 杨涛
 * @Date: 9/5/20 AM10:30
 */
@Data
public class GetSkuDetailResp {

    private List<SkuDetailGroup> skuDetailGroupList;

    @Data
    public static class SkuDetailGroup {
        private Integer groupNo;

        private List<SkuDetail> skuDetailList;
    }

    @Data
    public static class SkuDetail {

        private String sku;

        private String productName;

        private String brand;

        private String category;

        private String productCheckType;

        private Integer distribution;

        private BigDecimal facing;
    }

}
