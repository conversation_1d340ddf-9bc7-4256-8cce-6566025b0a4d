package com.lenztech.bi.enterprise.dto.gsk;

import lombok.Data;

/**
 * Gsk-bi结果集
 *
 * <AUTHOR>
 * @date 2020-11-10 17:02:02
 */
@Data
public class GskBiReport {
    private String id;
    private String responseId;
    private String groupId;
    private String sourceImgIdList;
    private String stitchImageUrl;
    private String imgUrl;
    private String recUrl;
    private String imgId;
    private Integer imgHeight;
    private Integer imgWidth;
    private Integer shelfHeight;
    private Integer numLayers;
    private Integer numPatchs;
    private Integer isRemake;
    private Double remakeScore;
    private String productId;
    private String coordinate;
    private Integer patchHeight;
    private Integer patchWidth;
    private Integer layer;
    private Integer column;
    private String scene;
    private Integer ifFacing;
    private String facingCount;
    private String updateTime;
}