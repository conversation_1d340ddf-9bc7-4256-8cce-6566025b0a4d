package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.dto.chinashop.ChinaShopDemoDTO;
import com.lenztech.bi.enterprise.dto.chinashop.WmddQuestionMapDTO;
import com.lenztech.bi.enterprise.entity.WmddQuestionMap;


import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/11/2 14:14
 **/
public interface IChinaShopService {

    ChinaShopDemoDTO getChinaShopResult();

    List<WmddQuestionMapDTO> getWmddQuestionMapList();

    ChinaShopDemoDTO getChinaShopResultV2(String questionId);

    ChinaShopDemoDTO getChinaShopResultV3(String questionId);

    List<WmddQuestionMapDTO> getChinaShopQuestionMapListV3();
}
