package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.common.CommonBiTargetResp;
import com.lenztech.bi.enterprise.service.CommonReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 基础bi指标Controller
 *
 * <AUTHOR>
 * @date 2021-08-19 13:44:19
 */
@Slf4j
@RestController
@RequestMapping("/biResult/common/")
public class CommonBiResultController {

    @Autowired
    private CommonReportService commonReportService;

    /**
     * 获取指标列表
     *
     * @param responseId
     * @return ResponseData<CommonBiTargetResp>
     */
    @RequestMapping(value = "getTargetList", method = RequestMethod.GET)
    public ResponseData<CommonBiTargetResp> get(String responseId) {
        try {
            CommonBiTargetResp commonBiTargetResp = commonReportService.getBiTargetList(responseId);
            return ResponseData.success().data(commonBiTargetResp);
        } catch (Exception e) {
            log.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 根据responseId查询表数据集合
     *
     * @param tableName
     * @param responseId 答卷Id
     * @return ResponseData<List<Map>>
     */
    @RequestMapping(value = "getTableDataList", method = RequestMethod.GET)
    public ResponseData<List<Map>> getTableDataList(String tableName, String responseId) {
        try {
            List<Map> list = commonReportService.getTableDataList(tableName, responseId);
            return ResponseData.success().data(list);
        } catch (Exception e) {
            log.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

}
