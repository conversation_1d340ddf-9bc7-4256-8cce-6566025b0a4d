package com.lenztech.bi.enterprise.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.comon.Constant;
import com.lenztech.bi.enterprise.dto.perfetti.PerfeittiPatch;
import com.lenztech.bi.enterprise.dto.perfetti.PerfettiAiResult;
import com.lenztech.bi.enterprise.dto.perfetti.PerfettiOriginalSceneResult;
import com.lenztech.bi.enterprise.dto.perfetti.PerfettiPosm;
import com.lenztech.bi.enterprise.dto.perfetti.PerfettiRepeatResult;
import com.lenztech.bi.enterprise.dto.perfetti.PerfettiResult;
import com.lenztech.bi.enterprise.dto.perfetti.PerfettiSku;
import com.lenztech.bi.enterprise.dto.perfetti.PerfettiStatistic;
import com.lenztech.bi.enterprise.dto.perfetti.PerfettiStitchResult;
import com.lenztech.bi.enterprise.entity.TBfdCashierDesk;
import com.lenztech.bi.enterprise.entity.TBfdDd;
import com.lenztech.bi.enterprise.entity.TBfdHangStrip;
import com.lenztech.bi.enterprise.entity.TBfdHanger;
import com.lenztech.bi.enterprise.entity.TBfdImage;
import com.lenztech.bi.enterprise.entity.TBfdOtherDisplay;
import com.lenztech.bi.enterprise.entity.TBfdPosm;
import com.lenztech.bi.enterprise.entity.TBfdProduct;
import com.lenztech.bi.enterprise.entity.TBfdRepeat;
import com.lenztech.bi.enterprise.entity.TBfdScene;
import com.lenztech.bi.enterprise.entity.TBfdShelfPitch;
import com.lenztech.bi.enterprise.entity.TBfdStitch;
import com.lenztech.bi.enterprise.entity.TBfdSweet;
import com.lenztech.bi.enterprise.entity.TProcessStatus;
import com.lenztech.bi.enterprise.mapper.TBfdCashierDeskMapper;
import com.lenztech.bi.enterprise.mapper.TBfdDdMapper;
import com.lenztech.bi.enterprise.mapper.TBfdHangStripMapper;
import com.lenztech.bi.enterprise.mapper.TBfdHangerMapper;
import com.lenztech.bi.enterprise.mapper.TBfdImageMapper;
import com.lenztech.bi.enterprise.mapper.TBfdOtherDisplayMapper;
import com.lenztech.bi.enterprise.mapper.TBfdPosmMapper;
import com.lenztech.bi.enterprise.mapper.TBfdProductMapper;
import com.lenztech.bi.enterprise.mapper.TBfdRepeatMapper;
import com.lenztech.bi.enterprise.mapper.TBfdSceneMapper;
import com.lenztech.bi.enterprise.mapper.TBfdShelfPitchMapper;
import com.lenztech.bi.enterprise.mapper.TBfdStitchMapper;
import com.lenztech.bi.enterprise.mapper.TBfdSweetMapper;
import com.lenztech.bi.enterprise.mapper.TProcessStatusMapper;
import com.lenztech.bi.enterprise.service.PerfettiService;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 不凡帝BI结果服务类
 * @Date 2021/1/26 17:12
 **/

@Service
public class PerfettiServiceImpl implements PerfettiService {

    @Autowired
    private TBfdImageMapper tBfdImageMapper;

    @Autowired
    private TBfdProductMapper tBfdProductMapper;

    @Autowired
    private TBfdRepeatMapper tBfdRepeatMapper;

    @Autowired
    private TBfdStitchMapper tBfdStitchMapper;

    @Autowired
    private TBfdSceneMapper tBfdSceneMapper;

    @Autowired
    private TProcessStatusMapper tProcessStatusMapper;

    @Autowired
    private TBfdPosmMapper tBfdPosmMapper;

    @Autowired
    private TBfdShelfPitchMapper tBfdShelfPitchMapper;

    @Autowired
    private TBfdHangStripMapper tBfdHangStripMapper;

    @Autowired
    private TBfdHangerMapper tBfdHangerMapper;

    @Autowired
    private TBfdSweetMapper tBfdSweetMapper;

    @Autowired
    private TBfdCashierDeskMapper tBfdCashierDeskMapper;

    @Autowired
    private TBfdOtherDisplayMapper tBfdOtherDisplayMapper;

    @Autowired
    private TBfdDdMapper tBfdDdMapper;

    /**
     * 查询不凡帝识别结果
     *
     * @param responseId
     * @return
     */
    @Override
    public PerfettiResult getResult(String responseId) {
        PerfettiResult result = new PerfettiResult();
        result.setResponseId(responseId);
        // 查询答卷识别状态
        LambdaQueryWrapper<TProcessStatus> statusWrapper = new LambdaQueryWrapper<>();
        statusWrapper.eq(TProcessStatus::getResponseId, responseId);
        statusWrapper.eq(TProcessStatus::getStatus, 0);
        List<TProcessStatus> statusList = tProcessStatusMapper.selectList(statusWrapper);
        if (!CollectionUtils.isEmpty(statusList)) {
            return result;
        }

        // 查询所有图片信息
        LambdaQueryWrapper<TBfdImage> imageWrapper = new LambdaQueryWrapper<>();
        imageWrapper.eq(TBfdImage::getResponseId, responseId);
        List<TBfdImage> imageList = tBfdImageMapper.selectList(imageWrapper);

        // 查询识别出的sku信息
        LambdaQueryWrapper<TBfdProduct> productWrapper = new LambdaQueryWrapper<>();
        productWrapper.eq(TBfdProduct::getResponseId, responseId);
        productWrapper.isNotNull(TBfdProduct::getProductId);
        List<TBfdProduct> productList = tBfdProductMapper.selectList(productWrapper);

        // 查询图片重复信息
        LambdaQueryWrapper<TBfdRepeat> repeatWrapper = new LambdaQueryWrapper<>();
        repeatWrapper.eq(TBfdRepeat::getResponseId, responseId);
        repeatWrapper.ne(TBfdRepeat::getGroupId, "");
        repeatWrapper.ne(TBfdRepeat::getRepeatId,"");
        List<TBfdRepeat> repeatList = tBfdRepeatMapper.selectList(repeatWrapper);

        // 查询图片拼接信息
        LambdaQueryWrapper<TBfdStitch> stitchWrapper = new LambdaQueryWrapper<>();
        stitchWrapper.eq(TBfdStitch::getResponseId, responseId);
        stitchWrapper.isNotNull(TBfdStitch::getSourceImageIdList);
        List<TBfdStitch> stitchList = tBfdStitchMapper.selectList(stitchWrapper);

        // 查询图片场景信息
        LambdaQueryWrapper<TBfdScene> sceneWrapper = new LambdaQueryWrapper<>();
        sceneWrapper.eq(TBfdScene::getResponseId, responseId);
        sceneWrapper.isNotNull(TBfdScene::getName);
        List<TBfdScene> sceneList = tBfdSceneMapper.selectList(sceneWrapper);

        // 查询图片识别场景原始框图信息
        LambdaQueryWrapper<TBfdScene> sceneOriginalWrapper = new LambdaQueryWrapper<>();
        sceneOriginalWrapper.eq(TBfdScene::getResponseId, responseId);
        sceneOriginalWrapper.isNotNull(TBfdScene::getOriginalName);
        List<TBfdScene> sceneOriginalList = tBfdSceneMapper.selectList(sceneOriginalWrapper);

        // 查询POSM信息
        LambdaQueryWrapper<TBfdPosm> posmWrapper = new LambdaQueryWrapper<>();
        posmWrapper.eq(TBfdPosm::getResponseId, responseId);
        List<TBfdPosm> tBfdPosmList = tBfdPosmMapper.selectList(posmWrapper);

        // 查询货架节数
        LambdaQueryWrapper<TBfdShelfPitch> shelfPitchWrapper = new LambdaQueryWrapper<>();
        shelfPitchWrapper.eq(TBfdShelfPitch::getResponseId, responseId);
        shelfPitchWrapper.ne(TBfdShelfPitch::getSceneCode, "");
        List<TBfdShelfPitch> shelfPitchList = tBfdShelfPitchMapper.selectList(shelfPitchWrapper);

        // 查询挂条
        LambdaQueryWrapper<TBfdHangStrip> hangStripWrapper = new LambdaQueryWrapper<>();
        hangStripWrapper.eq(TBfdHangStrip::getResponseId, responseId);
        hangStripWrapper.ne(TBfdHangStrip::getSceneCode, "");
        List<TBfdHangStrip> hangStripList = tBfdHangStripMapper.selectList(hangStripWrapper);

        // 查询挂架
        LambdaQueryWrapper<TBfdHanger> hangerWrapper = new LambdaQueryWrapper<>();
        hangerWrapper.eq(TBfdHanger::getResponseId, responseId);
        hangerWrapper.ne(TBfdHanger::getSceneCode, "");
        List<TBfdHanger> hangerList = tBfdHangerMapper.selectList(hangerWrapper);

        // 查询散糖
        LambdaQueryWrapper<TBfdSweet> sweetWrapper = new LambdaQueryWrapper<>();
        sweetWrapper.eq(TBfdSweet::getResponseId, responseId);
        sweetWrapper.ne(TBfdSweet::getSceneCode, "");
        List<TBfdSweet> sweetList = tBfdSweetMapper.selectList(sweetWrapper);

        // 查询收银台
        LambdaQueryWrapper<TBfdCashierDesk> cashierDeskWrapper = new LambdaQueryWrapper<>();
        cashierDeskWrapper.eq(TBfdCashierDesk::getResponseId, responseId);
        cashierDeskWrapper.ne(TBfdCashierDesk::getSceneCode, "");
        List<TBfdCashierDesk> cashierDeskList = tBfdCashierDeskMapper.selectList(cashierDeskWrapper);

        // 查询其他陈列
        LambdaQueryWrapper<TBfdOtherDisplay> otherWrapper = new LambdaQueryWrapper<>();
        otherWrapper.eq(TBfdOtherDisplay::getResponseId, responseId);
        otherWrapper.ne(TBfdOtherDisplay::getSceneCode, "");
        List<TBfdOtherDisplay> otherList = tBfdOtherDisplayMapper.selectList(otherWrapper);

        // 查询地堆
        LambdaQueryWrapper<TBfdDd> heapWrapper = new LambdaQueryWrapper<>();
        heapWrapper.eq(TBfdDd::getResponseId, responseId);
        heapWrapper.ne(TBfdDd::getSceneCode, "");
        if (CollectionUtils.isNotEmpty(productList)) {
            List<String> imageIdList = productList.stream().map(TBfdProduct::getImageId).collect(Collectors.toList());
            heapWrapper.in(TBfdDd::getImageId, imageIdList);
        }
        List<TBfdDd> heapList = tBfdDdMapper.selectList(heapWrapper);


        // 根据场景编码分组
        Map<String, List<TBfdScene>> sceneMap = sceneList.stream().filter(scene -> StringUtils.isNotBlank(scene.getName())).collect(Collectors.groupingBy(TBfdScene::getName));
        // 根据产经编码分组
        Map<String, List<TBfdProduct>> productSceneMap = productList.stream().filter(product -> StringUtils.isNotBlank(product.getProductId())).filter(product -> !product.getInrange().equals("2")).collect(Collectors.groupingBy(TBfdProduct::getScene));
        // 根据产经编码分组
        Map<String, List<TBfdProduct>> productImageMap = productList.stream().filter(product -> StringUtils.isNotBlank(product.getProductId())).collect(Collectors.groupingBy(TBfdProduct::getImageId));
        // 过滤翻盘图片
        List<TBfdImage> remakeImageList = imageList.stream().filter(image -> image.getIsRemake().equals(1)).collect(Collectors.toList());
        // 根据groupId分组
        Map<String, List<TBfdRepeat>> repeatMap = repeatList.stream().collect(Collectors.groupingBy(TBfdRepeat::getGroupId));
        // 根据原始场景编码分组
        Map<String, List<TBfdScene>> sceneOriginalMap = sceneOriginalList.stream().collect(Collectors.groupingBy(TBfdScene::getImageId));

        // 构建各场景统计信息
        List<PerfettiStatistic> statisticList = Lists.newArrayList();

        // 货架节数
        if (CollectionUtils.isNotEmpty(shelfPitchList)) {
            Map<String, List<TBfdShelfPitch>> shelfPitchSceneMap = shelfPitchList.stream().collect(Collectors.groupingBy(TBfdShelfPitch::getSceneCode));
            for (String sceneCode : shelfPitchSceneMap.keySet()) {
                double sum = shelfPitchSceneMap.get(sceneCode).stream().mapToDouble(TBfdShelfPitch::getPitchAmount).sum();
                String sumString = new BigDecimal(sum).setScale(1, BigDecimal.ROUND_DOWN).toString();
                buildStatistic(String.valueOf(shelfPitchSceneMap.get(sceneCode).get(0).getId()), sceneCode, sumString, "0", productSceneMap, statisticList, sceneMap, tBfdPosmList);
            }
        }

        // 挂条
        if (CollectionUtils.isNotEmpty(hangStripList)) {
            Map<String, List<TBfdHangStrip>> hangStripSceneMap = hangStripList.stream().collect(Collectors.groupingBy(TBfdHangStrip::getSceneCode));
            for (String sceneCode : hangStripSceneMap.keySet()) {
                int sum = hangStripSceneMap.get(sceneCode).stream().mapToInt(TBfdHangStrip::getHangStripAmount).sum();
                List<String> hangStripCodeList = hangStripSceneMap.get(sceneCode).stream().map(TBfdHangStrip::getHangStripCode).distinct().collect(Collectors.toList());
                buildHangStatistic(String.valueOf(hangStripSceneMap.get(sceneCode).get(0).getId()), sceneCode, String.valueOf(sum), "0", productSceneMap, statisticList, hangStripCodeList);
            }
        }

        // 挂架
        if (CollectionUtils.isNotEmpty(hangerList)) {
            Map<String, List<TBfdHanger>> hangerSceneMap = hangerList.stream().collect(Collectors.groupingBy(TBfdHanger::getSceneCode));
            for (String sceneCode : hangerSceneMap.keySet()) {
                List<String> hangStripCodeList = hangerSceneMap.get(sceneCode).stream().map(TBfdHanger::getHangerCode).distinct().collect(Collectors.toList());
                buildHangStatistic(String.valueOf(hangerSceneMap.get(sceneCode).get(0).getId()), sceneCode, String.valueOf(hangerSceneMap.get(sceneCode).size()), "0", productSceneMap, statisticList, hangStripCodeList);
            }
        }

        // 散糖
        if (CollectionUtils.isNotEmpty(sweetList)) {
            Map<String, List<TBfdSweet>> sweetSceneMap = sweetList.stream().collect(Collectors.groupingBy(TBfdSweet::getSceneCode));
            for (String sceneCode : sweetSceneMap.keySet()) {
                double sum = sweetSceneMap.get(sceneCode).stream().mapToDouble(TBfdSweet::getSweetAmount).sum();
                buildStatistic(String.valueOf(sweetSceneMap.get(sceneCode).get(0).getId()), sceneCode, String.valueOf((int)sum), "0", productSceneMap, statisticList, sceneMap, tBfdPosmList);
            }
        }

        // 收银台
        if (CollectionUtils.isNotEmpty(cashierDeskList)) {
            Map<String, List<TBfdCashierDesk>> cashierDeskSceneMap = cashierDeskList.stream().collect(Collectors.groupingBy(TBfdCashierDesk::getSceneCode));
            for (String sceneCode : cashierDeskSceneMap.keySet()) {
                double sumBlock = cashierDeskSceneMap.get(sceneCode).stream().mapToDouble(TBfdCashierDesk::getBlockNumber).sum();
                double sumLayer = cashierDeskSceneMap.get(sceneCode).stream().mapToDouble(TBfdCashierDesk::getLayerAmount).sum();
                int sum = new BigDecimal(sumBlock + sumLayer).intValue();
                buildStatistic(String.valueOf(cashierDeskSceneMap.get(sceneCode).get(0).getId()), sceneCode, String.valueOf(sum), "0", productSceneMap, statisticList, sceneMap, tBfdPosmList);
            }
        }

        // 其他陈列
        if (CollectionUtils.isNotEmpty(otherList)) {
            Map<String, List<TBfdOtherDisplay>> otherSceneMap = otherList.stream().collect(Collectors.groupingBy(TBfdOtherDisplay::getSceneCode));
            for (String sceneCode : otherSceneMap.keySet()) {
                double sum = otherSceneMap.get(sceneCode).stream().mapToDouble(TBfdOtherDisplay::getAmount).sum();
                buildStatistic(String.valueOf(otherSceneMap.get(sceneCode).get(0).getId()), sceneCode, String.valueOf(sum), "0", productSceneMap, statisticList, sceneMap, tBfdPosmList);
            }
        }

        // 地推面积
        if (CollectionUtils.isNotEmpty(heapList)) {
            Map<String, List<TBfdDd>> heapSceneMap = heapList.stream().collect(Collectors.groupingBy(TBfdDd::getSceneCode));
            for (String sceneCode : heapSceneMap.keySet()) {
                double sum = heapSceneMap.get(sceneCode).stream().mapToDouble(TBfdDd::getArea).sum();
                String sumString = "";
                if (StringUtils.equals(sceneCode, "L013")) {
                    sumString = String.valueOf((int)sum);
                } else {
                    sumString = new BigDecimal(sum).setScale(1, BigDecimal.ROUND_DOWN).toString();
                }
                buildStatistic(String.valueOf(heapSceneMap.get(sceneCode).get(0).getId()), sceneCode, "", sumString, productSceneMap, statisticList, sceneMap, tBfdPosmList);
            }
        }

        // 构建无场景统计信息
        for (String scene : productSceneMap.keySet()) {
            if (sceneMap.containsKey(scene)) {
                continue;
            }
            PerfettiStatistic statistic = new PerfettiStatistic();
            statistic.setId(responseId);
            statistic.setSceneCode(scene);
            statistic.setSceneProportion("0");
            statistic.setSceneNum("0");
            // 根据productId分组
            Map<String, List<TBfdProduct>> productMap = productSceneMap.get(scene).stream()
                    .filter(product -> !product.getInrange().equals("2"))
                    .collect(Collectors.groupingBy(TBfdProduct::getProductId));
            // 构建场景下sku列表
            List<PerfettiSku> skuList = Lists.newArrayList();
            for (String productId : productMap.keySet()) {
                if (!Constant.OTHER_CODE.equals(productId)) {
                    PerfettiSku sku = new PerfettiSku();
                    sku.setCount(productMap.get(productId).size());
                    sku.setSkuCode(productId);
                    sku.setSkuName(productMap.get(productId).get(0).getProductName());
                    skuList.add(sku);
                }
            }
            statistic.setSkuList(skuList);
            statisticList.add(statistic);
        }
        result.setStatisticList(statisticList);

        // 构建识别明细
        List<PerfettiAiResult> aiResultList = Lists.newArrayList();
        for (TBfdImage tBfdImage : imageList) {
            PerfettiAiResult aiResult = new PerfettiAiResult();
            aiResult.setImgUrl(tBfdImage.getImageUrl());
            aiResult.setRecUrl(tBfdImage.getRecUrl());
            aiResult.setImgId(tBfdImage.getImageId());
            aiResult.setImgHeight(tBfdImage.getImageHeight());
            aiResult.setImgWidth(tBfdImage.getImageWidth());
            aiResult.setShelfHeight(tBfdImage.getImageHeight());
            aiResult.setNumLayers(tBfdImage.getNumPatches());
            aiResult.setNumPatches(tBfdImage.getNumPatches());
            aiResult.setRemake(tBfdImage.getIsRemake());
            aiResult.setRemakeScore(tBfdImage.getRemakeScore());
            List<PerfettiOriginalSceneResult> originalSceneResultList = getCoordinateList(sceneOriginalMap, tBfdImage.getImageId());
            aiResult.setOriginalSceneResultList(originalSceneResultList);

            List<PerfeittiPatch> patchList = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(productImageMap.get(tBfdImage.getImageId()))) {
                for (TBfdProduct tBfdProduct : productImageMap.get(tBfdImage.getImageId())) {
                    if (!Constant.OTHER.equals(tBfdProduct.getProductName()) && !Constant.OTHER_CODE.equals(tBfdProduct.getProductId())) {
                        PerfeittiPatch patch = new PerfeittiPatch();
                        patch.setId(tBfdProduct.getProductId());
                        patch.setSkuCode(tBfdProduct.getProductId());
                        patch.setSkuName(tBfdProduct.getProductName());
                        patch.setCoordinate(tBfdProduct.getCoordinate());
                        patch.setPatchHeight(tBfdProduct.getPatchHeight());
                        patch.setPatchWidth(tBfdProduct.getPatchWidth());
                        patch.setLayer(tBfdProduct.getLayer());
                        patch.setColumn(tBfdProduct.getColumnNumber());
                        patch.setInrange(Integer.parseInt(tBfdProduct.getInrange()));
                        patch.setScene(tBfdProduct.getScene());
                        patch.setFacing(tBfdProduct.getIfFacing() == 1 ? Boolean.TRUE : Boolean.FALSE);
                        patch.setFacingCount(tBfdProduct.getFacingCount());
                        patchList.add(patch);
                    }
                }
            }

            // 单图posm数据
            List<PerfettiPosm> perfettiPosmList = Lists.newArrayList();
            for (TBfdPosm posm : tBfdPosmList) {
                if (posm.getImageId().equals(tBfdImage.getImageId()) && StringUtils.isNotEmpty(posm.getProductId()) && StringUtils.isNotEmpty(posm.getProductName())) {
                    PerfettiPosm perfettiPosm = new PerfettiPosm();
                    perfettiPosm.setPosmCode(posm.getProductId());
                    perfettiPosm.setPosmName(posm.getProductName());
                    perfettiPosmList.add(perfettiPosm);
                }
            }
            aiResult.setPosmList(perfettiPosmList);
            aiResult.setPatches(patchList);
            aiResultList.add(aiResult);
        }
        result.setAiResult(aiResultList);

        // 构建拼接结果
        List<PerfettiStitchResult> stitchResultList = Lists.newArrayList();
        for (TBfdStitch tBfdStitch : stitchList) {
            PerfettiStitchResult stitchResult = new PerfettiStitchResult();
            stitchResult.setGroupId(tBfdStitch.getGroupId());
            stitchResult.setSourceImageIdList(JSON.parseArray(tBfdStitch.getSourceImageIdList(), String.class));
            stitchResult.setStitchImageUrl(tBfdStitch.getStitchImageUrl());
            stitchResultList.add(stitchResult);
        }
        result.setAiStitchResult(stitchResultList);
        // 构建重复图结果
        List<PerfettiRepeatResult> repeatResultList = Lists.newArrayList();
        for (String groupId : repeatMap.keySet()) {
            PerfettiRepeatResult repeatResult = new PerfettiRepeatResult();
            List<String> repeatIdList = repeatMap.get(groupId).stream().map(TBfdRepeat::getRepeatId).collect(Collectors.toList());
            repeatResult.setRepeatIdList(repeatIdList);
            repeatResultList.add(repeatResult);
        }
        result.setAiRepeatResult(repeatResultList);

        result.setRemakeStatus(remakeImageList.size() > 0 ? Boolean.TRUE : Boolean.FALSE);
        result.setRepeatStatus(repeatResultList.size() > 0 ? Boolean.TRUE : Boolean.FALSE);
        return result;
    }

    private void buildHangStatistic(String id, String sceneCode, String sum, String proportion, Map<String, List<TBfdProduct>> productSceneMap, List<PerfettiStatistic> statisticList, List<String> codeList) {

        PerfettiStatistic statistic = new PerfettiStatistic();
        statistic.setId(id);
        statistic.setSceneCode(sceneCode);
        statistic.setSceneProportion(proportion);
        statistic.setSceneNum(sum);

        if (CollectionUtils.isNotEmpty(productSceneMap.get(sceneCode))) {
            // 根据productId分组
            Map<String, List<TBfdProduct>> productMap = productSceneMap.get(sceneCode).stream().collect(Collectors.groupingBy(TBfdProduct::getProductId));

            // 构建场景下sku列表
            List<PerfettiSku> skuList = Lists.newArrayList();
            for (String productId : productMap.keySet()) {
                if (!Constant.OTHER_CODE.equals(productId)) {
                    PerfettiSku sku = new PerfettiSku();
                    sku.setCount(productMap.get(productId).size());
                    sku.setSkuCode(productId);
                    sku.setSkuName(productMap.get(productId).get(0).getProductName());
                    skuList.add(sku);
                }
            }
            statistic.setSkuList(skuList);
        }


        // 分场景汇总POSM信息
        List<PerfettiPosm> posmList = Lists.newArrayList();
        for (String code : codeList) {
            if (StringUtils.isNotEmpty(code)) {
                PerfettiPosm posm = new PerfettiPosm();
                posm.setPosmCode(code);
                posmList.add(posm);
            }
        }
        statistic.setPosmList(posmList);
        statisticList.add(statistic);
    }

    private void buildStatistic(String id, String sceneCode, String sum, String proportion, Map<String, List<TBfdProduct>> productSceneMap, List<PerfettiStatistic> statisticList, Map<String, List<TBfdScene>> sceneMap, List<TBfdPosm> tBfdPosmList) {
        PerfettiStatistic statistic = new PerfettiStatistic();
        statistic.setId(id);
        statistic.setSceneCode(sceneCode);
        statistic.setSceneProportion(proportion);
        if (StringUtils.isEmpty(sum)) {
            statistic.setSceneNum("");
        } else {
            statistic.setSceneNum(new BigDecimal(sum).setScale(1, BigDecimal.ROUND_DOWN).toString());
        }

        if (CollectionUtils.isNotEmpty(productSceneMap.get(sceneCode))) {
            // 根据productId分组
            Map<String, List<TBfdProduct>> productMap = productSceneMap.get(sceneCode).stream().collect(Collectors.groupingBy(TBfdProduct::getProductId));

            // 构建场景下sku列表
            List<PerfettiSku> skuList = Lists.newArrayList();
            for (String productId : productMap.keySet()) {
                if (!Constant.OTHER_CODE.equals(productId)) {
                    PerfettiSku sku = new PerfettiSku();
                    sku.setCount(productMap.get(productId).size());
                    sku.setSkuCode(productId);
                    sku.setSkuName(productMap.get(productId).get(0).getProductName());
                    skuList.add(sku);
                }
            }
            statistic.setSkuList(skuList);
        }

        // 分场景汇总POSM信息
        List<String> sceneIdList = sceneMap.get(sceneCode).stream().map(TBfdScene::getSceneId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sceneIdList)) {
            return;
        }
        List<PerfettiPosm> posmList = Lists.newArrayList();
        for (TBfdPosm tBfdPosm : tBfdPosmList) {
            // 如果单场景内场景id包括posm信息
            if (sceneIdList.contains(tBfdPosm.getPosmId()) && StringUtils.isNotEmpty(tBfdPosm.getProductId()) && StringUtils.isNotEmpty(tBfdPosm.getProductName())) {
                PerfettiPosm posm = new PerfettiPosm();
                posm.setPosmCode(tBfdPosm.getProductId());
                posm.setPosmName(tBfdPosm.getProductName());
                posmList.add(posm);
            }
        }
        statistic.setPosmList(posmList);
        statisticList.add(statistic);
    }
    /**
     * 组装原始场景坐标信息
     *
     * @param sceneOriginalMap
     * @param imageId
     * @return
     */
    private List<PerfettiOriginalSceneResult> getCoordinateList(Map<String, List<TBfdScene>> sceneOriginalMap, String imageId) {
        List<TBfdScene> tBfdSceneList = sceneOriginalMap.get(imageId);
        List<PerfettiOriginalSceneResult> resultList = Lists.newArrayList();
        for (TBfdScene tBfdScene : tBfdSceneList) {
            PerfettiOriginalSceneResult result = new PerfettiOriginalSceneResult();
            result.setOriginalName(tBfdScene.getOriginalName());
            result.setCoordinate(tBfdScene.getCoordinate());
            resultList.add(result);
        }
        return resultList;
    }

}
