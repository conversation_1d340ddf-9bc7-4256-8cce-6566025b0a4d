package com.lenztech.bi.enterprise.dto.cloudPatrolShop;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(name = "StoreImageListResp", description = "门店题目图片列表response")
public class StoreImageListResp {

    /**
     * 题目id
     */
    @Schema(name = "questionId", description = "题目id", example = "123456")
    private String questionId;

    /**
     * 题目标题
     */
    @Schema(name = "title", description = "题目标题", example = "题目1")
    private String title;

    /**
     * 拍摄时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(name = "createTime", description = "拍摄时间", pattern = "yyyy-MM-dd HH:mm:ss", example = "2023-01-01 13:20:00")
    private Date createTime;

    /**
     * 图片列表
     */
    @Schema(name = "imageList", description = "图片列表")
    private List<StoreImageResp> imageList;

}
