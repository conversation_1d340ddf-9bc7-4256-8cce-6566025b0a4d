/*
 * Copyright (c) 2022. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lenztech.bi.enterprise.dto.xinhe.DownloadExcelDTO;
import com.lenztech.bi.enterprise.entity.XinheAppAggData;
import com.lenztech.bi.enterprise.mapper.XinheAppAggDataMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * 		date 2022/10/13
 **/
@Service
public class XinHeExcelDownloadServiceImpl extends ServiceImpl<XinheAppAggDataMapper, XinheAppAggData> {
	
	public List<XinheAppAggData> getListByImportInfo(DownloadExcelDTO downloadExcelDTO) {
		return baseMapper.getListByImportInfo(downloadExcelDTO);
	}
}
