package com.lenztech.bi.enterprise.dto.bi;

import lombok.Data;

/**
 * @Description:sku信息
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 3/10/20 PM3:15
 */
@Data
public class ReportSkuListItem {

    private Integer id;

    /**
     * sku 名称
     */
    private String name;

    /**
     * 品牌 名称
     */
    private String brand;

    private Integer brandId = 0;


    /**
     * 品类
     */
    private String category;

    private Integer categoryId = 0;

    /**
     * sku类型描述
     */
    private String skuTypeDes;
    /**
     * sku类型
     */
    private Integer skuType;

}
