package com.lenztech.bi.enterprise.dto.car;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/9/1 11:04
 **/

@Data
public class CarProportionData {
    /**
     * 品牌
     */
    @Excel(name = "brand")
    private String brand;
    /**
     * 计划完成
     */
    @Excel(name = "targetComplete")
    private Integer targetComplete;
    /**
     * 实际完成
     */
    @Excel(name = "practicalComplete")
    private Integer practicalComplete;
    /**
     * 完成率
     */
    @Excel(name = "completeRate")
    private String completeRate;
}
