package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
public class UnileverOsaStatus extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 问卷ID
     */
    private String responseId;

    /**
     *  COTC组名
     */
    @TableField("COTC_name")
    private String cotcName;

    /**
     * COTC是否分销
     */
    private String status;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }

    public String getCotcName() {
        return cotcName;
    }

    public void setCotcName(String cotcName) {
        this.cotcName = cotcName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "UnileverOsaStatus{" +
        "id=" + id +
        ", responseId=" + responseId +
        ", cotcName=" + cotcName +
        ", status=" + status +
        "}";
    }
}
