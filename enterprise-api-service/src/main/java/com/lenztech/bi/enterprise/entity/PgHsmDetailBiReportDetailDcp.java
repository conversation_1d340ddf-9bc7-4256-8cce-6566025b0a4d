package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public class PgHsmDetailBiReportDetailDcp extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 操作id
     */
    private String taskId;

    private String responseId;

    @TableField("addressIDnum")
    private String addressidnum;

    private String category;

    private String month;

    private String date;

    /**
     * 指标名称
     */
    private String kpiName;

    /**
     * barCode
     */
    private String barCodeDetail;

    /**
     * 指标明细名称
     */
    private String kpiNameDetail;

    /**
     * 指标明细值
     */
    private String kpiValueDetail;

    private LocalDateTime createTime;

    /**
     * 指标值
     */
    private String kpiValue;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public String getAddressidnum() {
        return addressidnum;
    }

    public void setAddressidnum(String addressidnum) {
        this.addressidnum = addressidnum;
    }
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }
    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }
    public String getKpiName() {
        return kpiName;
    }

    public void setKpiName(String kpiName) {
        this.kpiName = kpiName;
    }
    public String getBarCodeDetail() {
        return barCodeDetail;
    }

    public void setBarCodeDetail(String barCodeDetail) {
        this.barCodeDetail = barCodeDetail;
    }
    public String getKpiNameDetail() {
        return kpiNameDetail;
    }

    public void setKpiNameDetail(String kpiNameDetail) {
        this.kpiNameDetail = kpiNameDetail;
    }
    public String getKpiValueDetail() {
        return kpiValueDetail;
    }

    public void setKpiValueDetail(String kpiValueDetail) {
        this.kpiValueDetail = kpiValueDetail;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public String getKpiValue() {
        return kpiValue;
    }

    public void setKpiValue(String kpiValue) {
        this.kpiValue = kpiValue;
    }

    @Override
    public String toString() {
        return "PgHsmDetailBiReportDetailDcp{" +
            "id=" + id +
            ", taskId=" + taskId +
            ", responseId=" + responseId +
            ", addressidnum=" + addressidnum +
            ", category=" + category +
            ", month=" + month +
            ", date=" + date +
            ", kpiName=" + kpiName +
            ", barCodeDetail=" + barCodeDetail +
            ", kpiNameDetail=" + kpiNameDetail +
            ", kpiValueDetail=" + kpiValueDetail +
            ", createTime=" + createTime +
            ", kpiValue=" + kpiValue +
        "}";
    }
}
