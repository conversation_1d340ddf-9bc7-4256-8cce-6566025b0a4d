package com.lenztech.bi.enterprise.dto.monster;

import com.lenztech.bi.enterprise.entity.MonsterMec2ndDisplay;
import com.lenztech.bi.enterprise.entity.MonsterPosmAvailability;
import lombok.Data;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/5/26
 * Time: 10:52
 * 类功能:
 */
@Data
public class MonsterMec2ndDisplayDTO {

    List<MonsterMec2ndDisplay> monsterMec2ndDisplayList;

    /**
     * 平均数集合
     */
    List<MonsterMec2ndDisplay> monsterMec2ndDisplayAvgList;
}
