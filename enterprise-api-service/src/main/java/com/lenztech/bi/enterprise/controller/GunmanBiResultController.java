package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.bnhd.ProductPriceInfo;
import com.lenztech.bi.enterprise.dto.bnhd.RecognizeSceneInfo;
import com.lenztech.bi.enterprise.dto.gunman.SkuResultInfo;
import com.lenztech.bi.enterprise.entity.BnhdPosmsResult;
import com.lenztech.bi.enterprise.entity.BnhdStoreRecord;
import com.lenztech.bi.enterprise.entity.QiangshouStoreRecord;
import com.lenztech.bi.enterprise.service.BnhdReportService;
import com.lenztech.bi.enterprise.service.GunmanReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/03/15
 * Time: 17:19
 * 类功能: 枪手poc相关接口
 */
@RestController
@RequestMapping("/biResult/gunman/")
public class GunmanBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(GunmanBiResultController.class);

    @Autowired
    private GunmanReportService gunmanReportService;

    /**
     * 获取枪手门店相关信息
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getStoreinfo", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取门店相关信息")
    public ResponseData<QiangshouStoreRecord> getStoreinfo(String responseId) {
        try {
            QiangshouStoreRecord bnhdStoreRecord = gunmanReportService.getStoreinfo(responseId);
            return ResponseData.success().data(bnhdStoreRecord);
        } catch (Exception e) {
            logger.error("/getStoreinfo========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取枪手门店sku结果
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getSkuResult", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取枪手门店sku结果")
    public ResponseData getSkuResult(String responseId) {
        try {
            SkuResultInfo skuResultInfo = gunmanReportService.getSkuResult(responseId);
            return ResponseData.success().data(skuResultInfo);
        } catch (Exception e) {
            logger.error("/getSkuResult========", e);
        }
        return ResponseData.failure();
    }

}
