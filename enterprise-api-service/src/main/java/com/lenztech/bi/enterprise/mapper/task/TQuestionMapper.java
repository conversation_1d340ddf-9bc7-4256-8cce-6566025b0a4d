package com.lenztech.bi.enterprise.mapper.task;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lenztech.bi.enterprise.entity.TQuestion;
import com.lenztech.bi.enterprise.entity.TQuestionTemp;

/**
 * <p>
 * 单份答卷表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-28
 */
@DS("task")
public interface TQuestionMapper extends BaseMapper<TQuestion> {

}
