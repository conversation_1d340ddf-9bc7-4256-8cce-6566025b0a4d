package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.unileverapp.ApiResultDTO;
import com.lenztech.bi.enterprise.service.impl.TongJieAppServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022-01-17 17:50
 */
@RestController
@RequestMapping("/biResult/tongJie/")
@Slf4j
public class TongJieAppBiResultController {

    @Autowired
    private TongJieAppServiceImpl tongJieAppServiceImpl;

    /**
     * 查询同界App识别结果集
     *
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getBiTargetList", method = RequestMethod.GET)
    public ResponseData<ApiResultDTO> getBiTargetList(String responseId) {
        log.info("【同界App查询结果请求】, responseId:{}", responseId);
        try {
            return ResponseData.success().data(tongJieAppServiceImpl.getBiTargetList(responseId));
        } catch (Exception e) {
            log.error("【同界App查询结果请求失败!】", e);
        }
        return ResponseData.failure();
    }

}
