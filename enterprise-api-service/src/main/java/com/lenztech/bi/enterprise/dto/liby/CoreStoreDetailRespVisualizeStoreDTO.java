package com.lenztech.bi.enterprise.dto.liby;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/14 13:05
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CoreStoreDetailRespVisualizeStoreDTO {

    /**
     * 形象店类型
     */
    private String visualizeStoreType;
    /**
     * 每种形象店三个雷达图
     */
    private List<CoreStoreDetailRespVisualizeStoreRadarDTO> coreStoreDetailRespVisualizeStoreRadarDTOS;

}
