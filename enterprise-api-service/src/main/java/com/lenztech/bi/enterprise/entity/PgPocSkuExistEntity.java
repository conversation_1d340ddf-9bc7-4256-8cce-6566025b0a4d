package com.lenztech.bi.enterprise.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 宝洁POC有识别结果sku报表
 * <AUTHOR>
 * @version V1.0
 * @date 2019-11-11 15:29
 * @since JDK 1.8
 */
@Data
public class PgPocSkuExistEntity {

    /**
     * 任务id
     */
    private String taskid;
    /**
     * 答卷id
     */
    private String rid;
    /**
     * 品牌名称
     */
    private String brand;
    /**
     * 产品id
     */
    private Integer pid;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 是否是必分销
     */
    private String ifMust;
    /**
     * 面位数
     */
    private String facing;
    /**
     * 所在层数
     */
    private String layer;
    /**
     * 是否是黄金层
     */
    private String ifGoldLayer;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
