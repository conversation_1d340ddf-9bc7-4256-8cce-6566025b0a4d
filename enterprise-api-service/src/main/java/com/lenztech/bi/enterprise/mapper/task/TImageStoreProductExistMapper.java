package com.lenztech.bi.enterprise.mapper.task;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lenztech.bi.enterprise.entity.TImageStoreProductExist;

/**
 * <p>
 * 门店中某个商品是否存在 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@DS("task")
public interface TImageStoreProductExistMapper extends BaseMapper<TImageStoreProductExist> {

}
