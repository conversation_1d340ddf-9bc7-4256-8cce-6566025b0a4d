package com.lenztech.bi.enterprise.entity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/9 15:25
 * @since JDK 1.8
 */
@Data
public class LibyStoreDetailReportPcEntity {

    /**
     * 任务id
     */
    private String taskId;
    /**
     * 答卷id
     */
    private String responseId;
    /**
     * 展示场景规则
     */
    private String rule;
    /**
     * 产品id
     */
    private String productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 是否分销 0/1
     */
    private String isExist;
    /**
     * 最后更新时间
     */
    private Date updateTime;

}
