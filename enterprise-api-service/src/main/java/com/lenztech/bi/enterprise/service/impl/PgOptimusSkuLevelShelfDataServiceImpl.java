package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.entity.PgOptimusSkuLevelShelfData;
import com.lenztech.bi.enterprise.mapper.PgOptimusSkuLevelShelfDataMapper;
import com.lenztech.bi.enterprise.service.IPgOptimusSkuLevelShelfDataService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * SKU级别货架数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Service
public class PgOptimusSkuLevelShelfDataServiceImpl extends ServiceImpl<PgOptimusSkuLevelShelfDataMapper, PgOptimusSkuLevelShelfData> implements IPgOptimusSkuLevelShelfDataService {

}
