package com.lenztech.bi.enterprise.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.entity.XuehuaResults;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("lenzbi")
public interface XuehuaResultsMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(XuehuaResults record);

    int insertSelective(XuehuaResults record);

    XuehuaResults selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(XuehuaResults record);

    int updateByPrimaryKey(XuehuaResults record);

    List<XuehuaResults> getResultByResponseId(@Param("responseId") String responseId);
}