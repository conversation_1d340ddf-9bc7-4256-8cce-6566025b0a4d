package com.lenztech.bi.enterprise.entity;

import com.lenztech.bi.enterprise.entity.base.BaseEntity;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-28
 */
public class QingpiTarProductList extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private Integer displayId;

    private Integer productId;

    private Integer jishu;

    public Integer getDisplayId() {
        return displayId;
    }

    public void setDisplayId(Integer displayId) {
        this.displayId = displayId;
    }
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }
    public Integer getJishu() {
        return jishu;
    }

    public void setJishu(Integer jishu) {
        this.jishu = jishu;
    }

    @Override
    public String toString() {
        return "QingpiTarProductList{" +
        "displayId=" + displayId +
        ", productId=" + productId +
        ", jishu=" + jishu +
        "}";
    }
}
