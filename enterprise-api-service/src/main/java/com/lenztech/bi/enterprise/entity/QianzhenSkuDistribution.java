package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 千镇图片sku
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("qianzhen_sku_distribution")
public class QianzhenSkuDistribution implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    private Integer id;

    /**
     * rid
     */
    private String responseId;

    /**
     * 图片id
     */
    private String imageId;

    /**
     * 图片url
     */
    private String imageUrl;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 坐标
     */
    private String crood;

    /**
     * 层级
     */
    private Integer level;


    /**
     * 是否疑似翻拍0否1是
     */
    private Integer isRemake;

    /**
     * 是否疑似摆拍0否1是
     */
    private Integer isPose;



}
