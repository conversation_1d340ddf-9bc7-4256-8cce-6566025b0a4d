package com.lenztech.bi.enterprise.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.MappingJsonFactory;
import net.logstash.logback.decorate.JsonFactoryDecorator;

/**
 * 解决logstash appender file乱码
 * <AUTHOR>
 * @date 2018/9/14 17:39
 * @since JDK 1.8
 * @version V1.0
 */
public class MyJsonFactoryDecorator implements JsonFactoryDecorator {
    /**
     * 禁用对非ASCII码进行escape编码的特性
     * @param factory
     * @return
     */
    @Override
    public MappingJsonFactory decorate(MappingJsonFactory factory) {
        factory.disable(JsonGenerator.Feature.ESCAPE_NON_ASCII);
        return factory;
    }

}
