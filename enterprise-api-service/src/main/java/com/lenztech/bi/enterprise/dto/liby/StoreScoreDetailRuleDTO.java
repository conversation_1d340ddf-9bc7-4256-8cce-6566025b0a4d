package com.lenztech.bi.enterprise.dto.liby;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/14 14:10
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class StoreScoreDetailRuleDTO {
    /**
     * 规则
     */
    private String rule;
    /**
     * 识别详情列表
     */
    private List<StoreScoreDetailRuleExamineDTO> storeScoreDetailRuleExamineDTOS;

}
