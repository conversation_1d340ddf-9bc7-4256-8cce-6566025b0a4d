package com.lenztech.bi.enterprise.controller.base;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务Controller
 *
 * @Author: yuy<PERSON>
 * @Date: 2021-06-01 13:11:28
 */
@Slf4j
@RestController
@RequestMapping("/service")
public class ServiceController {

    /**
     * 是否暂停服务标识，主要用于消息监听使用
     */
    public volatile static boolean IS_STOP_SERVICE = false;

    /**
     * 心跳
     *
     * @return ResponseData<>
     */
    @RequestMapping(value = "/index", method = RequestMethod.GET)
    public String common() {
        return "SUCCESS";
    }

    /**
     * 暂停服务
     *
     * @return ResponseData<>
     */
    @RequestMapping(value = "/stop", method = RequestMethod.GET)
    public String stop() {

//        // mq消费者shutdown
//        if (CollectionUtils.isNotEmpty(MqClientUtils.getConsumerList())){
//            for (Consumer consumer : MqClientUtils.getConsumerList()){
//                consumer.shutdown();
//            }
//        }

        // mns消费者可指定标识位
        IS_STOP_SERVICE = true;
        return "SUCCESS";
    }

    /**
     * 是否可以kill服务
     *
     * @return ResponseData<>
     */
    @RequestMapping(value = "/isCanKill", method = RequestMethod.GET)
    public Boolean isCanKill() {
        log.info("系统中没有可执行任务！可以kill服务");
        return true;
//        Integer activeCount = CallbackThreadPool.getActiveCount();
//        activeCount += DingDingThreadPool.getActiveCount();
//        activeCount += ImageDownloadFinishThreadPool.getActiveCount();
//        activeCount += ImageUploadThreadPool.getActiveCount();
//        activeCount += KafkaConsumerThreadPool.getActiveCount();
//        activeCount += PecSubmitThreadPool.getActiveCount();
//        activeCount += SendMessageToImgThreadPool.getActiveCount();
//        activeCount += SubmitThreadPool.getActiveCount();
//        if (activeCount == 0) {
//            log.info("系统中没有可执行任务！可以kill服务");
//            return true;
//        }
//        log.info("系统中全部线程池总活跃数！activeCount={}, ", activeCount);
//        return false;
    }

//    /**
//     * 监控线程池情况
//     *
//     * @return ResponseData<>
//     */
//    @Scheduled(initialDelay = 30 * 1000, fixedRate = 60 * 1000)
//    @RequestMapping(value = "/monitorThreadPool", method = RequestMethod.GET)
//    public void monitorThreadPool() {
//        Integer taskCount = CallbackThreadPool.getQueueSize();
//        if (taskCount > 100) {
//            noticeService.sendThreadPoolWarning("CallbackThreadPool", taskCount);
//        }
//        taskCount = ImageDownloadFinishThreadPool.getQueueSize();
//        if (taskCount > 100) {
//            noticeService.sendThreadPoolWarning("ImageDownloadFinishThreadPool", taskCount);
//        }
//        taskCount = ImageUploadThreadPool.getQueueSize();
//        if (taskCount > 100) {
//            noticeService.sendThreadPoolWarning("ImageUploadThreadPool", taskCount);
//        }
//        taskCount = KafkaConsumerThreadPool.getQueueSize();
//        if (taskCount > 100) {
//            noticeService.sendThreadPoolWarning("KafkaConsumerThreadPool", taskCount);
//        }
//        taskCount = PecSubmitThreadPool.getQueueSize();
//        if (taskCount > 100) {
//            noticeService.sendThreadPoolWarning("PecSubmitThreadPool", taskCount);
//        }
//        taskCount = SendMessageToImgThreadPool.getQueueSize();
//        if (taskCount > 100) {
//            noticeService.sendThreadPoolWarning("SendMessageToImgThreadPool", taskCount);
//        }
//        taskCount = SubmitThreadPool.getQueueSize();
//        if (taskCount > 100) {
//            noticeService.sendThreadPoolWarning("SubmitThreadPool", taskCount);
//        }
//    }

}
