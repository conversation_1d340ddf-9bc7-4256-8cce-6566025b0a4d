package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.dto.junlibao.OriginalRepeatBiResultDTO;
import com.lenztech.bi.enterprise.dto.junlibao.RepeatBiResultResp;
import com.lenztech.bi.enterprise.entity.TJlbDuplicatePoc;
import com.lenztech.bi.enterprise.mapper.TJlbDuplicatePocMapper;
import com.lenztech.bi.enterprise.service.JunlebaoService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @ClassName JunlebaoServiceImpl
 * @Description: TODO
 * <AUTHOR>
 * @Date 2022/3/15
 **/

@Service
public class JunlebaoServiceImpl implements JunlebaoService {

    private TJlbDuplicatePocMapper tjlbDuplicatePocMapper;

    @Autowired
    public JunlebaoServiceImpl(TJlbDuplicatePocMapper tjlbDuplicatePocMapper) {
        this.tjlbDuplicatePocMapper = tjlbDuplicatePocMapper;
    }


    /**
     * 获取查重结果
     *
     * @param responseId
     * @return
     */
    @Override
    public RepeatBiResultResp getRepeatResult(String responseId) {
        RepeatBiResultResp resp = new RepeatBiResultResp();


        LambdaQueryWrapper<TJlbDuplicatePoc> jlbWrapper = new LambdaQueryWrapper<>();
        jlbWrapper.eq(TJlbDuplicatePoc::getResponseId, responseId);
        List<TJlbDuplicatePoc> tJlbDuplicatePocs = tjlbDuplicatePocMapper.selectList(jlbWrapper);

        if (CollectionUtils.isEmpty(tJlbDuplicatePocs)) {
            return resp;
        }

        List<OriginalRepeatBiResultDTO> repeatResultList = Lists.newArrayList();
        for (TJlbDuplicatePoc tJlbDuplicatePoc : tJlbDuplicatePocs) {
            OriginalRepeatBiResultDTO dto = new OriginalRepeatBiResultDTO();
            dto.setImageId(tJlbDuplicatePoc.getImageId());
            dto.setGroupId(tJlbDuplicatePoc.getGroupId());
            dto.setUrl(tJlbDuplicatePoc.getImageUrl());
            dto.setScore(String.valueOf(tJlbDuplicatePoc.getScore()));
            repeatResultList.add(dto);
        }

        resp.setRepeatResultList(repeatResultList);
        return resp;
    }

}
