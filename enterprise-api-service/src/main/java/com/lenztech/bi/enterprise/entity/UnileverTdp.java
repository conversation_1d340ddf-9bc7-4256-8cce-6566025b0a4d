package com.lenztech.bi.enterprise.entity;

import com.lenztech.bi.enterprise.entity.base.BaseEntity;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
public class UnileverTdp extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    private Integer id;

    /**
     * 问卷ID
     */
    private String responseId;

    /**
     * 品类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 该条码下SKU分销数量
     */
    private Integer count;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }
    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }
    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    @Override
    public String toString() {
        return "UnileverTdp{" +
        "id=" + id +
        ", responseId=" + responseId +
        ", category=" + category +
        ", brand=" + brand +
        ", barcode=" + barcode +
        ", count=" + count +
        "}";
    }
}
