//package com.lenztech.bi.enterprise.service.impl;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.google.common.collect.Lists;
//import com.lenztech.bi.enterprise.dto.CompanyResponseDTO;
//import com.lenztech.bi.enterprise.dto.CompanyUserDTO;
//import com.lenztech.bi.enterprise.dto.ExportStatisticsDTO;
//import com.lenztech.bi.enterprise.dto.GetStatisticsReqDTO;
//import com.lenztech.bi.enterprise.dto.OperateStatisticDTO;
//import com.lenztech.bi.enterprise.dto.PageDataResult;
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.dto.StoreListDTO;
//import com.lenztech.bi.enterprise.dto.UserResponseDTO;
//import com.lenztech.bi.enterprise.dto.UserResponseListDTO;
//import com.lenztech.bi.enterprise.dto.UserUseAppDayDTO;
//import com.lenztech.bi.enterprise.dto.UserUseStatisticsDTO;
//import com.lenztech.bi.enterprise.dto.UserVisitStoreNumDTO;
//import com.lenztech.bi.enterprise.entity.Company;
//import com.lenztech.bi.enterprise.entity.TAnswer;
//import com.lenztech.bi.enterprise.entity.TResponse;
//import com.lenztech.bi.enterprise.entity.TUseStatistics;
//import com.lenztech.bi.enterprise.entity.TUser;
//import com.lenztech.bi.enterprise.mapper.bienterprise.ArgusCompanyDao;
//import com.lenztech.bi.enterprise.mapper.bienterprise.CompanyMapper;
//import com.lenztech.bi.enterprise.mapper.task.OperateLogMapper;
//import com.lenztech.bi.enterprise.mapper.task.TAnswerMapper;
//import com.lenztech.bi.enterprise.mapper.task.TResponseMapper;
//import com.lenztech.bi.enterprise.mapper.task.TUserMapper;
//import com.lenztech.bi.enterprise.mapper.task.UseStatisticsMapper;
//import com.lenztech.bi.enterprise.service.ArgusCompanyService;
//import com.lenztech.bi.enterprise.service.IUseStatisticsService;
//import com.lenztech.bi.enterprise.utils.ExcelUtils;
//import com.lenztech.bi.enterprise.utils.date.DateTimeString;
//import com.lenztech.bi.enterprise.utils.date.DateTimeTool;
//import com.lenztech.bi.enterprise.utils.date.DateTool;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//import java.math.BigDecimal;
//import java.time.LocalDateTime;
//import java.util.Arrays;
//import java.util.Comparator;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @Description 用户使用统计服务类
// * @Date 2020/9/10 16:46
// **/
//@Slf4j
//@Service
//public class UseStatisticsServiceImpl implements IUseStatisticsService {
//
//    @Autowired
//    private TResponseMapper tResponseMapper;
//
//    @Autowired
//    private TUserMapper tUserMapper;
//
//    @Autowired
//    private TAnswerMapper tAnswerMapper;
//
//    @Autowired
//    private UseStatisticsMapper useStatisticsMapper;
//
//    @Autowired
//    private ArgusCompanyService argusCompanyService;
//
//    @Autowired
//    private ArgusCompanyDao argusCompanyDao;
//
//    @Autowired
//    private CompanyMapper companyMapper;
//
//    @Autowired
//    private OperateLogMapper operateLogMapper;
//
//    @Override
//    public void userUseStatistics(String executeDate) {
//        if (StringUtils.isBlank(executeDate)) {
//            executeDate = DateTimeString.DATE.toString(LocalDateTime.now().plusDays(-1));
//        }
//        // 获取 uptime 在今天范围内的答卷
//        String startTime = executeDate + " 00:00:00";
//        String endTime = executeDate + " 23:59:59";
//        List<CompanyResponseDTO> responseList = tResponseMapper.getEnterpriseResponseByUpTime(startTime, endTime);
//        if (CollectionUtils.isEmpty(responseList)) {
//            return;
//        }
//        // 将 responseList 按 startTime 的日期进行分组
//        Map<String, List<CompanyResponseDTO>> companyMap = responseList.stream().collect(Collectors.groupingBy(CompanyResponseDTO::getCompany));
//
//        // 获取用户信息
//        List<String> userIdList = responseList.stream().map(CompanyResponseDTO::getUserId).distinct().collect(Collectors.toList());
//        LambdaQueryWrapper<TUser> userWrapper = new LambdaQueryWrapper<>();
//        userWrapper.in(TUser::getId, userIdList);
//        List<TUser> userList = tUserMapper.selectList(userWrapper);
//        Map<String, TUser> userByIdMap = userList.stream().collect(Collectors.toMap(TUser::getId, user -> user));
//
//        // 查询公司信息
//        LambdaQueryWrapper<Company> companyWrapper = new LambdaQueryWrapper<>();
//        companyWrapper.in(Company::getCompanyName, companyMap.keySet());
//        List<Company> companyList = companyMapper.selectList(companyWrapper);
//        Map<String, Integer> enterpriseMap = companyList.stream().collect(Collectors.toMap(Company::getCompanyName, Company::getCompanyId));
//
//        List<TUseStatistics> useList = Lists.newArrayList();
//        for (String company : companyMap.keySet()) {
//            Map<String, List<CompanyResponseDTO>> dateMap = companyMap.get(company).stream().
//                    collect(Collectors.groupingBy(response -> DateTimeString.DATE.toString(response.getStartTime())));
//            for (String date : dateMap.keySet()) {
//                // 将每天的 responseList 按用户分组
//                Map<String, List<CompanyResponseDTO>> userMap = dateMap.get(date).stream().collect(Collectors.groupingBy(CompanyResponseDTO::getUserId));
//                for (String userId : userMap.keySet()) {
//                    // 将用户那天的所有答卷查出来，重新进行统计计算
//                    List<CompanyResponseDTO> responseByDateAndUserList = userMap.get(userId);
//                    if (CollectionUtils.isEmpty(responseByDateAndUserList)) {
//                        continue;
//                    }
//                    List<String> responseIdList = responseByDateAndUserList.stream().map(CompanyResponseDTO::getResponseId).collect(Collectors.toList());
//                    // 将答卷按门店分组
//                    Map<String, List<CompanyResponseDTO>> storeMap = responseByDateAndUserList.stream().collect(Collectors.groupingBy(CompanyResponseDTO::getStoreId));
//                    // 根据开始时间排序
//                    List<CompanyResponseDTO> startList = responseByDateAndUserList.stream().sorted(Comparator.comparing(CompanyResponseDTO::getStartTime)).collect(Collectors.toList());
//                    // 根据结束时间排序
//                    List<CompanyResponseDTO> endList = responseByDateAndUserList.stream().filter(response -> {
//                        // 过滤结束时间不是今天的
//                        if (response.getEndTime().compareTo(DateTimeString.DATE_TIME.toDateTime(date + " 23:59:59")) <= 0) {
//                            return true;
//                        } else {
//                            return false;
//                        }
//                    }).filter(response -> {
//                        // 过滤开始时间不是今天的
//                        if (response.getStartTime().compareTo(DateTimeString.DATE_TIME.toDateTime(date + " 00:00:00")) >= 0) {
//                            return true;
//                        } else {
//                            return false;
//                        }
//                    }).sorted(Comparator.comparing(CompanyResponseDTO::getEndTime)).collect(Collectors.toList());
//                    // 查询这些答卷的答案
//                    LambdaQueryWrapper<TAnswer> answerWrapper = new LambdaQueryWrapper<>();
//                    answerWrapper.in(TAnswer::getResponseId, responseIdList);
//                    List<TAnswer> answerList = tAnswerMapper.selectList(answerWrapper);
//                    // 计算共拍摄了多少张图片
//                    Optional<Integer> imageNum = answerList.stream()
//                            .filter(answer -> StringUtils.isNotBlank(answer.getImage()))
//                            .map(TAnswer::getImage)
//                            .map(image -> image.split(";"))
//                            .map(list -> list.length)
//                            .reduce((a, b) -> a + b);
//                    // 查询用户姓名
//                    String realName = argusCompanyService.getRealName(userByIdMap.get(userId).getPhone(), enterpriseMap.get(company));
//                    // 组装结果
//                    TUseStatistics analysis = new TUseStatistics();
//                    analysis.setDate(DateTimeString.DATE_TIME.toDateTime(date + " 00:00:00"));
//                    analysis.setUserId(userId);
//                    analysis.setAccount(userByIdMap.get(userId).getPhone());
//                    analysis.setUserName(StringUtils.isNoneBlank(realName) ? realName : userByIdMap.get(userId).getPhone());
//                    analysis.setEnterpriseId(String.valueOf(enterpriseMap.get(company)));
//                    analysis.setVisitNum(responseByDateAndUserList.size());
//                    analysis.setVisitStoreNum(storeMap.keySet().size());
//                    analysis.setStartTime(startList.get(0).getStartTime());
//                    if (CollectionUtils.isNotEmpty(endList)) {
//                        analysis.setEndTime(endList.get(endList.size() - 1).getEndTime());
//                    }
//                    if (responseByDateAndUserList.size() > 1) {
//                        analysis.setOnRoadTime(getOnRoadTime(startList));
//                    } else {
//                        analysis.setOnRoadTime(0);
//                    }
//                    analysis.setPictureNum(imageNum.orElse(0));
//                    analysis.setResponseNum(responseIdList.size());
//                    useList.add(analysis);
//                }
//            }
//        }
//        List<List<TUseStatistics>> partition = Lists.partition(useList, 100);
//        for (List<TUseStatistics> tUseStatistics : partition) {
//            useStatisticsMapper.batchInsert(tUseStatistics);
//        }
//    }
//
//    @Override
//    public ResponseData getOperateStatistics(GetStatisticsReqDTO reqDTO) {
//        String startTime = reqDTO.getStartDate() + " 00:00:00";
//        String endTime = reqDTO.getEndDate() + " 23:59:59";
//        List<UserUseAppDayDTO> logList = operateLogMapper.getUserUseAppDays(startTime, endTime, reqDTO.getCompanyId());
//        Map<String, List<UserUseAppDayDTO>> userMap = logList.stream().collect(Collectors.groupingBy(UserUseAppDayDTO::getUserId));
//        // 过滤使用超过五天的用户
//        Integer longerFiveDaysUserNum = userMap.keySet().stream().filter(log -> userMap.get(log).size() >= 5).collect(Collectors.toList()).size();
//        // 获取拜统计访记录
//        LambdaQueryWrapper<TUseStatistics> useWrapper = new LambdaQueryWrapper<>();
//        useWrapper.eq(TUseStatistics::getEnterpriseId, reqDTO.getCompanyId())
//                .gt(TUseStatistics::getDate, startTime)
//                .le(TUseStatistics::getDate, endTime);
//        List<TUseStatistics> analysisList = useStatisticsMapper.selectList(useWrapper);
//        int storeExecuteUserNum = 0;
//        int totalResponseNum = 0;
//        double userVisitStoreNum = 0;
//        if (CollectionUtils.isNotEmpty(analysisList)) {
//            // 计算门店执行用户数
//            storeExecuteUserNum = (int) analysisList.stream().map(TUseStatistics::getUserId).distinct().count();
//            // 计算人均拜访门店数
//            totalResponseNum = analysisList.stream().map(TUseStatistics::getResponseNum).reduce(Integer::sum).get();
//            userVisitStoreNum = new BigDecimal(totalResponseNum).divide(new BigDecimal(storeExecuteUserNum), 2, BigDecimal.ROUND_DOWN).doubleValue();
//        }
//
//        OperateStatisticDTO result = new OperateStatisticDTO();
//        result.setUseAppUserNum(userMap.keySet().size());
//        result.setLongerFiveDaysUserNum(longerFiveDaysUserNum);
//        result.setStoreExecuteUserNum(storeExecuteUserNum);
//        result.setUserVisitStoreNum(userVisitStoreNum);
//        return ResponseData.success(result);
//    }
//
//    @Override
//    public ResponseData exportOperateStatistics(String startDate, String endDate, String companyId, HttpServletResponse response) {
//        // 获取公司名称 拼接导出文件名
//        LambdaQueryWrapper<Company> companyWrapper = new LambdaQueryWrapper<>();
//        companyWrapper.eq(Company::getCompanyId, companyId);
//        Company company = companyMapper.selectOne(companyWrapper);
//        LocalDateTime date = DateTimeString.DATE_TIME.toDateTime(startDate);
//        String fileName = date.getYear() + "年" + date.getMonthValue() + "月" + company.getCompanyName() + "APP使用明细";
//        String sheetName = "sheet1";
//
//        // 查询企业所有用户
//        List<CompanyUserDTO> companyUserDTOList = argusCompanyDao.getCompanyUserList(companyId);
//
//        // 用户使用记录
//        List<UserUseAppDayDTO> logList = operateLogMapper.getUserUseAppDays(startDate, endDate, companyId);
//        Map<String, List<UserUseAppDayDTO>> logMap = logList.stream().collect(Collectors.groupingBy(UserUseAppDayDTO::getPhone));
//        List<String> userIdList = logList.stream().map(UserUseAppDayDTO::getUserId).collect(Collectors.toList());
//
//        // 查询用户答卷
//        LambdaQueryWrapper<TResponse> responseWrapper = new LambdaQueryWrapper<>();
//        responseWrapper.in(TResponse::getId, userIdList);
//        List<TResponse> responseList = tResponseMapper.selectList(responseWrapper);
//        Map<String, List<TResponse>> userMap = responseList.stream().collect(Collectors.groupingBy(TResponse::getUId));
//
//        // 组装导出结果
//        List<ExportStatisticsDTO> resultList = Lists.newArrayList();
//        for (CompanyUserDTO user : companyUserDTOList) {
//            ExportStatisticsDTO dto = new ExportStatisticsDTO();
//            if (StringUtils.isNotBlank(user.getRealName())) {
//                dto.setAccount(user.getRealName());
//            } else {
//                dto.setAccount(user.getAccount());
//            }
//            if (CollectionUtils.isNotEmpty(logMap.get(user.getAccount()))) {
//                dto.setUseApp("是");
//            }
//            if (CollectionUtils.isNotEmpty(logMap.get(user.getAccount())) && logMap.get(user.getAccount()).size() >= 5) {
//                dto.setLongerFiveDays("是");
//            }
//            if (CollectionUtils.isNotEmpty(logMap.get(user.getAccount())) && CollectionUtils.isNotEmpty(userMap.get(logMap.get(user.getAccount()).get(0).getUserId()))) {
//                dto.setExecuteStore("是");
//            }
//            resultList.add(dto);
//        }
//
//        try {
//            ExcelUtils.exportExcel(resultList, null, sheetName, ExportStatisticsDTO.class, fileName, response);
//        } catch (IOException e) {
//            log.error("exportExcel", e);
//        }
//
//        return null;
//    }
//
//    @Override
//    public PageDataResult getUserUseStatistic(GetStatisticsReqDTO reqDTO) {
//        String startTime = reqDTO.getStartDate();
//        String endTime = reqDTO.getEndDate();
//        PageDataResult pageDataResult = new PageDataResult();
//        // 用户使用记录
//        List<UserUseAppDayDTO> logList = operateLogMapper.getUserUseAppDays(startTime, endTime, reqDTO.getCompanyId());
//        Map<String, List<UserUseAppDayDTO>> logMap = logList.stream().collect(Collectors.groupingBy(UserUseAppDayDTO::getUserId));
//        // 获取需要统计的用户ID
//        List<String> userIdList = getUserIdList(reqDTO, startTime, endTime, pageDataResult);
//
//        if (CollectionUtils.isEmpty(userIdList)) {
//            return pageDataResult;
//        }
//
//        LambdaQueryWrapper<TUseStatistics> statisticsWrapper = new LambdaQueryWrapper<>();
//        statisticsWrapper.in(TUseStatistics::getUserId, userIdList)
//                .gt(TUseStatistics::getDate, startTime)
//                .le(TUseStatistics::getDate, endTime);
//        List<TUseStatistics> analysisList = useStatisticsMapper.selectList(statisticsWrapper);
//        if (CollectionUtils.isEmpty(analysisList)) {
//            return pageDataResult;
//        }
//        // 将统计记录按人分组
//        Map<String, List<TUseStatistics>> userMap = analysisList.stream().collect(Collectors.groupingBy(TUseStatistics::getUserId));
//
//        // 获取访问门店数
//        List<UserVisitStoreNumDTO> visitStoreDTOList = tResponseMapper.getUserVisitStoreNum(userMap.keySet(), startTime, endTime);
//        Map<String, List<UserVisitStoreNumDTO>> userStoreMap = visitStoreDTOList.stream().collect(Collectors.groupingBy(UserVisitStoreNumDTO::getUserId));
//
//        List<UserUseStatisticsDTO> resultList = Lists.newArrayList();
//        for (String userId : userMap.keySet()) {
//            // 拜访门店总数
//            int visitSoreNum = 0;
//            if (CollectionUtils.isNotEmpty(userStoreMap.get(userId))) {
//                visitSoreNum = userStoreMap.get(userId).size();
//            }
//            // 日均拜访门店数
//            double dayAverageVisitStoreNum = 0;
//            if (visitSoreNum > 0) {
//                dayAverageVisitStoreNum = new BigDecimal(visitSoreNum).divide(new BigDecimal(userMap.get(userId).size()), 2, BigDecimal.ROUND_DOWN).doubleValue();
//            }
//            /**
//             * 日均开始拜访时间和日均结束拜访时间
//             * 1.计算总的分钟数
//             * 2.平均小时=（总的分钟数/天数）/ 60 取整
//             * 2.平均小时=（总的分钟数/天数）% 60 取余
//             */
//            int totalStartMinutes = 0;
//            int totalEndMinutes = 0;
//            int haveEndDays = 0;
//            for (TUseStatistics entity : userMap.get(userId)) {
//                totalStartMinutes += entity.getStartTime().getHour() * 60 + entity.getStartTime().getMinute();
//                if (!Objects.isNull(entity.getEndTime())) {
//                    totalEndMinutes += entity.getEndTime().getHour() * 60 + entity.getEndTime().getMinute();
//                    haveEndDays += 1;
//                }
//            }
//            int startHour = (totalStartMinutes / userMap.get(userId).size()) / 60;
//            int startMinutes = (totalStartMinutes / userMap.get(userId).size()) % 60;
//            int endHour = 0;
//            int endMinutes = 0;
//            if (haveEndDays != 0) {
//                endHour = (totalEndMinutes / haveEndDays) / 60;
//                endMinutes = (totalEndMinutes / haveEndDays) % 60;
//            }
//
//
//            // 日均在途时间
//            Integer totalOnRoadTime = userMap.get(userId).stream().map(TUseStatistics::getOnRoadTime).reduce(Integer::sum).get();
//            Double dayAverageOnRoadTime = new BigDecimal(totalOnRoadTime).divide(new BigDecimal(userMap.get(userId).size()), 2, BigDecimal.ROUND_DOWN).doubleValue();
//
//            Integer totalPicNum = userMap.get(userId).stream().map(TUseStatistics::getPictureNum).reduce(Integer::sum).get();
//            Double averagePicNum = new BigDecimal(totalPicNum).divide(new BigDecimal(userMap.get(userId).size()), 2, BigDecimal.ROUND_DOWN).doubleValue();
//
//            UserUseStatisticsDTO dto = new UserUseStatisticsDTO();
//            dto.setUserId(userId);
//            dto.setAccount(userMap.get(userId).get(0).getAccount());
//            dto.setRealName(userMap.get(userId).get(0).getUserName());
//            if (CollectionUtils.isNotEmpty(logMap.get(userId))) {
//                dto.setUseAppDays(logMap.get(userId).size());
//            } else {
//                dto.setUseAppDays(0);
//            }
//            dto.setVisitStoreDays(userMap.get(userId).size());
//            dto.setVisitStoreNum(visitSoreNum);
//            dto.setDayAverageVisitStoreNum(dayAverageVisitStoreNum);
//            dto.setDayAverageStartTime(startHour + ":" + startMinutes);
//            dto.setDayAverageEndTime(endHour + ":" + endMinutes);
//            dto.setDayAverageOnRoadTime(dayAverageOnRoadTime);
//            dto.setPicNum(totalPicNum);
//            dto.setAverageVisitPicNum(averagePicNum);
//            resultList.add(dto);
//        }
//        pageDataResult.setList(resultList);
//        return pageDataResult;
//    }
//
//    private List<String> getUserIdList(GetStatisticsReqDTO reqDTO, String startTime, String endTime, PageDataResult pageDataResult) {
//        List<String> userIdList;
//        if (StringUtils.isBlank(reqDTO.getUserId())) {
//            IPage<TUseStatistics> page = new Page<>(reqDTO.getPageNo(), reqDTO.getPageSize());
//            LambdaQueryWrapper<TUseStatistics> useWrapper = new LambdaQueryWrapper<>();
//            useWrapper.eq(TUseStatistics::getEnterpriseId, reqDTO.getCompanyId())
//                    .gt(TUseStatistics::getDate, startTime)
//                    .le(TUseStatistics::getDate, endTime);
//            if (StringUtils.isNotBlank(reqDTO.getUserName())) {
//                useWrapper.like(TUseStatistics::getUserName, reqDTO.getUserName());
//            }
//            useWrapper.groupBy(TUseStatistics::getUserId);
//            Integer count = useStatisticsMapper.selectList(useWrapper).size();
//            IPage<TUseStatistics> usePage = useStatisticsMapper.selectPage(page, useWrapper);
//            pageDataResult.setTotals(count);
//            // 获取拜统计访记录
//            userIdList = usePage.getRecords().stream().map(TUseStatistics::getUserId).collect(Collectors.toList());
//        } else {
//            userIdList = Arrays.asList(reqDTO.getUserId());
//            pageDataResult.setTotals(1);
//        }
//        return userIdList;
//    }
//
//    @Override
//    public ResponseData getStoreList(GetStatisticsReqDTO reqDTO) {
//        String startTime = reqDTO.getStartDate();
//        String endTime = reqDTO.getEndDate();
//        List<StoreListDTO> result = tResponseMapper.getStoreList(reqDTO.getUserId(), startTime, endTime);
//        return ResponseData.success(result);
//    }
//
//    @Override
//    public ResponseData getUserResponseList(GetStatisticsReqDTO reqDTO) {
//        List<UserResponseDTO> userResponseDTOList = Lists.newArrayList();
//        String startTime = reqDTO.getStartDate();
//        String endTime = reqDTO.getEndDate();
//        List<UserResponseListDTO> result = tResponseMapper.getUserResponseList(reqDTO.getUserId(), startTime, endTime, reqDTO.getStoreId());
//        if (CollectionUtils.isEmpty(result)) {
//            return ResponseData.success(userResponseDTOList);
//        }
//
//        List<String> responseIdList = result.stream().map(UserResponseListDTO::getResponseId).distinct().collect(Collectors.toList());
//        LambdaQueryWrapper<TAnswer> answerWrapper = new LambdaQueryWrapper<>();
//        answerWrapper.in(TAnswer::getResponseId, responseIdList);
//        List<TAnswer> answerList = tAnswerMapper.selectList(answerWrapper);
//        Map<String, List<TAnswer>> answerMap = answerList.stream().collect(Collectors.groupingBy(TAnswer::getResponseId));
//
//        for (UserResponseListDTO userResponseListDTO : result) {
//            if (CollectionUtils.isEmpty(answerMap.get(userResponseListDTO.getResponseId()))) {
//                continue;
//            }
//            Optional<Integer> reduce = answerMap.get(userResponseListDTO.getResponseId()).stream()
//                    .filter(answer -> StringUtils.isNotBlank(answer.getImage()))
//                    .map(TAnswer::getImage)
//                    .map(image -> image.split(";"))
//                    .map(list -> list.length)
//                    .reduce((a, b) -> a + b);
//            userResponseListDTO.setPicNum(reduce.orElse(0));
//            userResponseListDTO.setExecuteTime(DateTimeString.TIME.toString(userResponseListDTO.getExecuteDateTime()));
//            userResponseListDTO.setEndTime(DateTimeString.TIME.toString(userResponseListDTO.getEndDateTime()));
//            userResponseListDTO.setEndDateTime(null);
//            userResponseListDTO.setUpLoadTime(DateTimeString.TIME.toString(userResponseListDTO.getUpLoadDateTime()));
//            userResponseListDTO.setUpLoadDateTime(null);
//        }
//        Map<String, List<UserResponseListDTO>> dateMap = result.stream().collect(Collectors.groupingBy(dto -> DateTimeString.DATE.toString(dto.getExecuteDateTime())));
//
//        for (String date : dateMap.keySet()) {
//            UserResponseDTO dto = new UserResponseDTO();
//            dto.setDate(date);
//            dto.setResponseListDTOList(dateMap.get(date));
//            userResponseDTOList.add(dto);
//        }
//        List<UserResponseDTO> resultList = userResponseDTOList.stream().sorted(Comparator.comparing(o -> DateTimeString.DATE.toDateTime(o.getDate()))).collect(Collectors.toList());
//        return ResponseData.success(resultList);
//    }
//
//    /**
//     * 计算在途时长
//     *
//     * @param responseList
//     * @return
//     */
//    private Integer getOnRoadTime(List<CompanyResponseDTO> responseList) {
//        Integer totalSeconds = 0;
//        for (int i = 1; i < responseList.size(); i++) {
//            if (responseList.get(i).getStartTime().compareTo(responseList.get(i - 1).getEndTime()) <= 0) {
//                continue;
//            }
//            totalSeconds += DateTool.differentSeconds(responseList.get(i).getStartTime(), responseList.get(i - 1).getEndTime());
//        }
//        return totalSeconds;
//    }
//}
