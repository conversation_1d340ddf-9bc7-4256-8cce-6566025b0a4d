package com.lenztech.bi.enterprise.dto.gsk;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lv
 */
@Data
public class GskPosmDto {

    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 门店类型
     */
    private String storeType;
    /**
     * 上传时间
     */
    private String uploadTime;
    /**
     * posm 是否达标
     */
    private String upToStandard;
    /**
     * 一个Rid 一个门店 返回Rid 前端传回来
     */
    private String responseId;
    /**
     * response表 taskId 用来做动态查询
     */
    private String taskId;

    /**
     * 任务ID
     */
    private String taskIdOwner;
}
