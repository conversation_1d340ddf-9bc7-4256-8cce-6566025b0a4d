package com.lenztech.bi.enterprise.validator.annotation;

import com.lenztech.bi.enterprise.validator.NotNullArrayValidatorImpl;
import com.lenztech.bi.enterprise.validator.NotNullValidatorImpl;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {
        NotNullValidatorImpl.class,
        NotNullArrayValidatorImpl.class
})
public @interface NotNull {

    String message() default "不能为空";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
