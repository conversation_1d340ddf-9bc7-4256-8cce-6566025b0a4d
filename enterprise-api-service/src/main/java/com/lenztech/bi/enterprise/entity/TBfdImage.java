package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TBfdImage implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String responseId;

    /**
     * 图片ID
     */
    private String imageId;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 识别图URL
     */
    private String recUrl;

    /**
     * 图片长(高)
     */
    private Integer imageHeight;

    /**
     * 图片宽
     */
    private Integer imageWidth;

    /**
     * 是否翻拍 1是 0否
     */
    private Integer isRemake;

    /**
     * 翻拍分值
     */
    private Double remakeScore;

    /**
     * 识别出来的sku数量
     */
    private Integer numPatches;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
