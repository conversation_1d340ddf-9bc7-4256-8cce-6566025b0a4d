package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;

import java.time.LocalDateTime;

/**
 * <p>
 * 任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-28
 */
public class TTask extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 任务id
     */
    private String id;

    /**
     * 任务名称
     */
    private String taskname;

    /**
     * 谁创建的
     */
    private String ownerId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除时间
     */
    private LocalDateTime deleteTime;

    /**
     * 任务状态 0编辑中 1执行中 2已下线
     */
    private String status;

    /**
     * 描述
     */
    private String description;

    /**
     * 0默认 1快消 2汽车 3服装 4医药 5烟酒 6家电
     */
    private String taskType;

    /**
     * 任务奖励
     */
    private Double reward;

    /**
     * 样本城市要求
     */
    private String sampleCity;

    /**
     * 样本年龄
     */
    private String sampleBirthday;

    /**
     * 样本性别
     */
    private String sampleSex;

    /**
     * 执行任务时是否要求会员提交申请
     */
    private String applyStatus;

    /**
     * 执行任务完成理由（文字描述）
     */
    private String reason1;

    /**
     * 执行任务完成时间（单选）
     */
    private String reason2;

    /**
     * 项目结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否有任务地点要求
     */
    private String addressStatus;

    /**
     * 发布时间
     */
    private LocalDateTime applitionTime;

    /**
     * 任务数量
     */
    private Integer taskcount;

    /**
     * 0 不需要   1 需要
     */
    @TableField("gpsStatus")
    private String gpsStatus;

    /**
     * 是否删除，0 未删除 1 删除 2彻底删除
     */
    @TableField("deleteStatus")
    private String deleteStatus;

    /**
     * 是否能返回上一页，0 不可以 1 可以
     */
    private String upstatus;

    /**
     * 任务允许的最大执行距离
     */
    @TableField("performRange")
    private Integer performRange;

    /**
     * 每个人最多可以执行的次数
     */
    private String vipExecuteNum;

    /**
     * 0：分配所有人；2：按任务分配；3：按网点分配；4：按属性分配
     */
    private String taskdistStatus;

    /**
     * 针对发布价钱为0元的项目发布状态，此任务需要ppz人员审核【默认0，未审核；1已审核 3待修】
     */
    @TableField("publishStatus")
    private String publishStatus;

    /**
     * 上传照片压缩比例控制 超清0，高清1，标清2，不压缩3，真原图4
     */
    @TableField("CompressionPIC")
    private String CompressionPIC;

    /**
     * 在无地址的情况下，是否记录gps的值
     */
    @TableField("gpsValueStatus")
    private String gpsValueStatus;

    /**
     * 任务排序标准
     */
    private Double orderNum;

    /**
     * 是否需要申请
     */
    private String applyed;

    /**
     * 任务开始时间
     */
    private LocalDateTime starTime;

    /**
     * 预定开始时间
     */
    private LocalDateTime applyTime;

    @TableField("rangeV2")
    private String rangeV2;

    @TableField("ExecutionTimes")
    private String ExecutionTimes;

    /**
     * 地址是否有个性化设置：0-无。1-有
     */
    @TableField("isDIY")
    private String isDIY;

    private String isPreview;

    /**
     * 强制审核时间
     */
    private Integer examinedays;

    /**
     * 任务点击次数
     */
    private Integer pcount;

    /**
     * 任务手续费
     */
    private Double percent;

    /**
     * 是否自动审核 0否 1是
     */
    private String iszdsh;

    /**
     * 是否审核中 0否 1是
     */
    private String isshz;

    /**
     * 申诉后企业会员不同意提交admin处理的个数
     */
    private Integer ssadmincount;

    private String ismark;

    /**
     * 0默认 1宅在家 2跑出去 3帮推广
     */
    private String tasktype;

    private String temp1;

    private String temp2;

    private String temp3;

    /**
     * 任务图标路径
     */
    private String imgpath;

    /**
     * 下载方式
     */
    private String downloadfs;

    /**
     * 1:给 0:不给 ,默认给
     */
    private String tjf;

    /**
     * 尾单价格
     */
    private Double wdReward;

    /**
     * 自动审核协议 0不同意 1同意
     */
    private String zdshsz;

    /**
     * 0不是sku 1是sku
     */
    private String issku;

    /**
     * 问卷导出类型 0:按response  1:按行  2:按列  3:安门店业态
     */
    private String exporttype;

    /**
     * 任务达标分值
     */
    private Double standardscore;

    /**
     * 任务未达标后是否允许重新回答 0运行 1不允许
     */
    private String candoagain;

    /**
     * 任务达标后的反馈信息
     */
    private String successtext;

    /**
     * 任务达标后的反馈信息
     */
    private String failedtext;

    /**
     * 分配给组的id
     */
    private String groupid;

    /**
     * 任务图片路径
     */
    private String imageurl;

    private String booknameId;

    /**
     * 是否与其他项目一起参与动态定价 1是 0否
     */
    private Integer allmovingprice;

    /**
     * 是否添加省市文件夹 0默认不添加 1添加
     */
    private String provincecity;

    /**
     * 有地址任务是否只显示地址编号文件夹
     */
    private String numberonly;

    private String tableaushow;

    /**
     * 模糊：0不需要 1需要
     */
    private String ifblur;

    /**
     * 允许距离商店最大距离
     */
    private Integer shopdistance;

    /**
     * 允许图片最大模糊度
     */
    private Double picvaguedegree;

    /**
     * 是否打包 1打包 0没打包
     */
    private Integer isdabao;

    private String iffenqu;

    /**
     * 1组内可见,2组内定时可见, 0组内不可见
     */
    @TableField("ifVisible")
    private String ifVisible;

    @TableField("visible_startTime")
    private LocalDateTime visibleStarttime;

    @TableField("visible_endTime")
    private LocalDateTime visibleEndtime;

    /**
     * 是否运行稍后提交 1默认允许 0不允许
     */
    @TableField("uploadLater")
    private String uploadLater;

    /**
     * 是不是多任务打包  1是 0不是
     */
    private Integer ismuldabao;

    /**
     * 是否预上线 0否 1是
     */
    private String isysx;

    /**
     * 正式上线时间
     */
    private LocalDateTime zssxTime;

    /**
     * 任务在APP端的模糊阀值判断  如50_1500 表示安卓50  苹果1500
     */
    private String fuzzyValve;

    /**
     * 任务在APP端的倾斜角判断  如50_1500表示安卓50  苹果1500
     */
    private String obliqueValve;

    /**
     * 是否可做 0不可做 1可做
     */
    @TableField("ifCando")
    private String ifCando;

    /**
     * 校验地址底库  0不校验  1校验
     */
    private String checkAddressDepositary;

    /**
     * 地址间隔时间执行任务 0不是 1是
     */
    private String intervalAddress;

    /**
     * 企业版2.0所用企业名称 如gsk、bayer
     */
    private String enterpriseEdition;

    /**
     * 是否跳转图像识别页面 0否 1是
     */
    @TableField("ifImageRecognize")
    private String ifImageRecognize;

    /**
     * 预定任务-完成任务期限（单位天）
     */
    private Integer durationTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTaskname() {
        return taskname;
    }

    public void setTaskname(String taskname) {
        this.taskname = taskname;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(LocalDateTime deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public Double getReward() {
        return reward;
    }

    public void setReward(Double reward) {
        this.reward = reward;
    }

    public String getSampleCity() {
        return sampleCity;
    }

    public void setSampleCity(String sampleCity) {
        this.sampleCity = sampleCity;
    }

    public String getSampleBirthday() {
        return sampleBirthday;
    }

    public void setSampleBirthday(String sampleBirthday) {
        this.sampleBirthday = sampleBirthday;
    }

    public String getSampleSex() {
        return sampleSex;
    }

    public void setSampleSex(String sampleSex) {
        this.sampleSex = sampleSex;
    }

    public String getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(String applyStatus) {
        this.applyStatus = applyStatus;
    }

    public String getReason1() {
        return reason1;
    }

    public void setReason1(String reason1) {
        this.reason1 = reason1;
    }

    public String getReason2() {
        return reason2;
    }

    public void setReason2(String reason2) {
        this.reason2 = reason2;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getAddressStatus() {
        return addressStatus;
    }

    public void setAddressStatus(String addressStatus) {
        this.addressStatus = addressStatus;
    }

    public LocalDateTime getApplitionTime() {
        return applitionTime;
    }

    public void setApplitionTime(LocalDateTime applitionTime) {
        this.applitionTime = applitionTime;
    }

    public Integer getTaskcount() {
        return taskcount;
    }

    public void setTaskcount(Integer taskcount) {
        this.taskcount = taskcount;
    }

    public String getGpsStatus() {
        return gpsStatus;
    }

    public void setGpsStatus(String gpsStatus) {
        this.gpsStatus = gpsStatus;
    }

    public String getDeleteStatus() {
        return deleteStatus;
    }

    public void setDeleteStatus(String deleteStatus) {
        this.deleteStatus = deleteStatus;
    }

    public String getUpstatus() {
        return upstatus;
    }

    public void setUpstatus(String upstatus) {
        this.upstatus = upstatus;
    }

    public Integer getPerformRange() {
        return performRange;
    }

    public void setPerformRange(Integer performRange) {
        this.performRange = performRange;
    }

    public String getVipExecuteNum() {
        return vipExecuteNum;
    }

    public void setVipExecuteNum(String vipExecuteNum) {
        this.vipExecuteNum = vipExecuteNum;
    }

    public String getTaskdistStatus() {
        return taskdistStatus;
    }

    public void setTaskdistStatus(String taskdistStatus) {
        this.taskdistStatus = taskdistStatus;
    }

    public String getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(String publishStatus) {
        this.publishStatus = publishStatus;
    }

    public String getCompressionPIC() {
        return CompressionPIC;
    }

    public void setCompressionPIC(String compressionPIC) {
        CompressionPIC = compressionPIC;
    }

    public String getGpsValueStatus() {
        return gpsValueStatus;
    }

    public void setGpsValueStatus(String gpsValueStatus) {
        this.gpsValueStatus = gpsValueStatus;
    }

    public Double getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Double orderNum) {
        this.orderNum = orderNum;
    }

    public String getApplyed() {
        return applyed;
    }

    public void setApplyed(String applyed) {
        this.applyed = applyed;
    }

    public LocalDateTime getStarTime() {
        return starTime;
    }

    public void setStarTime(LocalDateTime starTime) {
        this.starTime = starTime;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public String getRangeV2() {
        return rangeV2;
    }

    public void setRangeV2(String rangeV2) {
        this.rangeV2 = rangeV2;
    }

    public String getExecutionTimes() {
        return ExecutionTimes;
    }

    public void setExecutionTimes(String executionTimes) {
        ExecutionTimes = executionTimes;
    }

    public String getIsDIY() {
        return isDIY;
    }

    public void setIsDIY(String isDIY) {
        this.isDIY = isDIY;
    }

    public String getIsPreview() {
        return isPreview;
    }

    public void setIsPreview(String isPreview) {
        this.isPreview = isPreview;
    }

    public Integer getExaminedays() {
        return examinedays;
    }

    public void setExaminedays(Integer examinedays) {
        this.examinedays = examinedays;
    }

    public Integer getPcount() {
        return pcount;
    }

    public void setPcount(Integer pcount) {
        this.pcount = pcount;
    }

    public Double getPercent() {
        return percent;
    }

    public void setPercent(Double percent) {
        this.percent = percent;
    }

    public String getIszdsh() {
        return iszdsh;
    }

    public void setIszdsh(String iszdsh) {
        this.iszdsh = iszdsh;
    }

    public String getIsshz() {
        return isshz;
    }

    public void setIsshz(String isshz) {
        this.isshz = isshz;
    }

    public Integer getSsadmincount() {
        return ssadmincount;
    }

    public void setSsadmincount(Integer ssadmincount) {
        this.ssadmincount = ssadmincount;
    }

    public String getIsmark() {
        return ismark;
    }

    public void setIsmark(String ismark) {
        this.ismark = ismark;
    }

    public String getTasktype() {
        return tasktype;
    }

    public void setTasktype(String tasktype) {
        this.tasktype = tasktype;
    }

    public String getTemp1() {
        return temp1;
    }

    public void setTemp1(String temp1) {
        this.temp1 = temp1;
    }

    public String getTemp2() {
        return temp2;
    }

    public void setTemp2(String temp2) {
        this.temp2 = temp2;
    }

    public String getTemp3() {
        return temp3;
    }

    public void setTemp3(String temp3) {
        this.temp3 = temp3;
    }

    public String getImgpath() {
        return imgpath;
    }

    public void setImgpath(String imgpath) {
        this.imgpath = imgpath;
    }

    public String getDownloadfs() {
        return downloadfs;
    }

    public void setDownloadfs(String downloadfs) {
        this.downloadfs = downloadfs;
    }

    public String getTjf() {
        return tjf;
    }

    public void setTjf(String tjf) {
        this.tjf = tjf;
    }

    public Double getWdReward() {
        return wdReward;
    }

    public void setWdReward(Double wdReward) {
        this.wdReward = wdReward;
    }

    public String getZdshsz() {
        return zdshsz;
    }

    public void setZdshsz(String zdshsz) {
        this.zdshsz = zdshsz;
    }

    public String getIssku() {
        return issku;
    }

    public void setIssku(String issku) {
        this.issku = issku;
    }

    public String getExporttype() {
        return exporttype;
    }

    public void setExporttype(String exporttype) {
        this.exporttype = exporttype;
    }

    public Double getStandardscore() {
        return standardscore;
    }

    public void setStandardscore(Double standardscore) {
        this.standardscore = standardscore;
    }

    public String getCandoagain() {
        return candoagain;
    }

    public void setCandoagain(String candoagain) {
        this.candoagain = candoagain;
    }

    public String getSuccesstext() {
        return successtext;
    }

    public void setSuccesstext(String successtext) {
        this.successtext = successtext;
    }

    public String getFailedtext() {
        return failedtext;
    }

    public void setFailedtext(String failedtext) {
        this.failedtext = failedtext;
    }

    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

    public String getImageurl() {
        return imageurl;
    }

    public void setImageurl(String imageurl) {
        this.imageurl = imageurl;
    }

    public String getBooknameId() {
        return booknameId;
    }

    public void setBooknameId(String booknameId) {
        this.booknameId = booknameId;
    }

    public Integer getAllmovingprice() {
        return allmovingprice;
    }

    public void setAllmovingprice(Integer allmovingprice) {
        this.allmovingprice = allmovingprice;
    }

    public String getProvincecity() {
        return provincecity;
    }

    public void setProvincecity(String provincecity) {
        this.provincecity = provincecity;
    }

    public String getNumberonly() {
        return numberonly;
    }

    public void setNumberonly(String numberonly) {
        this.numberonly = numberonly;
    }

    public String getTableaushow() {
        return tableaushow;
    }

    public void setTableaushow(String tableaushow) {
        this.tableaushow = tableaushow;
    }

    public String getIfblur() {
        return ifblur;
    }

    public void setIfblur(String ifblur) {
        this.ifblur = ifblur;
    }

    public Integer getShopdistance() {
        return shopdistance;
    }

    public void setShopdistance(Integer shopdistance) {
        this.shopdistance = shopdistance;
    }

    public Double getPicvaguedegree() {
        return picvaguedegree;
    }

    public void setPicvaguedegree(Double picvaguedegree) {
        this.picvaguedegree = picvaguedegree;
    }

    public Integer getIsdabao() {
        return isdabao;
    }

    public void setIsdabao(Integer isdabao) {
        this.isdabao = isdabao;
    }

    public String getIffenqu() {
        return iffenqu;
    }

    public void setIffenqu(String iffenqu) {
        this.iffenqu = iffenqu;
    }

    public String getIfVisible() {
        return ifVisible;
    }

    public void setIfVisible(String ifVisible) {
        this.ifVisible = ifVisible;
    }

    public LocalDateTime getVisibleStarttime() {
        return visibleStarttime;
    }

    public void setVisibleStarttime(LocalDateTime visibleStarttime) {
        this.visibleStarttime = visibleStarttime;
    }

    public LocalDateTime getVisibleEndtime() {
        return visibleEndtime;
    }

    public void setVisibleEndtime(LocalDateTime visibleEndtime) {
        this.visibleEndtime = visibleEndtime;
    }

    public String getUploadLater() {
        return uploadLater;
    }

    public void setUploadLater(String uploadLater) {
        this.uploadLater = uploadLater;
    }

    public Integer getIsmuldabao() {
        return ismuldabao;
    }

    public void setIsmuldabao(Integer ismuldabao) {
        this.ismuldabao = ismuldabao;
    }

    public String getIsysx() {
        return isysx;
    }

    public void setIsysx(String isysx) {
        this.isysx = isysx;
    }

    public LocalDateTime getZssxTime() {
        return zssxTime;
    }

    public void setZssxTime(LocalDateTime zssxTime) {
        this.zssxTime = zssxTime;
    }

    public String getFuzzyValve() {
        return fuzzyValve;
    }

    public void setFuzzyValve(String fuzzyValve) {
        this.fuzzyValve = fuzzyValve;
    }

    public String getObliqueValve() {
        return obliqueValve;
    }

    public void setObliqueValve(String obliqueValve) {
        this.obliqueValve = obliqueValve;
    }

    public String getIfCando() {
        return ifCando;
    }

    public void setIfCando(String ifCando) {
        this.ifCando = ifCando;
    }

    public String getCheckAddressDepositary() {
        return checkAddressDepositary;
    }

    public void setCheckAddressDepositary(String checkAddressDepositary) {
        this.checkAddressDepositary = checkAddressDepositary;
    }

    public String getIntervalAddress() {
        return intervalAddress;
    }

    public void setIntervalAddress(String intervalAddress) {
        this.intervalAddress = intervalAddress;
    }

    public String getEnterpriseEdition() {
        return enterpriseEdition;
    }

    public void setEnterpriseEdition(String enterpriseEdition) {
        this.enterpriseEdition = enterpriseEdition;
    }

    public String getIfImageRecognize() {
        return ifImageRecognize;
    }

    public void setIfImageRecognize(String ifImageRecognize) {
        this.ifImageRecognize = ifImageRecognize;
    }

    public Integer getDurationTime() {
        return durationTime;
    }

    public void setDurationTime(Integer durationTime) {
        this.durationTime = durationTime;
    }
}
