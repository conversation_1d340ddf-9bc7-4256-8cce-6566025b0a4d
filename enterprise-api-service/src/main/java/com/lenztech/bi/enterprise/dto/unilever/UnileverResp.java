package com.lenztech.bi.enterprise.dto.unilever;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2021/3/31 20:39
 **/

@Data
public class UnileverResp {
    /**
     * 拜访ID
     */
    private String visitId;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 门店编码
     */
    private String storeCode;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 图片路径
     */
    private List<String> imageList;
    /**
     * OSA
     */
    private List<UnileverOsa> unileverOsaList;
    /**
     * TDP
     */
    private List<UnileverTdp> unileverTdpList;
    /**
     * TDP_DETAIL
     */
    private List<UnileverTdpDetail> unileverTdpDetailList;
}
