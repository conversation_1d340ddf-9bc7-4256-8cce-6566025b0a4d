//package com.lenztech.bi.enterprise.service;
//
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.dto.pg.PgPocBrandFacingListDTO;
//import com.lenztech.bi.enterprise.dto.pg.PgPocRuleListDTO;
//import com.lenztech.bi.enterprise.dto.pg.PgPocSkuExistListDTO;
//import com.lenztech.bi.enterprise.dto.pg.PgPocSkuNotExistListDTO;
//import com.lenztech.bi.enterprise.entity.PgPocKpiEntity;
//
///**
// * <AUTHOR>
// * @version V1.0
// * @date 2019-11-07 15:19
// * @since JDK 1.8
// */
//public interface PgEnterpriseService {
//
//    /**
//     * 查询每个答卷下品牌面位数
//     * @param responseId
//     * @return
//     */
//    ResponseData<PgPocBrandFacingListDTO> listPgPocBrandFacingEntity(String responseId);
//    /**
//     * 查询各个指标
//     * @param responseId
//     * @return
//     */
//    ResponseData<PgPocKpiEntity> selectPgPocKpiEntityByRid(String responseId);
//
//    /**
//     * 查询陈列规则
//     * @param responseId
//     * @return
//     */
//    ResponseData<PgPocRuleListDTO> listPgPocRuleEntity(String responseId);
//
//    /**
//     * 查询有识别结果的sku
//     * @param responseId
//     * @return
//     */
//    ResponseData<PgPocSkuExistListDTO> listPgPocSkuExistEntity(String responseId);
//
//    /**
//     * 查询无分销sku
//     * @param responseId
//     * @return
//     */
//    ResponseData<PgPocSkuNotExistListDTO> listPgPocSkuNotExistEntity(String responseId);
//
//}
