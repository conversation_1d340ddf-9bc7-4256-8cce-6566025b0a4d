package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.dto.ferrero.FerreroBiReport;
import com.lenztech.bi.enterprise.dto.ferrero.FerreroBiTargetResp;
import com.lenztech.bi.enterprise.mapper.FerreroReportMapper;
import com.lenztech.bi.enterprise.service.FerreroReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 费列罗bi指标ServiceImpl
 *
 * <AUTHOR>
 * @date 2021-01-19 13:23:09
 */
@Service
public class FerreroReportServiceImpl implements FerreroReportService {

    public static final Logger logger = LoggerFactory.getLogger(FerreroReportServiceImpl.class);

    @Autowired
    private FerreroReportMapper ferreroReportMapper;

    /**
     * 查询指标集合
     *
     * @param responseId
     * @return FerreroBiTargetResp
     */
    @Override
    public FerreroBiTargetResp getBiTargetList(String responseId) {
        logger.info("responseId:" + responseId);
        FerreroBiTargetResp ferreroBiTargetResp = new FerreroBiTargetResp();
        try {
            List<FerreroBiReport> ferreroBiReportList = ferreroReportMapper.getBiTargetList(responseId);
            ferreroBiTargetResp.setResponseId(responseId);
            ferreroBiTargetResp.setTargetList(ferreroBiReportList);
        } catch (Exception e) {
            logger.error("/getBiTargetList========", e);
        }
        return ferreroBiTargetResp;
    }

}
