package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 魔爪答卷识别结果明细(MonsterAppProductDetail)实体类
 *
 * <AUTHOR>
 * @since 2022-01-17 14:51:28
 */
@Data
public class MonsterAppProductDetail implements Serializable {
    private static final long serialVersionUID = -58302925246657089L;
    
    private Long id;
    /**
    * 答卷id
    */
    private String responseId;
    /**
    * 图片id
    */
    private String imageId;
    /**
    * 商品id
    */
    private String productId;
    /**
    * 商品名
    */
    private String productName;
    /**
    * 客户编码
    */
    private String customCode;
    /**
    * 面位(0.否 1.是)
    */
    private Integer isfaceing;
    /**
    * 层数
    */
    private Integer layer;
    /**
    * 列数
    */
    @TableField("`column`")
    private Integer column;
    /**
     * 去重状态
     */
    private Integer inarge;
    /**
    * 场景
    */
    private String scene;
    /**
    * 坐标字符串
    */
    private String xmin;
    /**
    * 坐标字符串
    */
    private String ymin;
    /**
    * 坐标字符串
    */
    private String xmax;
    /**
    * 坐标字符串
    */
    private String ymax;
    /**
    * 创建时间
    */
    private Date createTime;

}