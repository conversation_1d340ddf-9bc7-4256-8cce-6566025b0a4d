package com.lenztech.bi.enterprise.comon;

/**
 * 南区北区
 * <AUTHOR>
 * @version V1.0
 * @date 2020/04/06 15:45
 * @since JDK 1.8
 */
public enum DaQuEnum {
    /**
     * 北区
     */
    NORTH("NORTH", "北京销售"),
    /**
     * 南区
     */
    SOUTH("SOUTH", "华南营销");

    private String nameEn;
    private String nameCn;

    DaQuEnum(String nameEn, String nameCn) {
        this.nameEn = nameEn;
        this.nameCn = nameCn;
    }

    public String getNameCn() {
        return nameCn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public static String getNameCnByNameEn(String nameEn) {
        for (DaQuEnum areaEnum : DaQuEnum.values()) {
            if (areaEnum.getNameEn().equals(nameEn)) {
                return areaEnum.nameCn;
            }
        }
        return null;
    }

    public static String getNameEnByNameCn(String nameCn) {
        for (DaQuEnum areaEnum : DaQuEnum.values()) {
            if (areaEnum.getNameCn().equals(nameCn)) {
                return areaEnum.nameEn;
            }
        }
        return null;
    }

}
