package com.lenztech.bi.enterprise.entity;

import com.lenztech.bi.enterprise.entity.base.BaseEntity;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-24
 */
public class PgHsmConf extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private String month;

    private String kpiName;

    private String kpiType;

    private String fastGrowing;

    private String hnhb;

    private String barcode;

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }
    public String getKpiName() {
        return kpiName;
    }

    public void setKpiName(String kpiName) {
        this.kpiName = kpiName;
    }
    public String getKpiType() {
        return kpiType;
    }

    public void setKpiType(String kpiType) {
        this.kpiType = kpiType;
    }
    public String getFastGrowing() {
        return fastGrowing;
    }

    public void setFastGrowing(String fastGrowing) {
        this.fastGrowing = fastGrowing;
    }
    public String getHnhb() {
        return hnhb;
    }

    public void setHnhb(String hnhb) {
        this.hnhb = hnhb;
    }
    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    @Override
    public String toString() {
        return "PgHsmConf{" +
        "month=" + month +
        ", kpiName=" + kpiName +
        ", kpiType=" + kpiType +
        ", fastGrowing=" + fastGrowing +
        ", hnhb=" + hnhb +
        ", barcode=" + barcode +
        "}";
    }
}
