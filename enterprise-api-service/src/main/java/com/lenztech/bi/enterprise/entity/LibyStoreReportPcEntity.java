package com.lenztech.bi.enterprise.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/9 15:30
 * @since JDK 1.8
 */
@Data
public class LibyStoreReportPcEntity {

    /**
     * 任务id
     */
    private String taskId;
    /**
     * 答卷id
     */
    private String responseId;
    /**
     * 区域
     */
    private String place;
    /**
     * 城市
     */
    private String city;
    /**
     * 门店编码
     */
    private String storeCode;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 门店类型
     * 0：立白品牌形象店
     * 1：立白标准形象店
     * 2：立白普通形象店
     */
    private String storeType;
    /**
     * 执行时间
     */
    private Date startTime;
    /**
     * 提交时间
     */
    private Date upTime;
    /**
     * 门店总得分
     */
    private BigDecimal totalScore;
    /**
     * 地堆堆型（连堆数）
     */
    private Integer libyDuixing;
    /**
     * 立白堆数量
     */
    private Integer libyDuiNum;
    /**
     * 有无非立白特陈
     */
    private Integer isExistNotLibyDisplay;
    /**
     * 地堆陈设得分
     */
    private BigDecimal diduiDefen;
    /**
     * 连带销售得分
     */
    private BigDecimal liandaiDefen;
    /**
     * 物料有无得分
     */
    private BigDecimal wuliaoDefen;
    /**
     * 区隔陈列得分
     */
    private BigDecimal qugeDefen;
    /**
     * 挂条挂带得分
     */
    private BigDecimal guatiaoDefen;
    /**
     * 立白连带
     */
    private Integer libyLiandai;
    /**
     * 立白堆挂条连带
     */
    private Integer libyGuatiaoLiandai;
    /**
     * 好爸爸连带
     */
    private Integer haobabaLiandai;
    /**
     * 超威连带
     */
    private Integer chaoweiLiandai;
    /**
     * 最后更新时间
     */
    private Date updateTime;

}
