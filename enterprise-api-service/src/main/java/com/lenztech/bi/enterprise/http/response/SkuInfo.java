package com.lenztech.bi.enterprise.http.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

@ApiModel("SKU结果")
public class SkuInfo implements Serializable {

    @ApiModelProperty("图片URL")
    private String imgUrl;

    @ApiModelProperty("识别图URL")
    private String recUrl;

    @ApiModelProperty("图片ID")
    private String imgId;

    @ApiModelProperty("图片长（高）")
    private Integer imgHeight;

    @ApiModelProperty("图片宽度")
    private Integer imgWidth;

    @ApiModelProperty("货架高度")
    private Integer shelfHeight;

    @ApiModelProperty("货架层数")
    private Integer numLayers;

    @ApiModelProperty("sku数量")
    private Integer numPatches;

    @ApiModelProperty("是否翻拍：0否；1是；（默认为0，ai模型配置后才有意义）")
    private Integer remake;

    @ApiModelProperty("SKU详情列表")
    private List<Patche> patches;

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getRecUrl() {
        return recUrl;
    }

    public void setRecUrl(String recUrl) {
        this.recUrl = recUrl;
    }

    public String getImgId() {
        return imgId;
    }

    public void setImgId(String imgId) {
        this.imgId = imgId;
    }

    public Integer getImgHeight() {
        return imgHeight;
    }

    public void setImgHeight(Integer imgHeight) {
        this.imgHeight = imgHeight;
    }

    public Integer getImgWidth() {
        return imgWidth;
    }

    public void setImgWidth(Integer imgWidth) {
        this.imgWidth = imgWidth;
    }

    public Integer getShelfHeight() {
        return shelfHeight;
    }

    public void setShelfHeight(Integer shelfHeight) {
        this.shelfHeight = shelfHeight;
    }

    public Integer getNumLayers() {
        return numLayers;
    }

    public void setNumLayers(Integer numLayers) {
        this.numLayers = numLayers;
    }

    public Integer getNumPatches() {
        return numPatches;
    }

    public void setNumPatches(Integer numPatches) {
        this.numPatches = numPatches;
    }

    public Integer getRemake() {
        return remake;
    }

    public void setRemake(Integer remake) {
        this.remake = remake;
    }

    public List<Patche> getPatches() {
        return patches;
    }

    public void setPatches(List<Patche> patches) {
        this.patches = patches;
    }
}
