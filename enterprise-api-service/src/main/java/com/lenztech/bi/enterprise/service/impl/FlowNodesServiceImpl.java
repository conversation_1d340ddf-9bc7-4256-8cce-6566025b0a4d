//package com.lenztech.bi.enterprise.service.impl;
//
//import com.lenztech.bi.enterprise.entity.FlowNodes;
//import com.lenztech.bi.enterprise.mapper.bienterprise.FlowNodesMapper;
//import com.lenztech.bi.enterprise.service.IFlowNodesService;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import org.springframework.stereotype.Service;
//
///**
// * <p>
// * 流程详情 服务实现类
// * </p>
// *
// * <AUTHOR>
// * @since 2019-08-28
// */
//@Service
//public class FlowNodesServiceImpl extends ServiceImpl<FlowNodesMapper, FlowNodes> implements IFlowNodesService {
//
//}
