package com.lenztech.bi.enterprise.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.CustomerResponseData;
import com.lenztech.bi.enterprise.dto.pg.PgRtirMdDTO;
import com.lenztech.bi.enterprise.dto.pg.PgRtirQueryParam;
import com.lenztech.bi.enterprise.service.PgRtirService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * PG RTIR 控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/biResult/rtir/")
public class PgRtirController {

    public static final Logger logger = LoggerFactory.getLogger(PgRtirController.class);
    
    @Autowired
    PgRtirService pgRtirService;

    /**
     * 分页查询 RTIR 主数据
     * @param param 查询参数
     * @return RTIR主数据列表（分页）
     */
    @RequestMapping(value = "getRtirMdPageList", method = RequestMethod.POST)
    @ControllerAnnotation(use = "分页查询 RTIR 主数据")
    public CustomerResponseData<IPage<PgRtirMdDTO>> getRtirMdPageList(
            @RequestBody PgRtirQueryParam param) {
        try {
            IPage<PgRtirMdDTO> result = pgRtirService.getRtirMdPageList(
                    param.getGtinCode(), param.getCategoryCode(), param.getCategoryCn(),
                    param.getBrandCode(), param.getBrandCn(), param.getProductNameCn(),
                    param.getIsPgProduct(), param.getModeling(), param.getIsPosm(),
                    param.getPageNum(), param.getPageSize());
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("分页查询 RTIR 主数据异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据GTIN编码查询单条记录
     * @param gtinCode GTIN编码
     * @return RTIR主数据
     */
    @RequestMapping(value = "getRtirMdByGtinCode", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据GTIN编码查询单条记录")
    public CustomerResponseData<PgRtirMdDTO> getRtirMdByGtinCode(
            @RequestParam("gtinCode") String gtinCode) {
        try {
            PgRtirMdDTO result = pgRtirService.getRtirMdByGtinCode(gtinCode);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据GTIN编码查询单条记录异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 获取所有品类列表
     * @return 品类列表
     */
    @RequestMapping(value = "getCategoryList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取所有品类列表")
    public CustomerResponseData<List<PgRtirMdDTO>> getCategoryList() {
        try {
            List<PgRtirMdDTO> result = pgRtirService.getCategoryList();
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("获取所有品类列表异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 获取所有品牌列表
     * @return 品牌列表
     */
    @RequestMapping(value = "getBrandList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取所有品牌列表")
    public CustomerResponseData<List<PgRtirMdDTO>> getBrandList() {
        try {
            List<PgRtirMdDTO> result = pgRtirService.getBrandList();
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("获取所有品牌列表异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据品类编码查询商品列表
     * @param categoryCode 品类编码
     * @return 商品列表
     */
    @RequestMapping(value = "getProductListByCategory", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据品类编码查询商品列表")
    public CustomerResponseData<IPage<PgRtirMdDTO>> getProductListByCategory(
            @RequestParam("categoryCode") String categoryCode,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        try {
            IPage<PgRtirMdDTO> result = pgRtirService.getRtirMdPageList(
                    null, categoryCode, null, null, null, null, null, null, null, pageNum, pageSize);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据品类编码查询商品列表异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据品牌编码查询商品列表
     * @param brandCode 品牌编码
     * @return 商品列表
     */
    @RequestMapping(value = "getProductListByBrand", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据品牌编码查询商品列表")
    public CustomerResponseData<IPage<PgRtirMdDTO>> getProductListByBrand(
            @RequestParam("brandCode") String brandCode,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        try {
            IPage<PgRtirMdDTO> result = pgRtirService.getRtirMdPageList(
                    null, null, null, brandCode, null, null, null, null, null, pageNum, pageSize);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据品牌编码查询商品列表异常", e);
            return CustomerResponseData.failure();
        }
    }
}
