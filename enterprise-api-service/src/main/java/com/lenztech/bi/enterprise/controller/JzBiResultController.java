package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.BIResultRet;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.jz.JzBiTargetResp;
import com.lenztech.bi.enterprise.service.JzReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: BI结果 - 20210125 update V3
 * @Author: zhangjie
 * @Date: 29/12/18 下午2:14
 * @uda==
 */
@RestController
@RequestMapping("/biResult")
public class JzBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(JzBiResultController.class);

    @Autowired
    private JzReportService jzReportService;

    /**
     * 根据答卷id查询BI识别指标结果
     *
     * @param responseId 答卷Id
     * @return ResponseData
     */
    @RequestMapping(value = "/jz/getTargetList", method = RequestMethod.GET)
    public ResponseData<BIResultRet> biResultJzV2(String responseId) {
        try {
            JzBiTargetResp jzBiTargetResp = jzReportService.getBiTargetList(responseId);
            return ResponseData.success().data(jzBiTargetResp);
        } catch (Exception e) {
            logger.error("/biResult========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/test", method = RequestMethod.GET)
    public ResponseData test(HttpServletRequest request) {
        int i = request.getServerPort();
        return ResponseData.success().data("server port:" + i);
    }
}
