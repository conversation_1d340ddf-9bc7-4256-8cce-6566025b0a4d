package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public class PgHsmStep2BiReportPrice extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 操作id
     */
    private String taskId;

    private String responseId;

    @TableField("addressIDnum")
    private String addressIDnum;

    private String sfaStoreId = "";

    private String storeCodeSeq = "";

    private String distributorId = "";

    private String distributorStoreId = "";

    @TableField("Biz_Team")
    private String bizTeam = "";

    @TableField("Division")
    private String division = "";

    @TableField("Market")
    private String market = "";

    @TableField("RD")
    private String rd = "";

    @TableField("Province")
    private String province = "";

    @TableField("City")
    private String city = "";

    @TableField("City_Level")
    private String cityLevel = "";

    @TableField("Store_Name")
    private String storeName = "";

    @TableField("Store_Type")
    private String storeType = "";

    @TableField("New_Store_Type")
    private String newStoreType = "";

    @TableField("Banner")
    private String banner = "";

    @TableField("Sub_banner")
    private String subBanner = "";

    @TableField("Address")
    private String address = "";

    /**
     * 问卷生成日期
     */
    private String date;

    private String category;

    /**
     * 0:为新增，1:为更新
     */
    private String ridStatus;

    /**
     * 问题答案更新时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date answerUpdateTime;

    /**
     * 执行月份
     */
    private String month;

    /**
     * 程序执行日期
     */
    private String execDate;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 问卷拜访时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ridUpdateTime;

    /**
     * 品类code
     */
    @TableField("category_code")
    private String categorycode;

    /**
     * dcp_id 有值，则为1，其他0
     */
    @TableField("Is_dcp_Flag")
    private String isDcpFlag;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }

    public String getAddressIDnum() {
        return addressIDnum;
    }

    public void setAddressIDnum(String addressIDnum) {
        this.addressIDnum = addressIDnum;
    }

    public String getSfaStoreId() {
        return sfaStoreId;
    }

    public void setSfaStoreId(String sfaStoreId) {
        this.sfaStoreId = sfaStoreId;
    }
    public String getStoreCodeSeq() {
        return storeCodeSeq;
    }

    public void setStoreCodeSeq(String storeCodeSeq) {
        this.storeCodeSeq = storeCodeSeq;
    }
    public String getDistributorId() {
        return distributorId;
    }

    public void setDistributorId(String distributorId) {
        this.distributorId = distributorId;
    }
    public String getDistributorStoreId() {
        return distributorStoreId;
    }

    public void setDistributorStoreId(String distributorStoreId) {
        this.distributorStoreId = distributorStoreId;
    }
    public String getBizTeam() {
        return bizTeam;
    }

    public void setBizTeam(String bizTeam) {
        this.bizTeam = bizTeam;
    }
    public String getDivision() {
        return division;
    }

    public void setDivision(String division) {
        this.division = division;
    }
    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }
    public String getRd() {
        return rd;
    }

    public void setRd(String rd) {
        this.rd = rd;
    }
    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }
    public String getCityLevel() {
        return cityLevel;
    }

    public void setCityLevel(String cityLevel) {
        this.cityLevel = cityLevel;
    }
    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }
    public String getNewStoreType() {
        return newStoreType;
    }

    public void setNewStoreType(String newStoreType) {
        this.newStoreType = newStoreType;
    }
    public String getBanner() {
        return banner;
    }

    public void setBanner(String banner) {
        this.banner = banner;
    }
    public String getSubBanner() {
        return subBanner;
    }

    public void setSubBanner(String subBanner) {
        this.subBanner = subBanner;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
    public String getRidStatus() {
        return ridStatus;
    }

    public void setRidStatus(String ridStatus) {
        this.ridStatus = ridStatus;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }
    public String getExecDate() {
        return execDate;
    }

    public void setExecDate(String execDate) {
        this.execDate = execDate;
    }

    public Date getAnswerUpdateTime() {
        return answerUpdateTime;
    }

    public void setAnswerUpdateTime(Date answerUpdateTime) {
        this.answerUpdateTime = answerUpdateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getRidUpdateTime() {
        return ridUpdateTime;
    }

    public void setRidUpdateTime(Date ridUpdateTime) {
        this.ridUpdateTime = ridUpdateTime;
    }

    public String getCategorycode() {
        return categorycode;
    }

    public void setCategorycode(String categorycode) {
        this.categorycode = categorycode;
    }

    public String getIsDcpFlag() {
        return isDcpFlag;
    }

    public void setIsDcpFlag(String isDcpFlag) {
        this.isDcpFlag = isDcpFlag;
    }

    @Override
    public String toString() {
        return "PgHsmStep2BiReportPrice{" +
            "id=" + id +
            ", taskId=" + taskId +
            ", responseId=" + responseId +
            ", addressidnum=" + addressIDnum +
            ", sfaStoreId=" + sfaStoreId +
            ", storeCodeSeq=" + storeCodeSeq +
            ", distributorId=" + distributorId +
            ", distributorStoreId=" + distributorStoreId +
            ", bizTeam=" + bizTeam +
            ", division=" + division +
            ", market=" + market +
            ", rd=" + rd +
            ", province=" + province +
            ", city=" + city +
            ", cityLevel=" + cityLevel +
            ", storeName=" + storeName +
            ", storeType=" + storeType +
            ", newStoreType=" + newStoreType +
            ", banner=" + banner +
            ", subBanner=" + subBanner +
            ", address=" + address +
            ", date=" + date +
            ", category=" + category +
            ", ridStatus=" + ridStatus +
            ", answerUpdateTime=" + answerUpdateTime +
            ", month=" + month +
            ", execDate=" + execDate +
            ", createTime=" + createTime +
            ", ridUpdateTime=" + ridUpdateTime +
            ", categoryCode=" + categorycode +
            ", isDcpFlag=" + isDcpFlag +
        "}";
    }
} 