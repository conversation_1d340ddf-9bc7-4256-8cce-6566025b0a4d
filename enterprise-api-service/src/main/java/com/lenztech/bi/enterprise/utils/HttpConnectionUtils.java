package com.lenztech.bi.enterprise.utils;

import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;
import org.apache.http.StatusLine;
import org.apache.http.HttpStatus;

import javax.net.ssl.*;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.io.OutputStreamWriter;
import java.net.URL;
import java.net.URLConnection;
import org.apache.commons.io.IOUtils;
import java.io.OutputStream;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

public class HttpConnectionUtils {

    private static final Logger logger = LoggerFactory.getLogger(HttpConnectionUtils.class);

    final static int BUFFER_SIZE = 4096;
    private static final String MEDIA_TYPE_JSON = "application/json; charset=UTF-8";

    public static String get(String url) {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String str = null;
        try {
            HttpGet httpGet = new HttpGet(url);
            CloseableHttpResponse response1 = httpclient.execute(httpGet);
            logger.info("url = " + url);
            logger.info("http response code = " + response1.getStatusLine().getStatusCode());
            HttpEntity entity1 = response1.getEntity();
            str = HttpConnectionUtils.inputStreamTOString(entity1.getContent());
            logger.info("result = " + str);
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (UnsupportedOperationException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return str;
    }

    private static String inputStreamTOString(InputStream in) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] data = new byte[BUFFER_SIZE];
        int count = -1;
        while ((count = in.read(data, 0, BUFFER_SIZE)) != -1) {
            outStream.write(data, 0, count);
        }
        data = null;
        return new String(outStream.toByteArray(), "UTF-8");
    }

    public static String post(String url, String json) {
        CloseableHttpClient client = HttpClients.createDefault();
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(120000).setConnectionRequestTimeout(120000)
                .setSocketTimeout(120000).build();
        CloseableHttpResponse response = null;
        StringEntity entity = null;
        try {
            //建立Request的对象，一般用目标url来构造，Request一般配置addHeader、setEntity、setConfig
            HttpPost req = new HttpPost(url);
            req.setConfig(requestConfig);
            req.addHeader("Content-Type", "application/json;charset=utf-8");
            req.addHeader("apiKey", "apikey-sfa");
            entity = new StringEntity(json, "utf-8");
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            req.setEntity(entity);
            logger.info("url = " + url);
            logger.info("json = " + json);
            long start = System.currentTimeMillis();
            response = client.execute(req);
            String result = EntityUtils.toString(response.getEntity(), "UTF-8");
            logger.info("http response code = " + response.getStatusLine().getStatusCode());
            logger.info("result = " + result);
            logger.info("time = " + (System.currentTimeMillis() - start) / 1000.0);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                return result;
            }
        } catch (Exception e) {
            logger.error("post http error!", e);
        } finally {
            try {
                EntityUtils.consume(entity);
            } catch (Exception e) {
                logger.error("post http error!", e);
            }
        }
        return null;
    }

    public static String httpPost(String url, String json) {
        disableSslVerification();
        CloseableHttpClient client = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        StringEntity entity = null;
        try {
            logger.info("url = " + url);
            logger.info("json = " + json);
            //建立Request的对象，一般用目标url来构造，Request一般配置addHeader、setEntity、setConfig
            HttpPost req = new HttpPost(url);
            req.addHeader("Content-Type", "application/json;charset=utf-8");
            entity = new StringEntity(json, "utf-8");
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            req.setEntity(entity);
            response = client.execute(req);
            String result = EntityUtils.toString(response.getEntity(), "UTF-8");
            logger.info("result = " + result);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                return result;
            }
        } catch (Exception e) {
            logger.error("post http error!", e);
        } finally {
            try {
                EntityUtils.consume(entity);
            } catch (Exception e) {
                logger.error("post http error!", e);
            }
        }
        return null;
    }

    public static String getRequest(String url,int timeOut) throws Exception{
        URL u = new URL(url);
        if("https".equalsIgnoreCase(u.getProtocol())){
            SslUtils.ignoreSsl();
        }
        URLConnection conn = u.openConnection();
        conn.setConnectTimeout(timeOut);
        conn.setReadTimeout(timeOut);
        return IOUtils.toString(conn.getInputStream());
    }

    public static String postRequest(String urlAddress, String jsonArgs, int timeOut) throws Exception {
        logger.info("URL = " + urlAddress);
        logger.info("JSON = " + jsonArgs);

        URL url = new URL(urlAddress);
        HttpURLConnection conn = null;

        if ("https".equalsIgnoreCase(url.getProtocol())) {
            SslUtils.ignoreSsl(); // Not recommended for production use
        }

        try {
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoInput(true);
            conn.setDoOutput(true);
            conn.setConnectTimeout(timeOut);
            conn.setReadTimeout(timeOut);
            conn.setRequestProperty("Content-Type", MEDIA_TYPE_JSON);

            try (OutputStream os = conn.getOutputStream()) {
                byte[] input = jsonArgs.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
                StringBuilder response = new StringBuilder();
                String responseLine = null;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                return response.toString();
            }
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
    }

    public static void disableSslVerification() {
        // Create a trust manager that does not validate certificate chains
        TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }

                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }

                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };

        // Install the all-trusting trust manager
        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Create all-trusting host name verifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // Install the all-trusting host verifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        String json = "{\n" +
                "\t\"function\": \"point_group\",\n" +
                "\t\"data\": {\n" +
                "\t\t\"input\": [{\n" +
                "\t\t\t\t\"taskId\": \"1\",\n" +
                "\t\t\t\t\"longitude\": \"116.4661073685\",\n" +
                "\t\t\t\t\"latitude\": \"39.9238897177\"\n" +
                "\t\t\t},\n" +
                "\t\t\t{\n" +
                "\t\t\t\t\"taskId\": \"2\",\n" +
                "\t\t\t\t\"longitude\": \"116.4675664902\",\n" +
                "\t\t\t\t\"latitude\": \"39.9249099720\"\n" +
                "\t\t\t},\n" +
                "\t\t\t{\n" +
                "\t\t\t\t\"taskId\": \"3\",\n" +
                "\t\t\t\t\"longitude\": \"116.4640474319\",\n" +
                "\t\t\t\t\"latitude\": \"39.9303730108\"\n" +
                "\t\t\t},\n" +
                "\t\t\t{\n" +
                "\t\t\t\t\"taskId\": \"4\",\n" +
                "\t\t\t\t\"longitude\": \"116.4719438553\",\n" +
                "\t\t\t\t\"latitude\": \"39.9341902972\"\n" +
                "\t\t\t},\n" +
                "\t\t\t{\n" +
                "\t\t\t\t\"taskId\": \"5\",\n" +
                "\t\t\t\t\"longitude\": \"116.4693260193\",\n" +
                "\t\t\t\t\"latitude\": \"39.8774685300\"\n" +
                "\t\t\t},\n" +
                "\t\t\t{\n" +
                "\t\t\t\t\"taskId\": \"6\",\n" +
                "\t\t\t\t\"longitude\": \"116.4809989929\",\n" +
                "\t\t\t\t\"latitude\": \"39.8733845950\"\n" +
                "\t\t\t}\n" +
                "\t\t],\n" +
                "\t\t\"group\": \"3\"\n" +
                "\t}\n" +
                "}";
        System.out.println(HttpConnectionUtils.post("https://callback.sandbox.youxin.cloud/workflow/hooks/nW4f8buPci", json));
    }
}