package com.lenztech.bi.enterprise.dto.pg;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/6/25
 * Time: 10:57
 * 类功能:
 */
@Data
public class PgDpbCategoryDetail {

    private String categorycode;

    private String taskId;

    private String responseId;

    private String ridStatus;

    private String execDate;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date answerUpdateTime;

    private PgHsmSummary pgHsmSummary = new PgHsmSummary();

    private PgHsmDetail pgHsmDetail = new PgHsmDetail();


}
