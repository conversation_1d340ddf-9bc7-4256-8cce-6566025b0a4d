package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.dto.walls.WallsSkuResultInfo;
import com.lenztech.bi.enterprise.entity.HeluxueSkuResult;
import com.lenztech.bi.enterprise.entity.HeluxueStoreRecord;
import com.lenztech.bi.enterprise.mapper.HeluxueSkuResultMapper;
import com.lenztech.bi.enterprise.mapper.HeluxueStoreRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/03/16
 * Time: 15:38
 * 类功能: 和路雪poc相关接口逻辑
 */
@Service
public class WallsReportService {

    @Autowired
    private HeluxueSkuResultMapper heluxueSkuResultMapper;

    @Autowired
    private HeluxueStoreRecordMapper heluxueStoreRecordMapper;


    /**
     * 获取和路雪门店信息
     * @param responseId
     * @return
     */
    public HeluxueStoreRecord getStoreInfo(String responseId){

        LambdaQueryWrapper<HeluxueStoreRecord> heluxueStoreRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        heluxueStoreRecordLambdaQueryWrapper.eq(HeluxueStoreRecord::getResponseId, responseId);
        HeluxueStoreRecord heluxueStoreRecord = heluxueStoreRecordMapper.selectOne(heluxueStoreRecordLambdaQueryWrapper);

        return heluxueStoreRecord;
    }

    /**
     * 获取和路雪门店sku结果
     * @param responseId
     * @return
     */
    public WallsSkuResultInfo getSkuResult(String responseId){

        WallsSkuResultInfo wallsSkuResultInfo = new WallsSkuResultInfo();
        LambdaQueryWrapper<HeluxueSkuResult> heluxueSkuResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
        heluxueSkuResultLambdaQueryWrapper.eq(HeluxueSkuResult::getResponseId, responseId);
        List<HeluxueSkuResult> heluxueSkuResultList = heluxueSkuResultMapper.selectList(heluxueSkuResultLambdaQueryWrapper);
        wallsSkuResultInfo.setSkuResultList(heluxueSkuResultList);
        Integer totalDistCount = 0;
        Integer totalFacingCount = 0;
        for (HeluxueSkuResult heluxueSkuResult : heluxueSkuResultList){
            if (heluxueSkuResult.getDist()){
                totalDistCount ++;
            }
        }
        wallsSkuResultInfo.setTotalDistCount(totalDistCount);

        return wallsSkuResultInfo;
    }

}
