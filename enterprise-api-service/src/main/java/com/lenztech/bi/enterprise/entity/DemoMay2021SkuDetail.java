package com.lenztech.bi.enterprise.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-14
 */
public class DemoMay2021SkuDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String questionId;

    private String responseId;

    private Integer productId;

    /**
     * SKU名称
     */
    private String productName;

    /**
     * 排面
     */
    private Integer facingCount;

    /**
     * 建议排面数
     */
    private Integer standardFacingCount;

    /**
     * 层数
     */
    private String level;

    /**
     * 价格
     */
    private String price;

    /**
     * 标准价格
     */
    private BigDecimal standardPrice;

    /**
     * 是否错误价格
     */
    private Integer ifWrongPrice;

    /**
     * 是否为PSKU
     */
    private Integer ifPsku;

    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
    public Integer getFacingCount() {
        return facingCount;
    }

    public void setFacingCount(Integer facingCount) {
        this.facingCount = facingCount;
    }
    public Integer getStandardFacingCount() {
        return standardFacingCount;
    }

    public void setStandardFacingCount(Integer standardFacingCount) {
        this.standardFacingCount = standardFacingCount;
    }
    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }
    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }
    public BigDecimal getStandardPrice() {
        return standardPrice;
    }

    public void setStandardPrice(BigDecimal standardPrice) {
        this.standardPrice = standardPrice;
    }

    public Integer getIfWrongPrice() {
        return ifWrongPrice;
    }

    public void setIfWrongPrice(Integer ifWrongPrice) {
        this.ifWrongPrice = ifWrongPrice;
    }

    public Integer getIfPsku() {
        return ifPsku;
    }

    public void setIfPsku(Integer ifPsku) {
        this.ifPsku = ifPsku;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Override
    public String toString() {
        return "DemoMay2021SkuDetail{" +
        "id=" + id +
        ", questionId=" + questionId +
        ", responseId=" + responseId +
        ", productName=" + productName +
        ", facingCount=" + facingCount +
        ", standardFacingCount=" + standardFacingCount +
        ", level=" + level +
        ", price=" + price +
        ", standardPrice=" + standardPrice +
        ", ifPsku=" + ifPsku +
        ", updateTime=" + updateTime +
        "}";
    }
}
