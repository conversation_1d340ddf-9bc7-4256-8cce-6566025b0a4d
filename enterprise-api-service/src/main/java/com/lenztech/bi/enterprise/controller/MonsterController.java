package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.chinashop.ChinaShopDemoDTO;

import com.lenztech.bi.enterprise.dto.monster.MonsterMec2ndDisplayDTO;
import com.lenztech.bi.enterprise.dto.monster.MonsterPosmAvailabilityDTO;
import com.lenztech.bi.enterprise.dto.monster.QueryConditionsDTO;
import com.lenztech.bi.enterprise.entity.*;
import com.lenztech.bi.enterprise.service.MonsterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sun<PERSON><PERSON>
 * Date: 2021/5/18
 * Time: 10:48
 * 类功能: 魔爪BI
 */
@RestController
@RequestMapping("/biResult/monster")
public class MonsterController {
    public static final Logger logger = LoggerFactory.getLogger(MonsterController.class);

    @Autowired
    private MonsterService monsterService;

    @RequestMapping(value = "/getDistributionByBrandList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取DistributionByBrandList")
    public ResponseData getDistributionByBrandList(String period, String bg, String city) {
        try {
            List<MonsterDistributionByBrand> result = monsterService.getDistributionByBrandList(period, bg, city);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getDistributionByBrandList========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getDistributionByFlavorList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取DistributionByFlavorList")
    public ResponseData getDistributionByFlavorList(String period, String bg, String city) {
        try {
            List<MonsterDistributionByFlavor> result = monsterService.getDistributionByFlavorList(period, bg, city);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getDistributionByFlavorList========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getUtcDistributionByFlavorList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取UtcDistributionByFlavorList")
    public ResponseData getUtcDistributionByFlavorList(String period, String bg, String city) {
        try {
            List<MonsterUtcDistributionByFlavor> result = monsterService.getUtcDistributionByFlavorList(period, bg, city);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getUtcDistributionByFlavorList========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getStoreRatioByMecFlavorNumberList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取getStoreRatioByMecFlavorNumberList")
    public ResponseData getStoreRatioByMecFlavorNumberList(String period, String bg, String city) {
        try {
            List<MonsterStoreRatioByMecFlavorNumber> result = monsterService.getStoreRatioByMecFlavorNumberList(period, bg, city);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getStoreRatioByMecFlavorNumberList========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getMecPositioningInMainShelfList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取MecPositioningInMainShelfList")
    public ResponseData getMecPositioningInMainShelfList(String period, String bg, String city) {
        try {
            List<MonsterMecPositioningInMainShelf> result = monsterService.getMecPositioningInMainShelfList(period, bg, city);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getMecPositioningInMainShelfList========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getCokeCoolerRatioWithMecList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取CokeCoolerRatioWithMecList")
    public ResponseData getCokeCoolerRatioWithMecList(String period, String bg, String city) {
        try {
            List<MonsterCokeCoolerRatioWithMec> result = monsterService.getCokeCoolerRatioWithMecList(period, bg, city);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getCokeCoolerRatioWithMecList========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getMecPositioningInCustomerCooler", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取MecPositioningInCustomerCooler")
    public ResponseData getMecPositioningInCustomerCooler(String period, String bg, String city) {
        try {
            List<MonsterMecPositioningInCustomerCooler> result = monsterService.getMecPositioningInCustomerCooler(period, bg, city);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getMecPositioningInCustomerCooler========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getSuctionRackAvailabilityList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取SuctionRackAvailabilityList")
    public ResponseData getSuctionRackAvailabilityList(String period, String bg, String city) {
        try {
            List<MonsterSuctionRackAvailability> result = monsterService.getSuctionRackAvailabilityList(period, bg, city);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getSuctionRackAvailabilityList========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getPriceParityWithRbgList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取PriceParityWithRbgList")
    public ResponseData getPriceParityWithRbgList(String period, String bg, String city) {
        try {
            List<MonsterPriceParityWithRbg> result = monsterService.getPriceParityWithRbgList(period, bg, city);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getPriceParityWithRbgList========", e);
        }
        return ResponseData.failure();
    }

    //=============================== 以上为渠道对比，下为年度对比======================================================================

    @RequestMapping(value = "/getPosmAvailabilityList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取PosmAvailabilityList")
    public ResponseData getPosmAvailabilityList(String period, String bg) {
        try {
            MonsterPosmAvailabilityDTO result = monsterService.getPosmAvailabilityList(period, bg);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getPosmAvailabilityList========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getMec2ndDisplayList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取Mec2ndDisplayList")
    public ResponseData getMec2ndDisplayList(String period, String bg) {
        try {
            MonsterMec2ndDisplayDTO result = monsterService.getMec2ndDisplayList(period, bg);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getMec2ndDisplayList========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getYearComparisonEachCityList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取YearComparisonEachCityList")
    public ResponseData getYearComparisonEachCityList(String period, String channel) {
        try {
            List<MonsterYearComparisonEachCity> result = monsterService.getYearComparisonEachCityList(period, channel);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getYearComparisonEachCityList========", e);
        }
        return ResponseData.failure();
    }



    //=============================== 以上为年度对比，下为经销商数据 =====================================================================

    @RequestMapping(value = "/getDealerData1List", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取DealerData1List")
    public ResponseData getDealerData1List(String retailer, String city) {
        try {
            List<MonsterDealerData1> result = monsterService.getDealerData1List(retailer, city);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getDealerData1List========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getDealerData2List", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取DealerData2List")
    public ResponseData getDealerData2List(String retailer, String city) {
        try {
            List<MonsterDealerData2> result = monsterService.getDealerData2List(retailer, city);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getDealerData2List========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getDealerData3List", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取DealerData3List")
    public ResponseData getDealerData3List(String retailer, String city) {
        try {
            List<MonsterDealerData3> result = monsterService.getDealerData3List(retailer, city);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getDealerData3List========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getDealerData4List", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取DealerData4List")
    public ResponseData getDealerData4List(String retailer, String city) {
        try {
            List<MonsterDealerData4> result = monsterService.getDealerData4List(retailer, city);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getDealerData4List========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getDealerData5List", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取DealerData5List")
    public ResponseData getDealerData5List(String retailer, String city) {
        try {
            List<MonsterDealerData5> result = monsterService.getDealerData5List(retailer, city);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getDealerData5List========", e);
        }
        return ResponseData.failure();
    }

    //=============================== 以上为经销商数据，下为查询条件 =====================================================================

    @RequestMapping(value = "/getQueryConditionsChannelCompareList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取渠道对比接口")
    public ResponseData getQueryConditionsChannelCompareList() {
        try {
            QueryConditionsDTO result = monsterService.getQueryConditionsChannelCompareList();
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getQueryConditionsChannelCompareList========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getQueryConditionsAnnualComparisonList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取年度对比接口")
    public ResponseData getQueryConditionsAnnualComparisonList() {
        try {
            QueryConditionsDTO result = monsterService.getQueryConditionsAnnualComparisonList();
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getQueryConditionsAnnualComparisonList========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getQueryConditionsDealerDataList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取经销商数据接口")
    public ResponseData getQueryConditionsDealerDataList() {
        try {
            QueryConditionsDTO result = monsterService.getQueryConditionsDealerDataList();
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getQueryConditionsDealerDataList========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getQueryConditionsDealerDataListByRetail", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取经销商数据接口")
    public ResponseData getQueryConditionsDealerDataListByRetail(String retail) {
        try {
            QueryConditionsDTO result = monsterService.getQueryConditionsDealerDataListByRetail(retail);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getQueryConditionsDealerDataList========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "/getQueryConditionsYearComparisonEachCityList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取年度对比查询条件YearComparisonEachCityList")
    public ResponseData getQueryConditionsYearComparisonEachCityList() {
        try {
            QueryConditionsDTO result = monsterService.getQueryConditionsYearComparisonEachCityList();
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getQueryConditionsYearComparisonEachCityList========", e);
        }
        return ResponseData.failure();
    }

}
