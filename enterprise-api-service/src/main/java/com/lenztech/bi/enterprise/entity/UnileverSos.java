package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-22
 */
public class UnileverSos extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 问卷ID
     */
    private String responseId;

    /**
     * 品类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 本品货架长度
     */
    private Double lengthUl;

    /**
     * 全品类货架长度
     */
    private Double lengthAll;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }
    public Double getLengthUl() {
        return lengthUl;
    }

    public void setLengthUl(Double lengthUl) {
        this.lengthUl = lengthUl;
    }
    public Double getLengthAll() {
        return lengthAll;
    }

    public void setLengthAll(Double lengthAll) {
        this.lengthAll = lengthAll;
    }

    @Override
    public String toString() {
        return "UnileverSos{" +
        "id=" + id +
        ", responseId=" + responseId +
        ", category=" + category +
        ", brand=" + brand +
        ", lengthUl=" + lengthUl +
        ", lengthAll=" + lengthAll +
        "}";
    }
}
