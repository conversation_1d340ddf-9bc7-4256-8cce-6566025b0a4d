package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.BiProjectsConfV1DTO;
import com.lenztech.bi.enterprise.service.impl.BiProjectsConfV1ServiceImpl;
import com.trax.lenz.common.core.domain.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/6/4
 * Time: 10:35
 * 类功能: 和路雪, 英文名叫wall's, poc接口
 */

@RestController
@RequestMapping("/projectsConf")
public class BiProjectsConfV1Controller {

    public static final Logger logger = LoggerFactory.getLogger(BiProjectsConfV1Controller.class);

    @Autowired
    private BiProjectsConfV1ServiceImpl biProjectsConfV1ServiceImpl;

    /**
     * 查询bi BiProjectsConfV1
     * @param othersArgs
     * @return
     */
    @GetMapping(value = "getListByOthersArgs")
    public R<List<BiProjectsConfV1DTO>> getListByOthersArgs(@RequestParam("othersArgs") String othersArgs) {
        return R.ok(biProjectsConfV1ServiceImpl.getListByOthersArgs(othersArgs));
    }


}
