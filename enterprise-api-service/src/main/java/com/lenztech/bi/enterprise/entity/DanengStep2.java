package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 主数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public class DanengStep2 extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 月份
     */
    private String month;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 执行时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    private Date execDate;

    /**
     * 请求ID
     */
    private String responseId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 客户编码
     */
    private String storeCode;

    /**
     * 工单ID
     */
    private String wordOrderId;

    /**
     * 稽查店名
     */
    private String inspectStoreName;

    /**
     * 稽查门店结果
     */
    private String inspectStoreResult;

    /**
     * 稽查日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inspectDate;

    /**
     * 稽查时间
     */
    @DateTimeFormat(pattern="HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "HH:mm:ss")
    private Date inspectTime;

    /**
     * 稽查地址
     */
    private String inspectAddress;

    /**
     * 稽查联系电话
     */
    private String inspectPhone;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 冰柜设备编号
     */
    private String freezerNumber;

    /**
     * 链接
     */
    private String link;

    /**
     * 备注
     */
    private String remarks;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getExecDate() {
        return execDate;
    }

    public void setExecDate(Date execDate) {
        this.execDate = execDate;
    }

    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getWordOrderId() {
        return wordOrderId;
    }

    public void setWordOrderId(String wordOrderId) {
        this.wordOrderId = wordOrderId;
    }

    public String getInspectStoreName() {
        return inspectStoreName;
    }

    public void setInspectStoreName(String inspectStoreName) {
        this.inspectStoreName = inspectStoreName;
    }

    public String getInspectStoreResult() {
        return inspectStoreResult;
    }

    public void setInspectStoreResult(String inspectStoreResult) {
        this.inspectStoreResult = inspectStoreResult;
    }

    public Date getInspectDate() {
        return inspectDate;
    }

    public void setInspectDate(Date inspectDate) {
        this.inspectDate = inspectDate;
    }

    public Date getInspectTime() {
        return inspectTime;
    }

    public void setInspectTime(Date inspectTime) {
        this.inspectTime = inspectTime;
    }

    public String getInspectAddress() {
        return inspectAddress;
    }

    public void setInspectAddress(String inspectAddress) {
        this.inspectAddress = inspectAddress;
    }

    public String getInspectPhone() {
        return inspectPhone;
    }

    public void setInspectPhone(String inspectPhone) {
        this.inspectPhone = inspectPhone;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getFreezerNumber() {
        return freezerNumber;
    }

    public void setFreezerNumber(String freezerNumber) {
        this.freezerNumber = freezerNumber;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @Override
    public String toString() {
        return "DanengStep2{" +
            "id=" + id +
            ", month=" + month +
            ", createTime=" + createTime +
            ", execDate=" + execDate +
            ", responseId=" + responseId +
            ", type=" + type +
            ", storeCode=" + storeCode +
            ", wordOrderId=" + wordOrderId +
            ", inspectStoreName=" + inspectStoreName +
            ", inspectStoreResult=" + inspectStoreResult +
            ", inspectDate=" + inspectDate +
            ", inspectTime=" + inspectTime +
            ", inspectAddress=" + inspectAddress +
            ", inspectPhone=" + inspectPhone +
            ", customerType=" + customerType +
            ", freezerNumber=" + freezerNumber +
            ", link=" + link +
            ", remarks=" + remarks +
        "}";
    }
} 