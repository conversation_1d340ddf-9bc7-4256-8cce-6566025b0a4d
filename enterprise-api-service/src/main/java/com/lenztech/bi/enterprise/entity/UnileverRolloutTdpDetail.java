package com.lenztech.bi.enterprise.entity;

public class UnileverRolloutTdpDetail {
    private Integer id;

    private String responseId;

    private String categoryCode;

    private String categoryNameCn;

    private String categoryNameEn;

    private String subCategoryCode;

    private String subCategoryName;

    private String subCategoryNameEn;

    private String ulCode;

    private String barCode;

    private String skuName;

    private String ifPs;

    private String url;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId == null ? null : responseId.trim();
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode == null ? null : categoryCode.trim();
    }

    public String getCategoryNameCn() {
        return categoryNameCn;
    }

    public void setCategoryNameCn(String categoryNameCn) {
        this.categoryNameCn = categoryNameCn == null ? null : categoryNameCn.trim();
    }

    public String getCategoryNameEn() {
        return categoryNameEn;
    }

    public void setCategoryNameEn(String categoryNameEn) {
        this.categoryNameEn = categoryNameEn == null ? null : categoryNameEn.trim();
    }

    public String getSubCategoryCode() {
        return subCategoryCode;
    }

    public void setSubCategoryCode(String subCategoryCode) {
        this.subCategoryCode = subCategoryCode == null ? null : subCategoryCode.trim();
    }

    public String getSubCategoryName() {
        return subCategoryName;
    }

    public void setSubCategoryName(String subCategoryName) {
        this.subCategoryName = subCategoryName == null ? null : subCategoryName.trim();
    }

    public String getSubCategoryNameEn() {
        return subCategoryNameEn;
    }

    public void setSubCategoryNameEn(String subCategoryNameEn) {
        this.subCategoryNameEn = subCategoryNameEn == null ? null : subCategoryNameEn.trim();
    }

    public String getUlCode() {
        return ulCode;
    }

    public void setUlCode(String ulCode) {
        this.ulCode = ulCode == null ? null : ulCode.trim();
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName == null ? null : skuName.trim();
    }

    public String getIfPs() {
        return ifPs;
    }

    public void setIfPs(String ifPs) {
        this.ifPs = ifPs == null ? null : ifPs.trim();
    }
}