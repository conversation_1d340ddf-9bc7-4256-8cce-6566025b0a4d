//package com.lenztech.bi.enterprise.service;
//
//import com.lenztech.bi.enterprise.dto.BiReportSkuDTO;
//import com.lenztech.bi.enterprise.entity.BiReportBrand;
//import com.lenztech.bi.enterprise.entity.BiReportCategory;
//import com.lenztech.bi.enterprise.entity.BiReportSku;
//import com.lenztech.bi.enterprise.entity.BiReportSkuExample;
//import com.lenztech.bi.enterprise.mapper.BiReportSkuMapper;
//import com.lenztech.bi.enterprise.utils.CglibCopyBeanUtil;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * @Description: bi 品牌
// * @Author: zhangjie
// * @Date: 4/07/20 PM1:59
// */
//@Service
//public class BiReportSkuService {
//
//    @Autowired
//    private BiReportSkuMapper biReportSkuMapper;
//
//    @Autowired
//    private BiCategoryService biCategoryService;
//
//    @Autowired
//    private BiBrandService biBrandService;
//
//    /**
//     * skuId与sku信息映射
//     * @param taskId 任务id
//     * @return
//     */
//    public Map<Integer, BiReportSkuDTO> skuIdSkuMap(String taskId){
//        HashMap<Integer, BiReportCategory> categoryIdHashMap = biCategoryService.categoryIdHashMap(taskId);
//        HashMap<Integer, BiReportBrand> brandIdHashMap = biBrandService.brandIdHashMap(taskId);
//
//        Map<Integer, BiReportSkuDTO> biReportSkuMap = new HashMap<>();
//
//        BiReportSkuExample example = new BiReportSkuExample();
//        example.createCriteria().andTaskIdEqualTo(taskId);
//
//        List<BiReportSku> biReportSkuList = biReportSkuMapper.selectByExample(example);
//        List<BiReportSkuDTO> biReportSkuDTOList = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(biReportSkuList)){
//            for (BiReportSku biReportSku : biReportSkuList){
//                BiReportSkuDTO biReportSkuDTO = new BiReportSkuDTO();
//                CglibCopyBeanUtil.basicCopyBean(biReportSku, biReportSkuDTO);
//                //品类
//                if (categoryIdHashMap.get(biReportSku.getTreeId()) != null){
//                    biReportSkuDTO.setCategoryName(categoryIdHashMap.get(biReportSku.getTreeId()).getName());
//                }
//                //品牌
//                if (brandIdHashMap.get(biReportSku.getTreeId()) != null){
//                    biReportSkuDTO.setBrandName(brandIdHashMap.get(biReportSku.getTreeId()).getName());
//                }
//                biReportSkuMap.put(biReportSku.getTreeId(), biReportSkuDTO);
//            }
//        }
//        return biReportSkuMap;
//    }
//}
