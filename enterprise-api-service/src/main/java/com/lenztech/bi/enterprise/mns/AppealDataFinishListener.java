package com.lenztech.bi.enterprise.mns;

import com.aliyun.mns.client.CloudQueue;
import com.aliyun.mns.client.MNSClient;
import com.aliyun.mns.common.ClientException;
import com.aliyun.mns.common.ServiceException;
import com.aliyun.mns.model.Message;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.lenztech.bi.enterprise.comon.Constant;
import com.lenztech.bi.enterprise.dto.bi.BiReportDetailReq;
//import com.lenztech.bi.enterprise.executor.AppThreadExecutor;
//import com.lenztech.bi.enterprise.service.RecognitionDoneService;
import com.lenztech.bi.enterprise.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Description: 申诉完了mns消息通知
 * @Author: zhangjie
 * @Date: 5/12/18 下午4:14
 */
@Component
public class AppealDataFinishListener {

    private static final Logger logger = LoggerFactory.getLogger(AppealDataFinishListener.class);

    @Value("${aliyun_mns_queueNameAppeal}")
    private String queueName;

    @Autowired
    private MNSClient mnsClient;

//    @Autowired
//    private BiComputeDataService biComputeDataService;
//
//    @Autowired
//    private RecognitionDoneService recognitionDoneService;

    /**
     * 申诉数据处理完成MNS消息监听器
     * <p>
     * 用于监听并消费申诉推送的MNS的消息
     */
    public void consumeMessage() throws InterruptedException {
        logger.info(">>>>>>>>>>>>>>> 申诉数据处理完成MNS消息监听器【启动成功】 ****** <<<<<<<<<<<<<");
        while (true) {
            if (StringUtils.isBlank(queueName)){
              logger.info("未配置队列，退出！");
              return;
            }
            // 解决CPU占用率
            Thread.sleep(1);
            try {
                Message message = getMessage(queueName);
                if (null != message && StringUtils.isNotBlank(message.getMessageBody())) {
                    logger.info("接收到【{}】【BI数据处理完成】消息,messageId:{},messageBody:{}", queueName, message.getMessageId(), message.getMessageBody());
                    BiReportDetailReq biReportDetailReq = JsonUtil.parseObject(message.getMessageBody(), new TypeReference<BiReportDetailReq>() {
                    });
                    if (biReportDetailReq != null && !Strings.isNullOrEmpty(biReportDetailReq.getResponseId())) {
//                        // 如果是青岛啤酒项目，计算中间表数据
//                        if (recognitionDoneService.isQingPiProject(biReportDetailReq.getResponseId())) {
//                            AppThreadExecutor.getInstance().execute(new Runnable() {
//                                @Override
//                                public void run() {
//                                    biComputeDataService.computeQingPiDetail(biReportDetailReq.getResponseId());
//                                }
//                            });
//                        }
                    }

                }
            } catch (Exception e) {
                logger.error("BI数据处理完成MNS消息消费异常!!", e);
            } finally {
                endTrack();
            }
        }
    }

    private Message getMessage(String queueName) {
        try {
            CloudQueue queue = mnsClient.getQueueRef(queueName);
            Message message = queue.popMessage();
            if (null != message) {
                beginTrack(message);
                logger.info("receive message  from: {}  message handle: {}  messageId: {}  message dequeue count: {}  messageBody: {}", queueName, message.getReceiptHandle(), message.getMessageId(), message.getDequeueCount(), message.getMessageBody());
                queue.deleteMessage(message.getReceiptHandle());
                return message;
            }
        } catch (Exception e) {
            logger.error("获取消息异常,queueName:{}", queueName, e);
        }
        return null;
    }

    /**
     * 发消息到MNS队列
     *
     * @param msg
     * @return boolean
     */
    public boolean sendMessageMns(String msg) {
        try {
            CloudQueue queue = mnsClient.getQueueRef(queueName);
            logger.info("【发消息至MNS】message: " + msg);
            Message message = new Message();
            message.setMessageBody(msg);
            Message putMsg = queue.putMessage(message);
            logger.info("【发消息至MNS】mns ret messageId: " + putMsg.getMessageId());
            return true;
        } catch (ClientException ce) {
            logger.error("Something wrong with the network connection between client and MNS service."
                    + "Please check your network and DNS availablity.", ce);
        } catch (ServiceException se) {
            logger.error("MNS exception requestId:" + se.getRequestId(), se);
            if (se.getErrorCode() != null) {
                if (se.getErrorCode().equals("QueueNotExist")) {
                    logger.error("Queue is not exist.Please create before use");
                } else if (se.getErrorCode().equals("TimeExpired")) {
                    logger.error("The request is time expired. Please check your local machine timeclock");
                }
            /*
            you can get more MNS service error code from following link:
            https://help.aliyun.com/document_detail/mns/api_reference/error_code/error_code.html
            */
            }
        } catch (Exception e) {
            logger.error("Unknown exception happened!", e);
        }
        return false;
    }

    /**
     * mns处理线程的traceId设置
     *
     * <AUTHOR>
     * @date 20191008
     */
    private void beginTrack(Message message) {
        String msgId = message.getMessageId();
        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String time = df.format(new Date());
        long id = Thread.currentThread().getId();

        String traceId = time + "." + msgId + "." + id;

        // traceId
        MDC.put(Constant.HEADER_TRACE_ID, traceId);
    }

    /**
     * 清空mdc信息
     *
     * <AUTHOR>
     * @date 2019/4/28 3:14 PM
     */
    private void endTrack() {
        MDC.clear();
    }

}
