package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.entity.BullPatchesResult;
import com.lenztech.bi.enterprise.entity.BullStoreRecord;
import com.lenztech.bi.enterprise.service.BullReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/9/22
 * Time: 13:47
 * 类功能: 公牛poc接口
 */
@RestController
@RequestMapping("/biResult/bull/")
public class BullBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(BullBiResultController.class);

    @Autowired
    BullReportService bullReportService;

    /**
     * 获取公牛门店相关信息
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getStoreInfo", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取门店相关信息")
    public ResponseData<BullStoreRecord> getStoreInfo(String responseId) {
        try {
            BullStoreRecord bullStoreRecord = bullReportService.getStoreInfo(responseId);
            return ResponseData.success().data(bullStoreRecord);
        } catch (Exception e) {
            logger.error("/getStoreInfo========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取公牛识别产品信息
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getRecognizeProductsInfo", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取公牛识别产品信息")
    public ResponseData getRecognizeProductsInfo(String responseId) {
        try {
            List<BullPatchesResult> productPriceInfoList = bullReportService.getRecognizeProductsInfo(responseId);
            return ResponseData.success().data(productPriceInfoList);
        } catch (Exception e) {
            logger.error("/getRecognizeProductsInfo========", e);
        }
        return ResponseData.failure();
    }

}
