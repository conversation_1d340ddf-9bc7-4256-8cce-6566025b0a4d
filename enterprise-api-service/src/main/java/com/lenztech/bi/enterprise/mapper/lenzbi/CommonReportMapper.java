package com.lenztech.bi.enterprise.mapper.lenzbi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.dto.common.CommonBiImage;
import com.lenztech.bi.enterprise.dto.common.CommonBiProduct;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: z<PERSON><PERSON>e
 * @Date: 29/12/18 上午11:31
 */
@Mapper
@DS("lenzbi")
public interface CommonReportMapper {

    /**
     * 查询图片集合
     *
     * @param responseId 答卷Id
     * @return List<CommonBiImage>
     */
    List<CommonBiImage> getImageList(@Param("responseId") String responseId, @Param("shardingMonth") String shardingMonth);

    /**
     * 查询商品集合
     *
     * @param responseId 答卷Id
     * @return List<CommonBiProduct>
     */
    List<CommonBiProduct> getProductList(@Param("responseId") String responseId, @Param("shardingMonth") String shardingMonth);

    /**
     * 根据responseId查询表数据集合
     *
     * @param tableName
     * @param responseId 答卷Id
     * @return List<Map>
     */
    List<Map> getTableDataList(@Param("tableName") String tableName, @Param("responseId") String responseId);
}
