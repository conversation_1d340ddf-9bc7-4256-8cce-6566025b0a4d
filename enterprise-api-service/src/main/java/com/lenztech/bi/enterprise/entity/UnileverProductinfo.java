package com.lenztech.bi.enterprise.entity;

import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
public class UnileverProductinfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private String productId;

    private String categoryName;

    private String brandNameEn;

    private String brandNameCn;

    private String barCode;

    @TableField("UL_code")
    private String ulCode;

    private String productName;

    @TableField("if_COTC")
    private String ifCotc;

    @TableField("COTC_code")
    private String cotcCode;

    @TableField("COTC_name")
    private String cotcName;

    private String ifPrice;

    private String size;

    /**
     * SKU物理宽度/mm
     */
    private Integer width;

    /**
     * SKU物理高度/mm
     */
    private Integer height;

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }
    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }
    public String getBrandNameEn() {
        return brandNameEn;
    }

    public void setBrandNameEn(String brandNameEn) {
        this.brandNameEn = brandNameEn;
    }
    public String getBrandNameCn() {
        return brandNameCn;
    }

    public void setBrandNameCn(String brandNameCn) {
        this.brandNameCn = brandNameCn;
    }
    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }
    public String getUlCode() {
        return ulCode;
    }

    public void setUlCode(String ulCode) {
        this.ulCode = ulCode;
    }
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
    public String getIfCotc() {
        return ifCotc;
    }

    public void setIfCotc(String ifCotc) {
        this.ifCotc = ifCotc;
    }
    public String getCotcCode() {
        return cotcCode;
    }

    public void setCotcCode(String cotcCode) {
        this.cotcCode = cotcCode;
    }
    public String getCotcName() {
        return cotcName;
    }

    public void setCotcName(String cotcName) {
        this.cotcName = cotcName;
    }
    public String getIfPrice() {
        return ifPrice;
    }

    public void setIfPrice(String ifPrice) {
        this.ifPrice = ifPrice;
    }
    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }
    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }
    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    @Override
    public String toString() {
        return "UnileverProductinfo{" +
        "productId=" + productId +
        ", categoryName=" + categoryName +
        ", brandNameEn=" + brandNameEn +
        ", brandNameCn=" + brandNameCn +
        ", barCode=" + barCode +
        ", ulCode=" + ulCode +
        ", productName=" + productName +
        ", ifCotc=" + ifCotc +
        ", cotcCode=" + cotcCode +
        ", cotcName=" + cotcName +
        ", ifPrice=" + ifPrice +
        ", size=" + size +
        ", width=" + width +
        ", height=" + height +
        "}";
    }
}
