package com.lenztech.bi.enterprise.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lenztech.bi.enterprise.entity.DanengPushLog;

import java.util.List;

/**
 * <p>
 * 丹能数据推送日志记录表 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-24
 */
public interface IDanengPushLogService extends IService<DanengPushLog> {
    
    /**
     * 批量保存推送日志
     *
     * @param logList 日志列表
     * @return 是否保存成功
     */
    boolean saveBatch(List<DanengPushLog> logList);
} 