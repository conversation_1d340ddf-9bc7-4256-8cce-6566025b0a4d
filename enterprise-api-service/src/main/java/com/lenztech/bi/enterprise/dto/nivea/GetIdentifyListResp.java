package com.lenztech.bi.enterprise.dto.nivea;

import lombok.Data;
import java.util.List;

/**
 * 妮维雅识别结果列表响应DTO
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Data
public class GetIdentifyListResp {
    /**
     * 开始时间
     */
    private String from_time;

    /**
     * 结束时间
     */
    private String to_time;

    /**
     * 分页信息
     */
    private Metadata metadata;

    /**
     * 结果数量
     */
    private Integer results_generated;

    /**
     * 结果列表
     */
    private List<IdentifyResult> results;

    @Data
    public static class Metadata {
        /**
         * 当前页码
         */
        private Integer page;

        /**
         * 每页数量
         */
        private Integer per_page;

        /**
         * 总页数
         */
        private Integer page_count;

        /**
         * 总记录数
         */
        private Integer total_count;
    }

    @Data
    public static class IdentifyResult {
        /**
         * 会话ID
         */
        private String session_uid;

        /**
         * 客户端会话ID
         */
        private String client_session_uid;

        /**
         * 项目名称
         */
        private String project_name;

        /**
         * 门店编号
         */
        private String store_number;

        /**
         * 外部路线ID
         */
        private String external_route_id;

        /**
         * 会话日期
         */
        private String session_date;

        /**
         * 会话开始时间
         */
        private String session_start_time;

        /**
         * 访问者标识
         */
        private String visitor_identifier;

        /**
         * 结果元数据
         */
        private ResultsMetadata results_metadata;

        /**
         * 结果链接
         */
        private String results_link;
    }

    @Data
    public static class ResultsMetadata {
        /**
         * 生成时间
         */
        private String generation_time;

        /**
         * 版本
         */
        private Integer version;

        /**
         * 状态
         */
        private String status;

        /**
         * 包含的章节
         */
        private List<String> sections_included;
    }
} 