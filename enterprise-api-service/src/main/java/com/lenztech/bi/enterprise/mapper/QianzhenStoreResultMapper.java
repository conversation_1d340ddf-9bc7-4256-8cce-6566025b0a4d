package com.lenztech.bi.enterprise.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lenztech.bi.enterprise.entity.QianzhenStoreResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 千镇门店分销汇总结果 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Mapper
public interface QianzhenStoreResultMapper extends BaseMapper<QianzhenStoreResult> {
    List<QianzhenStoreResult> getListDynamicTable(@Param("responseId") String responseId, @Param("prefix") String prefix);
}
