package com.lenztech.bi.enterprise.utils.date;

import com.lenztech.bi.enterprise.utils.MathTool;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

public class DateTool {

    private static final String[] WEEKS = new String[]{"星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"};

    /**
     * 获指定日期当天的开始时间
     *
     * @param date 指定日期
     * @return 指定日期当天开始时间
     */
    public static Date getDayStart(Date date) {
        Objects.requireNonNull(date);
        return DateTimeTool.toUtilDate(DateTimeUnit.DAY.adjust(DateTimeTool.toLocalDateTime(date), true, 0));
    }

    /**
     * 获指定日期当天的结束时间
     *
     * @param date 指定日期
     * @return 指定日期当天结束时间
     */
    public static Date getDayEnd(Date date) {
        Objects.requireNonNull(date);
        LocalDateTime localDateTime = DateTimeTool.toLocalDateTime(date);
        LocalDateTime dayEnd = LocalDateTime.of(
                localDateTime.getYear(),
                localDateTime.getMonth(),
                localDateTime.getDayOfMonth(),
                23, 59, 59
        );
        return DateTimeTool.toUtilDate(dayEnd);
    }

    /**
     * 获取两个日期之间的天数差
     */
    public static int differentDays(Date date1, Date date2) {
        return differentDays(DateTimeTool.toLocalDateTime(date1), DateTimeTool.toLocalDateTime(date2));
    }

    /**
     * 获取周几
     *
     * @param date 日期
     * @return 周几
     */
    public static String getWeek(Date date) {
        LocalDateTime localDateTime = DateTimeTool.toLocalDateTime(date);
        return WEEKS[localDateTime.getDayOfWeek().getValue() - 1];
    }

    /**
     * 获取两个日期之间的天数差
     */
    public static int differentDays(LocalDateTime localDateTime1, LocalDateTime localDateTime2) {
        LocalDateTime localDate1 = LocalDateTime.of(
                localDateTime1.getYear(),
                localDateTime1.getMonth(),
                localDateTime1.getDayOfMonth(),
                0, 0, 0
        );
        LocalDateTime localDate2 = LocalDateTime.of(
                localDateTime2.getYear(),
                localDateTime2.getMonth(),
                localDateTime2.getDayOfMonth(),
                0, 0, 0
        );
        Duration duration = Duration.between(localDate1, localDate2);
        return Math.abs(MathTool.getIntValue(duration.toDays()));
    }

    /**
     * date2比date1多的天数 通过日期相差秒数计算
     */
    public static int differentDaysBySeconds(Date date1, Date date2) {
        return differentDays(DateTimeTool.toLocalDateTime(date1), DateTimeTool.toLocalDateTime(date2));
    }

    public static int differentDaysBySeconds(LocalDateTime localDateTime1, LocalDateTime localDateTime2) {
        Duration duration = Duration.between(localDateTime1, localDateTime2);
        return Math.abs(MathTool.getIntValue(duration.toDays()));
    }

    public static int differentSeconds(LocalDateTime localDateTime1, LocalDateTime localDateTime2) {
        Duration duration = Duration.between(localDateTime1, localDateTime2);
        return Math.abs(MathTool.getIntValue(duration.toMinutes()));
    }

}
