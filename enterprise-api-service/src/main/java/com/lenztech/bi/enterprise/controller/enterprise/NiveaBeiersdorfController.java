package com.lenztech.bi.enterprise.controller.enterprise;

import com.lenztech.bi.enterprise.dto.nivea.GetIdentifyListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetProductListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreBranchListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreDistrictListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreRegionListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreRetailerListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreStateListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreTypeListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetTargetListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetUserListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetVisitTypeListResp;
import com.lenztech.bi.enterprise.dto.nivea.RouteResp;
import com.lenztech.bi.enterprise.dto.nivea.GetGlobalDataReq;
import com.lenztech.bi.enterprise.dto.nivea.GetGlobalDataResp;
import com.lenztech.bi.enterprise.dto.nivea.QueueAckReq;
import com.lenztech.bi.enterprise.dto.nivea.QueueAckResp;
import com.lenztech.bi.enterprise.dto.nivea.GetQueueSizeResp;
import com.lenztech.bi.enterprise.service.NiveaReportService;
import com.lenztech.bi.enterprise.utils.JsonUtil;
import com.lenztech.bi.enterprise.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 妮维雅BeiersdorfController
 *
 * <AUTHOR>
 * @date 2025-05-07 14:19:19
 */
@Slf4j
@RestController
public class NiveaBeiersdorfController {

    @Autowired
    private NiveaReportService niveaReportService;

    /**
     * 查询门店列表
     *
     * @param page
     * @param per_page
     * @return GetStoreListResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/entity/store", method = RequestMethod.GET)
    public Object getStoreList(HttpServletRequest request, HttpServletResponse response, Integer page, Integer per_page) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetStoreListResp resp = niveaReportService.getStoreList(page, per_page);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 查询门店列表-branch
     *
     * @param page
     * @param per_page
     * @return GetStoreBranchListResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/entity/store/branch", method = RequestMethod.GET)
    public Object getStoreBranchList(HttpServletRequest request, HttpServletResponse response, Integer page, Integer per_page) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetStoreBranchListResp resp = niveaReportService.getStoreBranchList(page, per_page);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 查询门店区域列表
     *
     * @param page
     * @param per_page
     * @return GetStoreDistrictListResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/entity/store/district", method = RequestMethod.GET)
    public Object getStoreDistrictList(HttpServletRequest request, HttpServletResponse response, Integer page, Integer per_page) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetStoreDistrictListResp resp = niveaReportService.getStoreDistrictList(page, per_page);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 查询门店零售商列表
     *
     * @param page
     * @param per_page
     * @return GetStoreRetailerListResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/entity/store/retailer", method = RequestMethod.GET)
    public Object getStoreRetailerList(HttpServletRequest request, HttpServletResponse response, Integer page, Integer per_page) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetStoreRetailerListResp resp = niveaReportService.getStoreRetailerList(page, per_page);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 查询门店州/省列表
     *
     * @param page
     * @param per_page
     * @return GetStoreStateListResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/entity/store/state", method = RequestMethod.GET)
    public Object getStoreStateList(HttpServletRequest request, HttpServletResponse response, Integer page, Integer per_page) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetStoreStateListResp resp = niveaReportService.getStoreStateList(page, per_page);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 查询门店类型列表
     *
     * @param page
     * @param per_page
     * @return GetStoreTypeListResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/entity/store/store_type", method = RequestMethod.GET)
    public Object getStoreTypeList(HttpServletRequest request, HttpServletResponse response, Integer page, Integer per_page) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetStoreTypeListResp resp = niveaReportService.getStoreTypeList(page, per_page);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 查询门店区域列表
     *
     * @param page
     * @param per_page
     * @return GetStoreRegionListResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/entity/store/region", method = RequestMethod.GET)
    public Object getStoreRegionList(HttpServletRequest request, HttpServletResponse response, Integer page, Integer per_page) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetStoreRegionListResp resp = niveaReportService.getStoreRegionList(page, per_page);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 查询用户列表
     *
     * @param page
     * @param per_page
     * @return GetUserListResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/entity/user", method = RequestMethod.GET)
    public Object getUserList(HttpServletRequest request, HttpServletResponse response, Integer page, Integer per_page) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetUserListResp resp = niveaReportService.getUserList(page, per_page);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 查询目标列表
     *
     * @return GetTargetListResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/entity/targets", method = RequestMethod.GET)
    public Object getTargetList(HttpServletRequest request, HttpServletResponse response) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetTargetListResp resp = niveaReportService.getTargetList();
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 查询拜访类型列表
     *
     * @return 固定返回visitType列表
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/entity/visit_type", method = RequestMethod.GET)
    public Object getVisitTypeList(HttpServletRequest request, HttpServletResponse response) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetVisitTypeListResp resp = niveaReportService.getVisitTypeList();
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 查询路线列表
     *
     * @param page
     * @param per_page
     * @return RouteResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/entity/routes", method = RequestMethod.GET)
    public Object getRouteList(HttpServletRequest request, HttpServletResponse response, Integer page, Integer per_page) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        RouteResp resp = niveaReportService.getRouteList(page, per_page);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 查询商品列表
     *
     * @param page
     * @param per_page
     * @return GetProductListResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/entity/product", method = RequestMethod.GET)
    public Object getProductList(HttpServletRequest request, HttpServletResponse response, Integer page, Integer per_page) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetProductListResp resp = niveaReportService.getProductList(page, per_page);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 查询报告
     *
     * @param page
     * @param per_page
     * @return RouteResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/analysis-results", method = RequestMethod.GET)
    public Object getReportList(String from, String to, HttpServletRequest request, HttpServletResponse response, Integer page, Integer per_page) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetIdentifyListResp resp = niveaReportService.getIdentifyList(from, to, page, per_page);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 查询全局数据
     *
     * @param request 请求参数
     * @param response 响应对象
     * @return GetGlobalDataResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/analysis-results/queue/pull", method = RequestMethod.POST)
    public Object getGlobalData(@RequestBody GetGlobalDataReq request, HttpServletRequest httpRequest, HttpServletResponse response) {
        String apiKey = httpRequest.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetGlobalDataResp resp = niveaReportService.queuePull(request);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 队列ack接口
     *
     * @param request 请求参数
     * @param httpRequest HTTP请求
     * @param response HTTP响应
     * @return QueueAckResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/analysis-results/queue/ack", method = RequestMethod.POST)
    public Object queueAck(@RequestBody QueueAckReq request, HttpServletRequest httpRequest, HttpServletResponse response) {
        String apiKey = httpRequest.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        log.info("【beiersdorfch项目】提交ack！body={}", JsonUtil.toJsonString(request.getMessage_ids()));
        QueueAckResp resp = niveaReportService.queueAck(request);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 获取队列长度
     *
     * @param httpRequest HTTP请求
     * @param response HTTP响应
     * @return GetQueueSizeResp
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/analysis-results/queue/size", method = RequestMethod.GET)
    public Object getQueueSize(HttpServletRequest httpRequest, HttpServletResponse response) {
        String apiKey = httpRequest.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetQueueSizeResp resp = niveaReportService.getQueueSize();
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 文件下载接口
     *
     * @param id 响应ID
     * @param httpRequest HTTP请求
     * @param response HTTP响应
     * @return 重定向到文件URL
     */
    @RequestMapping(value = "/api/v1/beiersdorfch/analysis-results/file/download", method = RequestMethod.GET)
    public void downloadFile(String id, HttpServletRequest httpRequest, HttpServletResponse response) {
        String apiKey = httpRequest.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            response.setContentType("application/json");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            try {
                response.getWriter().write(JsonUtil.toJsonString(map));
            } catch (IOException e) {
                log.error("写入响应失败", e);
            }
            return;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            response.setContentType("application/json");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            try {
                response.getWriter().write(JsonUtil.toJsonString(map));
            } catch (IOException e) {
                log.error("写入响应失败", e);
            }
            return;
        }
        
        String fileUrl = niveaReportService.getFileUrl(id);
        if (StringUtils.isBlank(fileUrl)) {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return;
        }
        
        try {
            response.sendRedirect(fileUrl);
        } catch (IOException e) {
            log.error("重定向失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
}
