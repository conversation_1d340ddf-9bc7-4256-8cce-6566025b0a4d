package com.lenztech.bi.enterprise.entity;

import lombok.Data;

import java.util.Date;

/**
 * description
 *
 * @author: yuyang
 * @date：2024-04-12
 */
@Data
public class NiveaGlobalProduct {

    private Long id;
    private String productId;
    private String englishSkuName;
    private String eanCode;
    private String localSkuName;
    private String barcode;
    private String brandEnglishName;
    private String categoryEnglishName;
    private String subCategory;
    private String subCategoryLocalName;
    private String size;
    private String sizeUnit;
    private String attribute1;
    private String attribute3;
    private String attribute5;
    private String attribute6;
    private String attribute7;
    private Date createTime;

}
