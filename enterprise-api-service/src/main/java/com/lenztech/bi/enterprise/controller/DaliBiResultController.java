package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.bnhd.ProductPriceInfo;
import com.lenztech.bi.enterprise.dto.bnhd.RecognizeSceneInfo;
import com.lenztech.bi.enterprise.entity.BnhdPosmsResult;
import com.lenztech.bi.enterprise.entity.BnhdStoreRecord;
import com.lenztech.bi.enterprise.entity.TDaliPoc;
import com.lenztech.bi.enterprise.service.BnhdReportService;
import com.lenztech.bi.enterprise.service.DaliReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sun<PERSON><PERSON>
 * Date: 2021/05/22
 * Time: 17:19
 * 类功能: 达利食品poc相关接口
 */
@RestController
@RequestMapping("/biResult/dali/")
public class DaliBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(DaliBiResultController.class);

    @Autowired
    private DaliReportService daliReportService;

    /**
     * 获取达利食品相关信息
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getStoreInfo", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取门店相关信息")
    public ResponseData<TDaliPoc> getStoreinfo(String responseId) {
        try {
            TDaliPoc bnhdStoreRecord = daliReportService.getStoreInfo(responseId);
            return ResponseData.success().data(bnhdStoreRecord);
        } catch (Exception e) {
            logger.error("/getStoreInfo========", e);
        }
        return ResponseData.failure();
    }


    /**
     * 获取达利食品识别场景信息
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getRecognizeSceneProducts", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取达利食品识别场景及其产品信息")
    public ResponseData getRecognizeSceneProducts(String responseId) {
        try {
            List<RecognizeSceneInfo> recognizeSceneInfoList = daliReportService.getRecognizeSceneProducts(responseId);
            return ResponseData.success().data(recognizeSceneInfoList);
        } catch (Exception e) {
            logger.error("/getRecognizeSceneProducts========", e);
        }
        return ResponseData.failure();
    }




}
