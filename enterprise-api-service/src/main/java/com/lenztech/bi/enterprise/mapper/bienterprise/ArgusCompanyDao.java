//package com.lenztech.bi.enterprise.mapper.bienterprise;
//
//import com.baomidou.dynamic.datasource.annotation.DS;
//import com.lenztech.bi.enterprise.dto.CompanyUserDTO;
//import com.lenztech.bi.enterprise.dto.bi.CompanyDTO;
//import org.apache.ibatis.annotations.Param;
//
//import java.util.List;
//
///**
// * Argus关于企业信息dao
// */
//@DS("bi-enterprise")
//public interface ArgusCompanyDao {
//    /**
//     * 根据账号和公司id查询出用户真实姓名
//     * @param account 账号
//     * @param conpanyId  公司ID
//     * @return
//     */
//    String findRealNameByAaccount(@Param("account") String account,@Param("companyId") Integer companyId);
//
//    /**
//     * 根据公司名称模糊查询公司信息
//     * @param message
//     * @return
//     */
//    List<CompanyDTO> getCompanyMessage(@Param("message") String message);
//
//    /**
//     * 根据公司名称模糊查询公司信息
//     * @param message
//     * @return
//     */
//    List<CompanyDTO> getCompanyId(@Param("message") String message);
//
//    /**
//     * 根据公司名称查询公司信息
//     * @param companyName
//     * @return
//     */
//    CompanyDTO getCompanyMessageByName(@Param("companyName")String companyName);
//
//    List<CompanyUserDTO> getCompanyUser(@Param("account") String account);
//
//
//    List<CompanyUserDTO> getCompanyUserList(@Param("companyId") String companyId);
//}
