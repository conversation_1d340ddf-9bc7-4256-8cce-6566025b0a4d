package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.dto.manon.ManonBiReport;
import com.lenztech.bi.enterprise.dto.manon.ManonBiTargetResp;
import com.lenztech.bi.enterprise.dto.wangwang.WangWangBiTargetResp;
import com.lenztech.bi.enterprise.dto.wangwang.WangwangBiReport;
import com.lenztech.bi.enterprise.mapper.ManonReportMapper;
import com.lenztech.bi.enterprise.mapper.WangwangReportMapper;
import com.lenztech.bi.enterprise.service.ManonReportService;
import com.lenztech.bi.enterprise.service.WangwangReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 曼伦bi指标ServiceImpl
 *
 * <AUTHOR>
 * @date 2019-10-17 15:44:19
 */
@Service
public class ManonReportServiceImpl implements ManonReportService {

    public static final Logger logger = LoggerFactory.getLogger(ManonReportServiceImpl.class);

    @Autowired
    private ManonReportMapper manonReportMapper;

    /**
     * 查询指标集合
     *
     * @param responseId
     * @return ManonBiTargetResp
     */
    @Override
    public ManonBiTargetResp getBiTargetList(String responseId) {
        logger.info("responseId:" + responseId);
        ManonBiTargetResp manonBiTargetResp = new ManonBiTargetResp();
        try {
            List<ManonBiReport> manonBiReportList = manonReportMapper.getBiTargetList(responseId);
            manonBiTargetResp.setResponseId(responseId);
            manonBiTargetResp.setTargetList(manonBiReportList);
        } catch (Exception e) {
            logger.error("/getBiTargetList========", e);
        }
        return manonBiTargetResp;
    }

}
