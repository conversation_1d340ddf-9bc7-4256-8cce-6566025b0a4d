package com.lenztech.bi.enterprise.dto.nivea;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * description
 *
 * @author: y<PERSON><PERSON>
 * @date：2024-04-12
 */
@Data
public class GetProductListResp {

    private Metadata metadata;
    private List<ProductDTO> product;

    @Data
    public static class Metadata {
        private int page;
        private int per_page;
        private int page_count;
        private int total_count;
        private Links links;
        @Data
        public static class Links {
            private String self;
            private String first;
            private String previous;
            private String next;
            private String last;
        }
    }

    @Data
    public static class ProductDTO {
        private Long pk;
        private String product_item_code;
        private String product_client_code;
        private String product_uuid;
        private String product_name;
        private String product_local_name;
        private String product_short_name;
        private String product_global_status_name;
        private String brand_pk;
        private String brand_name;
        private String brand_local_name;
        private String manufacturer_pk;
        private String manufacturer_name;
        private String manufacturer_local_name;
        @JsonProperty("is_deleted")
        private Boolean is_deleted;
        @JsonProperty("is_active")
        private Boolean is_active;
        private String category_pk;
        private String category_name;
        private String category_local_name;
        private String subcategory_name;
        private String subcategory_local_name;
        private String container_type;
        private Integer size;
        private String unit_measurement;
        private Integer number_of_subpackages;
        private Integer units;
        private Boolean discovered_by_brand_watch;
        private List<Image> images;
        private Map<String, String> product_additional_attributes;
        private String alt_code;
        private String product_type;
        private Integer width;
        private Integer height;
        private AlternativeDesigns alternative_designs;

        @Data
        public static class AlternativeDesigns {
            private String alternative_design_name;
            private String start_date;
            private String end_date;
            private List<Image> images;
        }

        @Data
        public static class Image {
            private String image_url;
            private String direction;
        }

    }

}
