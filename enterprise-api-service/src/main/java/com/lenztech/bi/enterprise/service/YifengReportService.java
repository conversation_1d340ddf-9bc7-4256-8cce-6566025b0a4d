package com.lenztech.bi.enterprise.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.dto.bnhd.ProductPriceInfo;
import com.lenztech.bi.enterprise.entity.YifengpharmStoreRecord;
import com.lenztech.bi.enterprise.mapper.YifengpharmPatchResultMapper;
import com.lenztech.bi.enterprise.mapper.YifengpharmStoreRecordMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/7/19
 * Time: 10:41
 * 类功能:
 */
@Service
public class YifengReportService {

    @Autowired
    private YifengpharmStoreRecordMapper yifengpharmStoreRecordMapper;

    @Autowired
    private YifengpharmPatchResultMapper yifengpharmPatchResultMapper;

    private static final String PRODUCT_1 = "缬沙坦氨氯地平片(I) (倍博特) 80毫克+5毫克*28片";

    private static final String PRODUCT_2 = "缬沙坦氨氯地平片(Ⅰ)(倍博特) 7片";

    private static final String PRODUCT_3 = "硝苯地平缓释片(II) (伲福达) 20毫克*42片";

    private static final String PRODUCT_4 = "硝苯地平缓释片(II) (伲福达) 20毫克*30片";

    private static final String PRODUCT_5 = "速效救心丸 40毫克*60粒*3瓶";

    private static final String PRODUCT_6 = "曲安奈德益康唑乳膏 (派瑞松) 15克";

    private static final String PRODUCT_7 = "碳酸钙D3片(Ⅱ) (朗迪) 500毫克*100片";

    private static final String PRODUCT_8 = "三九胃泰颗粒 20克*6袋";

    private static final String PRODUCT_9 = "通络祛痛膏(骨质增生一贴灵) 7厘米*10厘米*6贴";

    private static final String PRODUCT_10 = "维C银翘片 18片";

    private static final String PRODUCT_11 = "新康泰克-氨麻美敏片II-10片";

    private static final String PRODUCT_12 = "芬必得-布洛芬缓释胶囊-300mg";

    private static final String PRODUCT_13 = "芬必得-布洛芬缓释胶囊-400mg";

    private static final String PRODUCT_14 = "芬必得-酚咖片";

    private static final String PRODUCT_15 = "百多邦-莫匹罗星软膏-5g";

    private static final String PRODUCT_16 = "百多邦-莫匹罗星软膏-10g";

    private static final String PRODUCT_17 = "百多邦-创面消毒喷雾";

    private static final String PRODUCT_18 = "扶他林-双氯芬酸二乙胺乳胶剂-20g";

    private static final String PRODUCT_19 = "史克肠虫清-0.2g";

    private static final String PRODUCT_20 = "辅舒良-丙酸氟替卡松鼻喷雾剂-120喷";

    /**
     * 获取益丰药房门店信息
     * @param responseId
     * @return
     */
    public YifengpharmStoreRecord getStoreinfo(String responseId){

        LambdaQueryWrapper<YifengpharmStoreRecord> yifengpharmStoreRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        yifengpharmStoreRecordLambdaQueryWrapper.eq(YifengpharmStoreRecord::getResponseId, responseId);
        YifengpharmStoreRecord yifengpharmStoreRecord = yifengpharmStoreRecordMapper.selectOne(yifengpharmStoreRecordLambdaQueryWrapper);

        return yifengpharmStoreRecord;
    }

    /**
     * 获取益丰药房识别产品信息
     * @param responseId
     * @return
     */
    public List<ProductPriceInfo> getRecognizeProductsInfo(String responseId){

        Map<String, ProductPriceInfo> productPriceInfoMap = initProductPriceInfo();
        List<ProductPriceInfo> productPriceInfoList = yifengpharmPatchResultMapper.getProductPriceInfo(responseId);
        for (ProductPriceInfo productPriceInfo : productPriceInfoList){
            ProductPriceInfo productPriceInfo1 = productPriceInfoMap.get(productPriceInfo.getProductName());
            if (productPriceInfo1 != null){
                productPriceInfo1.setProductPrice(removeRepeat(productPriceInfo.getProductPrice()));
                productPriceInfo1.setCurrentUnit(productPriceInfo.getCurrentUnit());
                productPriceInfo1.setTotalAmount(productPriceInfo.getTotalAmount());
                productPriceInfo1.setIfDist((Integer.valueOf(productPriceInfo.getTotalAmount()) > 0) ? 1 : 0);
            }

        }
        List<ProductPriceInfo> productPriceInfoListTotal = productPriceInfoMap.values().stream().collect(Collectors.toList());
        List<ProductPriceInfo> productPriceInfoListExist = new ArrayList<>();
        List<ProductPriceInfo> productPriceInfoListNotExist = new ArrayList<>();
        for (ProductPriceInfo productPriceInfo : productPriceInfoListTotal){
            if (StringUtils.isNotBlank(productPriceInfo.getTotalAmount()) && !"0".equals(productPriceInfo.getTotalAmount())){
                productPriceInfoListExist.add(productPriceInfo);
            }else {
                productPriceInfoListNotExist.add(productPriceInfo);
            }
        }
        productPriceInfoListTotal.clear();
        productPriceInfoListTotal.addAll(productPriceInfoListExist);
        productPriceInfoListTotal.addAll(productPriceInfoListNotExist);
        return productPriceInfoListTotal;
    }

    /**
     * 去除重复价签 层数 为共享通用poc后台，改为保留一位小数
     * @return
     */
    public String removeRepeat(String sourceData){

        String[] sourceDatas = sourceData.split(",");
        Set sourceDataSet = new HashSet();
        for (String data: sourceDatas){
            if (StringUtils.isNotBlank(data) && (sourceDataSet.size() < 7)){
                data = String.format("%.1f", Double.valueOf(data));
                sourceDataSet.add(data);
            }
        }
        String resultData = String.join(",", sourceDataSet);
        return resultData;

    }

    /**
     * 初始化产品信息
     * @return
     */
    public Map<String, ProductPriceInfo> initProductPriceInfo(){

        Map<String, ProductPriceInfo> productPriceInfoMap = new HashMap<>();

        String[] productArray = {PRODUCT_1, PRODUCT_2, PRODUCT_3, PRODUCT_4, PRODUCT_5, PRODUCT_6, PRODUCT_7, PRODUCT_8, PRODUCT_9, PRODUCT_10, PRODUCT_11, PRODUCT_12, PRODUCT_13, PRODUCT_14, PRODUCT_15, PRODUCT_16, PRODUCT_17, PRODUCT_18, PRODUCT_19, PRODUCT_20};

        for (String product : productArray){
            ProductPriceInfo productPriceInfo = new ProductPriceInfo();
            productPriceInfo.setIfDist(0);
            productPriceInfo.setProductName(product);
            productPriceInfo.setTotalAmount("0");
            productPriceInfoMap.put(product, productPriceInfo);
        }

        return productPriceInfoMap;
    }

}
