//package com.lenztech.bi.enterprise.controller;
//
//import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
//import com.lenztech.bi.enterprise.dto.RequestData;
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.dto.bi.*;
//import com.lenztech.bi.enterprise.service.BiSettingService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//
///**
// * @Description:BI报表重构设置
// * @Author: zhangjie
// * @Date: 3/10/20 PM3:15
// */
//@RestController
//@RequestMapping("/biSetting")
//public class BiSettingController {
//
//    public static final Logger LOGGER = LoggerFactory.getLogger(BiSettingController.class);
//
//    @Autowired
//    private BiSettingService biSettingService;
//
//    /**
//     * 任务基本信息接口
//     *
//     * @param skuTargetListReq
//     * @return
//     */
//    @RequestMapping(value = "/taskInfo", method = RequestMethod.POST)
//    @ControllerAnnotation(use = "任务基本信息接口")
//    public ResponseData getTaskInfo(@RequestBody RequestData<SkuTargetListReq> skuTargetListReq) throws IOException {
//        if (skuTargetListReq == null){
//            return ResponseData.failure();
//        }
//        return biSettingService.getTaskInfo(skuTargetListReq.getReqobj());
//    }
//
//    /**
//     * 拉取SKU列表
//     *
//     * @param skuTargetListReq
//     * @return
//     */
//    @RequestMapping(value = "/getSkuList", method = RequestMethod.POST)
//    @ControllerAnnotation(use = "拉取SKU列表")
//    public ResponseData getSkuList(@RequestBody RequestData<SkuTargetListReq> skuTargetListReq) throws IOException {
//        if (skuTargetListReq == null){
//            return ResponseData.failure();
//        }
//        return biSettingService.getTargetSkuList(skuTargetListReq.getReqobj());
//    }
//
////    /**
////     * 下载SKU模板
////     *
////     * @param taskId 任务id
////     * @param response http响应
////     * @return
////     */
////    @RequestMapping(value = "/downloadSkuExcel", method = RequestMethod.GET)
////    @ControllerAnnotation(use = "下载报表SKU模板")
////    @Deprecated
////    public void downloadSkuExcel(@RequestParam(value = "taskId", required = true) String taskId, HttpServletResponse response) throws IOException {
////        biSettingService.downloadSkuExcel(taskId, response);
////    }
////
////    /**
////     * 上传SKU Excel
////     *
////     * @param taskId 任务id
////     * @param file 文件
////     * @return
////     */
////    @RequestMapping(value = "/uploadSkuExcel", method = RequestMethod.POST)
////    @ControllerAnnotation(use = "上传SKU Excel")
////    @Deprecated
////    public ResponseData uploadSkuExcel(@RequestParam("taskId") String taskId, @RequestPart(value = "file") MultipartFile file) throws IOException {
////        return biSettingService.uploadSkuExcel(taskId, file);
////    }
//
//    /**
//     * 新增或修改品类接口
//     *
//     * @param skuCategoryReq 请求信息
//     * @return
//     */
//    @RequestMapping(value = "/categoryUpdate", method = RequestMethod.POST)
//    @ControllerAnnotation(use = "新增或修改品类接口")
//    public ResponseData categoryUpdate(@RequestBody RequestData<SkuCategoryReq> skuCategoryReq) throws IOException {
//        if (skuCategoryReq == null){
//            return ResponseData.failure();
//        }
//        return biSettingService.categoryUpdate(skuCategoryReq.getReqobj());
//    }
//
//    /**
//     * 删除品类接口
//     *
//     * @param delReq 请求信息
//     * @return
//     */
//    @RequestMapping(value = "/categoryDelete", method = RequestMethod.POST)
//    @ControllerAnnotation(use = "删除品类接口")
//    public ResponseData categoryDelete(@RequestBody RequestData<SkuBrandCategoryDelReq> delReq) throws IOException {
//        if (delReq == null){
//            return ResponseData.failure();
//        }
//        return biSettingService.categoryDel(delReq.getReqobj());
//    }
//
//    /**
//     * 品类列表接口
//     *
//     * @param taskIdReq 请求信息
//     * @return
//     */
//    @RequestMapping(value = "/categoryList", method = RequestMethod.POST)
//    @ControllerAnnotation(use = "品类列表接口")
//    public ResponseData categoryList(@RequestBody RequestData<CommonTaskIdReq> taskIdReq) throws IOException {
//        if (taskIdReq == null){
//            return ResponseData.failure();
//        }
//        return biSettingService.categoryList(taskIdReq.getReqobj());
//    }
//
//    /**
//     * 品牌列表接口
//     *
//     * @param taskIdReq 请求信息
//     * @return
//     */
//    @RequestMapping(value = "/brandList", method = RequestMethod.POST)
//    @ControllerAnnotation(use = "品牌列表接口")
//    public ResponseData brandList(@RequestBody RequestData<CommonTaskIdReq> taskIdReq) throws IOException {
//        if (taskIdReq == null){
//            return ResponseData.failure();
//        }
//        return biSettingService.brandList(taskIdReq.getReqobj());
//    }
//
//    /**
//     * 新增或修改品牌接口
//     *
//     * @param skuBrandReq 请求信息
//     * @return
//     */
//    @RequestMapping(value = "/brandUpdate", method = RequestMethod.POST)
//    @ControllerAnnotation(use = "新增或修改品牌接口")
//    public ResponseData bandUpdate(@RequestBody RequestData<SkuBrandReq> skuBrandReq) throws IOException {
//        if (skuBrandReq == null){
//            return ResponseData.failure();
//        }
//        return biSettingService.brandUpdate(skuBrandReq.getReqobj());
//    }
//
//    /**
//     * 删除品牌接口
//     *
//     * @param delReq 请求信息
//     * @return
//     */
//    @RequestMapping(value = "/brandDelete", method = RequestMethod.POST)
//    @ControllerAnnotation(use = "删除品牌接口")
//    public ResponseData brandDel(@RequestBody RequestData<SkuBrandCategoryDelReq> delReq) throws IOException {
//        if (delReq == null){
//            return ResponseData.failure();
//        }
//        return biSettingService.brandDel(delReq.getReqobj());
//    }
//
//    /**
//     * sku修改接口
//     *
//     * @param updateReq 请求信息
//     * @return
//     */
//    @RequestMapping(value = "/skuUpdate", method = RequestMethod.POST)
//    @ControllerAnnotation(use = "sku修改接口")
//    public ResponseData skuUpdate(@RequestBody RequestData<SkuUpdateReq> updateReq) throws IOException {
//        if (updateReq == null){
//            return ResponseData.failure();
//        }
//        return biSettingService.skuUpdate(updateReq.getReqobj());
//    }
//
//}
