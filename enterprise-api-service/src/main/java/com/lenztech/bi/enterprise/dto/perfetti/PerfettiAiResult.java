package com.lenztech.bi.enterprise.dto.perfetti;

import lombok.Data;

import java.util.List;

/**
 * @ClassName PerfettiAiResult
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/9/24
 **/

@Data
public class PerfettiAiResult {
    /**
     * 图片url
     */
    private String imgUrl;

    /**
     * 识别图url
     */
    private String recUrl;

    /**
     * 图片id
     */
    private String imgId;

    /**
     * 图片长（高）
     */
    private Integer imgHeight;

    /**
     * 图片宽度
     */
    private Integer imgWidth;

    /**
     * 货架高度
     */
    private Integer shelfHeight;

    /**
     * 货架层数
     */
    private Integer numLayers;

    /**
     * 识别出来的sku数量（如果识别后无结果，这里是0））
     */
    private Integer numPatches;

    /**
     * 是否翻拍 1是 0否
     */
    private Integer remake;

    /**
     * Ai翻拍校验置信度分值
     */
    private Double remakeScore;

    /**
     * 原始场景坐标信息
     */
    private List<PerfettiOriginalSceneResult> originalSceneResultList;

    /**
     * sku详情list
     */
    private List<PerfeittiPatch> patches;

    /**
     * posm详情list
     */
    private List<PerfettiPosm> posmList;


}
