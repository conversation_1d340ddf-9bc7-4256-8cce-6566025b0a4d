package com.lenztech.bi.enterprise.dto.bi;

import lombok.Data;

import java.util.List;

/**
 * @Description:魔爪bi识别结果
 * @Author: yuca<PERSON>iang
 * @Date: 15/10/21 AM10:30
 */
@Data
public class MonsterBiResultResp {

    /**
     * rid
     */
    private String responseId;

    /**
     * 是否有任意魔爪的posm 0否,1是
     */
    private Integer hasPosm;

    /**
     * 是否紧邻 0否; 1是
     */
    private Integer hasNext;

    /**
     * 是否有任意魔爪的utc_posm;0否,1是
     */
    private Integer hasUtcPosm;

    /**
     * 是否与红牛相差+—0.3 0否; 1是
     */
    private Integer hasDiff;

    /**
     * 产品分销集合
     */
    private List<MonsterDistribDetail>  productDistribList;


}
