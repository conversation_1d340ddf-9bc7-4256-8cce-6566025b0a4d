package com.lenztech.bi.enterprise.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.entity.UnileverRolloutStoreRecord;
import feign.Param;

@DS("lenzbi")
public interface UnileverRolloutStoreRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(UnileverRolloutStoreRecord record);

    int insertSelective(UnileverRolloutStoreRecord record);

    UnileverRolloutStoreRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(UnileverRolloutStoreRecord record);

    int updateByPrimaryKey(UnileverRolloutStoreRecord record);

    UnileverRolloutStoreRecord getByResponseId(@Param("responseId") String responseId);
}