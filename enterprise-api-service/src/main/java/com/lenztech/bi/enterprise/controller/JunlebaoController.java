package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.junlibao.RepeatBiResultResp;
import com.lenztech.bi.enterprise.dto.unileverapp.ApiResultDTO;
import com.lenztech.bi.enterprise.service.JunlebaoService;
import com.lenztech.bi.enterprise.service.UnileverAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022-01-17 17:50
 */
@RestController
@RequestMapping("/biResult/junlebao")
@Slf4j
public class JunlebaoController {

    @Autowired
    private JunlebaoService junlebaoService;

    /**
     * 查询联合利华App识别结果集
     *
     * @param responseId
     * @return
     */
    @RequestMapping(value = "/getRepeatResult", method = RequestMethod.GET)
    public ResponseData<RepeatBiResultResp> getRepeatResult(String responseId) {
        log.info("【君乐宝查询结果请求】, responseId:{}", responseId);
        try {
            return ResponseData.success().data(junlebaoService.getRepeatResult(responseId));
        } catch (Exception e) {
            log.error("【君乐宝查询结果请求失败!】", e);
        }
        return ResponseData.failure();
    }

}
