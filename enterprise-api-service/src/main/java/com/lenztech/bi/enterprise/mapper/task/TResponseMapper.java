package com.lenztech.bi.enterprise.mapper.task;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.dto.CompanyResponseDTO;
import com.lenztech.bi.enterprise.dto.StoreListDTO;
import com.lenztech.bi.enterprise.dto.UserResponseListDTO;
import com.lenztech.bi.enterprise.dto.UserVisitStoreNumDTO;
import com.lenztech.bi.enterprise.entity.TResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 单份答卷表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-28
 */
@DS("task")
public interface TResponseMapper extends BaseMapper<TResponse> {

//    List<String> selectRidByTaskId(@Param("taskId") String taskId);

    String selectLastVisitTimeByQid(@Param("qid") String qid);

//    String selectTaskIdByQid(@Param("qid") String qid);

//    List<CompanyResponseDTO> getEnterpriseResponseByUpTime(@Param("startTime") String startTime, @Param("endTime") String endTime);

//    List<StoreListDTO> getStoreList(@Param("userId") String userId, @Param("startTime") String startTime, @Param("endTime") String endTime);

//    List<UserResponseListDTO> getUserResponseList(@Param("userId") String userId, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("storeId") String storeId);

//    List<UserVisitStoreNumDTO> getUserVisitStoreNum(@Param("list") Set<String> list, @Param("startTime") String startTime, @Param("endTime") String endTime);

//    List<TResponse> getResponseByDateAndUserList(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("userId") String userId);
}
