package com.lenztech.bi.enterprise.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description 用户答卷
 * @Date 2020/9/14 14:15
 **/

@Data
public class UserResponseListDTO {

    private String responseId;

    private String storeName;

    private String taskAddress;

    private String executeAddress;

    private LocalDateTime executeDateTime;

    private String executeTime;

    private LocalDateTime endDateTime;

    private String endTime;

    private LocalDateTime upLoadDateTime;

    private String upLoadTime;

    private Integer picNum;
}
