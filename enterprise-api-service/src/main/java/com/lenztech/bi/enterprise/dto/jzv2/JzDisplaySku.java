package com.lenztech.bi.enterprise.dto.jzv2;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * (JzDisplaySku)实体类
 *
 * <AUTHOR>
 * @since 2021-10-11 16:45:11
 */
@Data
public class JzDisplaySku implements Serializable {
    private static final long serialVersionUID = 166406365364534832L;
    
    private Object id;
    /**
    * response_id
    */
    private String responseId;
    /**
    * 图片id
    */
    private String imgId;
    /**
    * 场景编号
    */
    private Integer sceneSeq;
    /**
    * 产品客户编码
    */
    private String skuCode;
    /**
    * 产品名称
    */
    private String skuName;
    /**
    * 产品数量
    */
    private Integer count;
    /**
    * 更新时间
    */
    private Date updateTime;

}