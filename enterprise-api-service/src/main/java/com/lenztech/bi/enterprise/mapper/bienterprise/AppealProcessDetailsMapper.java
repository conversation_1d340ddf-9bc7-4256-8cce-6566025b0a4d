//package com.lenztech.bi.enterprise.mapper.bienterprise;
//
//import com.baomidou.dynamic.datasource.annotation.DS;
//import com.lenztech.bi.enterprise.entity.AppealProcessDetails;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//
///**
// * <p>
// * 申诉详情，多条数据对应申诉信息主表 Mapper 接口
// * </p>
// *
// * <AUTHOR>
// * @since 2019-08-28
// */
//@DS("bi-enterprise")
//public interface AppealProcessDetailsMapper extends BaseMapper<AppealProcessDetails> {
//
//}
