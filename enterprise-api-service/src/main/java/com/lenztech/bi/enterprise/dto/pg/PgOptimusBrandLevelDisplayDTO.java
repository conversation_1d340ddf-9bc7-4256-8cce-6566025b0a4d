package com.lenztech.bi.enterprise.dto.pg;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * Brand级别二陈数据DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
public class PgOptimusBrandLevelDisplayDTO {

    /**
     * 客户名称
     */
    private String banner;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店类型
     */
    private String storeType;

    /**
     * group_id
     */
    private String responseGroupId;

    /**
     * rid
     */
    private String responseId;

    /**
     * 执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date execDate;

    /**
     * 品牌名称
     */
    private String brand;

    /**
     * 拜访日期时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date visitDatetime;

    /**
     * 拜访第n次/月
     */
    private String visitCycle;

    /**
     * 拜访年份
     */
    private String visitYear;

    /**
     * 拜访月份
     */
    private String visitMonth;

    /**
     * 拜访周数
     */
    private String visitWeek;

    /**
     * display类型
     */
    private String displayType;

    /**
     * dispaly堆叠的面位数
     */
    private Integer displayStackFacing;

    /**
     * display个数
     */
    private Integer displayCnt;

    /**
     * ByBrand所有堆总的个数
     */
    private Integer totalDisplayCountBrand;

    /**
     * display占比
     */
    private BigDecimal displayCountShare;
}
