package com.lenztech.bi.enterprise.mapper.lenzbi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.entity.MonsterSuctionRackAvailability;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 魔爪suction_rack_availability Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-18
 */
@DS("lenzbi")
public interface MonsterSuctionRackAvailabilityMapper extends BaseMapper<MonsterSuctionRackAvailability> {

}
