package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.cloudPatrolShop.StoreImageListResp;
import com.lenztech.bi.enterprise.dto.cloudPatrolShop.StoreIndicatorResp;
import com.lenztech.bi.enterprise.dto.cloudPatrolShop.StoreListResp;
import com.lenztech.bi.enterprise.service.CloudPatrolShopService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 云巡店controller
 *
 * <AUTHOR>
 * @date 2023/1/3 10:19
 */
@Slf4j
@RestController
@RequestMapping("/biResult/cloudPatrolShop")
@Tag(name = "云巡店服务接口", description = "云巡店服务接口")
public class CloudPatrolShopController {

    @Autowired
    private CloudPatrolShopService cloudPatrolShopService;

    /**
     * 查询门店列表
     *
     * <AUTHOR>
     * @date 2023/1/3 10:26
     */
    @RequestMapping(value = "getStoreList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "查询门店列表")
    @Operation(summary = "查询门店列表", tags = "云巡店服务接口", description = "查询门店列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "provinceName", value = "省名称", example = "河北省"),
            @ApiImplicitParam(name = "cityName", value = "市名称", example = "秦皇岛市"),
            @ApiImplicitParam(name = "address", value = "地址", example = "秦皇大街9号")
    })
    public ResponseData<List<StoreListResp>> getStoreList(@RequestParam(name = "provinceName", required = false) String provinceName,
                                                          @RequestParam(name = "cityName", required = false) String cityName,
                                                          @RequestParam(name = "address", required = false) String address) {
        List<StoreListResp> list = cloudPatrolShopService.getStoreList(provinceName, cityName, address);
        return ResponseData.success(list);
    }

    /**
     * 查询门店题目列表
     *
     * <AUTHOR>
     * @date 2023/1/3 15:35
     */
    @RequestMapping(value = "getStoreImageList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "查询门店题目列表")
    @Operation(summary = "查询门店题目列表", tags = "云巡店服务接口", description = "查询门店题目列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeId", value = "门店编码", required = true)
    })
    public ResponseData<List<StoreImageListResp>> getStoreImageList(@RequestParam(name = "storeId") String storeId) {
        List<StoreImageListResp> list = cloudPatrolShopService.getStoreImageList(storeId);
        return ResponseData.success(list);
    }

    /**
     * 获取门店指标（必分销率&sos）
     *
     * <AUTHOR>
     * @date 2023/1/3 18:07
     */
    @RequestMapping(value = "getStoreIndicator", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取门店指标（必分销率&sos）")
    @Operation(summary = "获取门店指标（必分销率&sos）", tags = "云巡店服务接口", description = "获取门店指标（必分销率&sos）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeId", value = "门店编码", required = true)
    })
    public ResponseData<List<StoreIndicatorResp>> getStoreIndicator(@RequestParam(name = "storeId") String storeId) {
        List<StoreIndicatorResp> list = cloudPatrolShopService.getStoreIndicator(storeId);
        return ResponseData.success(list);
    }
}
