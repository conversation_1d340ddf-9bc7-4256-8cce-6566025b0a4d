package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.VehicleResultDTO;
import com.lenztech.bi.enterprise.entity.JdpResult;
import com.lenztech.bi.enterprise.mapper.lenzbi.JdpResultMapper;
import com.lenztech.bi.enterprise.service.VehicleResultService;
import com.lenztech.bi.enterprise.utils.CglibCopyBeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/11/16 16:30
 **/

@Service
public class VehicleResultServiceImpl implements VehicleResultService {

    @Autowired
    private JdpResultMapper jdpResultMapper;

    @Override
    public ResponseData<VehicleResultDTO> getVehicleResult(String responseId) {
        LambdaQueryWrapper<JdpResult> vehicleWrapper = new LambdaQueryWrapper<>();
        vehicleWrapper.eq(JdpResult::getResponseId, responseId);
        JdpResult result = jdpResultMapper.selectOne(vehicleWrapper);

        VehicleResultDTO dto = new VehicleResultDTO();
        if (!Objects.isNull(result)) {
            dto.setId(result.getId());
            dto.setResponseId(result.getResponseId());
            dto.setPlateNo(result.getPlateNo());
            dto.setVehicleType(result.getVehicleType());
            dto.setOwner(result.getOwner());
            dto.setAddress(result.getAddress());
            dto.setUseChracter(result.getUseChracter());
            dto.setModel(result.getModel());
            dto.setVin(result.getVin());
            dto.setEngineNo(result.getEngineNo());
            dto.setRegisterDate(result.getRegisterDate());
            dto.setIssueDate(result.getIssueDate());
            dto.setUpdateTime(result.getUpdateTime());
            dto.setIsCheat(result.getCheat());
        }

        return ResponseData.success(dto);
    }
}
