package com.lenztech.bi.enterprise.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.dto.bnhd.ProductPriceInfo;
import com.lenztech.bi.enterprise.dto.bnhd.RecognizeSceneInfo;
import com.lenztech.bi.enterprise.entity.BnhdPicLinks;
import com.lenztech.bi.enterprise.entity.BnhdStoreRecord;
import com.lenztech.bi.enterprise.entity.TDaliPoc;
import com.lenztech.bi.enterprise.mapper.TDaliPocMapper;
import com.lenztech.bi.enterprise.mapper.lenzbi.DaliReportMapper;
import com.lenztech.bi.enterprise.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/5/22
 * Time: 9:54
 * 类功能: 达利食品报表
 */
@Service
public class DaliReportService {

    @Autowired
    TDaliPocMapper daliPocMapper;

    @Autowired
    DaliReportMapper daliReportMapper;

    /**
     * 场景 货架
     */
    private static final String SCENE_HJ = "HJ";

    /**
     * 场景 冰柜
     */
    private static final String SCENE_BG= "BG";

    /**
     * 场景 割箱
     */
    private static final String SCENE_GX = "GX";

    /**
     * 获取门店信息
     * @param responseId
     * @return
     */
    public TDaliPoc getStoreInfo(String responseId){

        LambdaQueryWrapper<TDaliPoc> daliPocLambdaQueryWrapper = new LambdaQueryWrapper<>();
        daliPocLambdaQueryWrapper.eq(TDaliPoc::getRid, responseId);
        daliPocLambdaQueryWrapper.last(" limit 1");
        TDaliPoc tDaliPoc = daliPocMapper.selectOne(daliPocLambdaQueryWrapper);
        return tDaliPoc;

    }

    /**
     * 获取百年糊涂识别场景及其产品信息
     * @param responseId
     * @return
     */
    public List<RecognizeSceneInfo> getRecognizeSceneProducts(String responseId){

        List<RecognizeSceneInfo> recognizeSceneInfoList = new ArrayList<>();
        List<TDaliPoc> daliPocList = daliReportMapper.getImageUrlByScene(responseId);
        Map<String, String> stringPicLinksMap = daliPocList.stream().collect(Collectors.toMap(TDaliPoc::getScene, TDaliPoc::getImageUrl));
        if (stringPicLinksMap != null && stringPicLinksMap.size() > 0){
            List<String> sceneList = getSceneList();
            for (String scene: sceneList){
                RecognizeSceneInfo recognizeSceneInfo = getRecognizeSceneInfo(responseId, scene, stringPicLinksMap);
                if (recognizeSceneInfo.getProductPriceInfos() != null && recognizeSceneInfo.getProductPriceInfos().size() > 0){
                    recognizeSceneInfoList.add(recognizeSceneInfo);
                }

            }
        }

        return recognizeSceneInfoList;
    }

    /**
     * 获取百年糊涂识别场景信息
     * @return
     */
    public RecognizeSceneInfo getRecognizeSceneInfo(String responseId, String scene, Map<String, String> stringPicLinksMap){

        RecognizeSceneInfo recognizeSceneInfo = new RecognizeSceneInfo();
        recognizeSceneInfo.setScene(scene);
        recognizeSceneInfo.setPicUrl(stringPicLinksMap.get(scene));
        List<ProductPriceInfo> productPriceInfoList = daliReportMapper.getProductPriceInfoList(responseId, scene);
        if (productPriceInfoList != null && productPriceInfoList.size() > 0){
            recognizeSceneInfo.setProductPriceInfos(productPriceInfoList);
        }

        return recognizeSceneInfo;
    }

    /**
     * 封装场景集合, 要求按 货架、冰柜、割箱、堆头 次序
     * @return
     */
    public List<String> getSceneList(){
        List<String> sceneList = new ArrayList<>();
        sceneList.add(SCENE_HJ);
        sceneList.add(SCENE_BG);
        sceneList.add(SCENE_GX);
        return sceneList;
    }



}
