package com.lenztech.bi.enterprise.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
public class WmddKpi extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * qid
     */
    private String questionId;

    private String responseId;

    /**
     * 货架缺货率
     */
    private BigDecimal shortageRatio;

    /**
     * SKU缺货
     */
    private Integer shortageSku;

    /**
     * 排面数
     */
    private Integer facingCount;

    /**
     * PSKU价格错误
     */
    private Integer wrongPricePskuCount;

    /**
     * PSKU缺货
     */
    private Integer shortagePsku;

    /**
     * PSKU低排面
     */
    private Integer lowFacingPskuCount;

    private LocalDateTime updateTime;

    /**
     * 拼接图链接
     */
    private String stitchUrl;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public BigDecimal getShortageRatio() {
        return shortageRatio;
    }

    public void setShortageRatio(BigDecimal shortageRatio) {
        this.shortageRatio = shortageRatio;
    }
    public Integer getShortageSku() {
        return shortageSku;
    }

    public void setShortageSku(Integer shortageSku) {
        this.shortageSku = shortageSku;
    }
    public Integer getFacingCount() {
        return facingCount;
    }

    public void setFacingCount(Integer facingCount) {
        this.facingCount = facingCount;
    }
    public Integer getWrongPricePskuCount() {
        return wrongPricePskuCount;
    }

    public void setWrongPricePskuCount(Integer wrongPricePskuCount) {
        this.wrongPricePskuCount = wrongPricePskuCount;
    }
    public Integer getShortagePsku() {
        return shortagePsku;
    }

    public void setShortagePsku(Integer shortagePsku) {
        this.shortagePsku = shortagePsku;
    }
    public Integer getLowFacingPskuCount() {
        return lowFacingPskuCount;
    }

    public void setLowFacingPskuCount(Integer lowFacingPskuCount) {
        this.lowFacingPskuCount = lowFacingPskuCount;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getStitchUrl() {
        return stitchUrl;
    }

    public void setStitchUrl(String stitchUrl) {
        this.stitchUrl = stitchUrl;
    }

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    @Override
    public String toString() {
        return "WmddKpi{" +
        "id=" + id +

        ", responseId=" + responseId +
        ", shortageRatio=" + shortageRatio +
        ", shortageSku=" + shortageSku +
        ", facingCount=" + facingCount +
        ", wrongPricePskuCount=" + wrongPricePskuCount +
        ", shortagePsku=" + shortagePsku +
        ", lowFacingPskuCount=" + lowFacingPskuCount +
        ", updateTime=" + updateTime +
        "}";
    }
}
