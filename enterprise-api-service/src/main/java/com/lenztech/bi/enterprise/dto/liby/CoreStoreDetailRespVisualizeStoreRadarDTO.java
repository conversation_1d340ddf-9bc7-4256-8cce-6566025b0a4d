package com.lenztech.bi.enterprise.dto.liby;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 雷达数据
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/13 13:58
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@ToString
@AllArgsConstructor
public class CoreStoreDetailRespVisualizeStoreRadarDTO {

    /**
     * 门店类别
     */
    private String name;

    private Long diDui;

    private Long lianDai;

    private Long wuLiao;

    private Long quGe;

    private Long guaTiao;

}
