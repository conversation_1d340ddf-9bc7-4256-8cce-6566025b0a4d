package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.dto.wangwang.WangWangBiTargetResp;
import com.lenztech.bi.enterprise.dto.wangwang.WangwangBiReport;
import com.lenztech.bi.enterprise.mapper.WangwangReportMapper;
import com.lenztech.bi.enterprise.service.WangwangReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 旺旺bi指标ServiceImpl
 *
 * <AUTHOR>
 * @date 2019-10-17 15:44:19
 */
@Service
public class WangwangReportServiceImpl implements WangwangReportService {

    public static final Logger logger = LoggerFactory.getLogger(WangwangReportServiceImpl.class);

    @Autowired
    private WangwangReportMapper wangwangReportMapper;

    /**
     * 查询指标集合
     *
     * @param responseId
     * @return WangWangBiTargetResp
     */
    @Override
    public WangWangBiTargetResp getBiTargetList(String responseId) {
        logger.info("responseId:" + responseId);
        WangWangBiTargetResp wangWangBiTargetResp = new WangWangBiTargetResp();
        try {
            List<WangwangBiReport> jmlBiTargetList = wangwangReportMapper.getBiTargetList(responseId);
            wangWangBiTargetResp.setResponseId(responseId);
            wangWangBiTargetResp.setTargetList(jmlBiTargetList);
        } catch (Exception e) {
            logger.error("/getBiTargetList========", e);
        }
        return wangWangBiTargetResp;
    }

}
