package com.lenztech.bi.enterprise.dto.bi;

import lombok.Data;

/**
 * @Description:产品分销明细
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 15/10/21 AM10:30
 */
@Data
public class ProductDistribDetail {

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 必分销：1.是 0.否
     */
    private Integer mdist;

    /**
     * 是否分销：1.是 0.否
     */
    private Integer hasDist;

    /**
     * 面位
     */
    private Integer facing;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 排序
     */
    private Integer sort;
}
