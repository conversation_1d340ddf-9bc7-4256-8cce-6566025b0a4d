package com.lenztech.bi.enterprise.mapper.lenzbi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.dto.bnhd.ProductPriceInfo;
import com.lenztech.bi.enterprise.entity.TDaliPoc;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("lenzbi")
public interface DaliReportMapper {

    List<TDaliPoc> getImageUrlByScene(@Param("responseId") String responseId);

    List<ProductPriceInfo> getProductPriceInfoList(@Param("responseId") String responseId, @Param("scene") String scene);
}
