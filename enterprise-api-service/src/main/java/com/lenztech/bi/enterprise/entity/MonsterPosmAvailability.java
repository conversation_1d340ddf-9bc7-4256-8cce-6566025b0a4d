package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 魔爪monster_posm_availability
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-20
 */
public class MonsterPosmAvailability extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 编号
     */
    private Integer noId;

    /**
     * 时间段
     */
    private String period;

    /**
     * BG
     */
    private String bg;

    private String channel;

    private String fact;

    @TableField(value = "posm_availability_2020")
    private String posmAvailability2020;

    @TableField(value = "posm_availability_2021")
    private String posmAvailability2021;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getNoId() {
        return noId;
    }

    public void setNoId(Integer noId) {
        this.noId = noId;
    }
    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }
    public String getBg() {
        return bg;
    }

    public void setBg(String bg) {
        this.bg = bg;
    }
    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
    public String getFact() {
        return fact;
    }

    public void setFact(String fact) {
        this.fact = fact;
    }
    public String getPosmAvailability2020() {
        return posmAvailability2020;
    }

    public void setPosmAvailability2020(String posmAvailability2020) {
        this.posmAvailability2020 = posmAvailability2020;
    }
    public String getPosmAvailability2021() {
        return posmAvailability2021;
    }

    public void setPosmAvailability2021(String posmAvailability2021) {
        this.posmAvailability2021 = posmAvailability2021;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "MonsterPosmAvailability{" +
        "id=" + id +
        ", noId=" + noId +
        ", period=" + period +
        ", bg=" + bg +
        ", channel=" + channel +
        ", fact=" + fact +
        ", posmAvailability2020=" + posmAvailability2020 +
        ", posmAvailability2021=" + posmAvailability2021 +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
