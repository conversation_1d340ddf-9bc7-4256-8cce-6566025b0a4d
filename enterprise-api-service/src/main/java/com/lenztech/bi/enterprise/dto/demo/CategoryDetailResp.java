package com.lenztech.bi.enterprise.dto.demo;

import lombok.Data;

import java.util.List;

@Data
public class CategoryDetailResp {
    private String submitId;
    private String shelfOccupyRate;
    private String waiteHandleTaskNum;
    private String reportTime;
    private List<ProductDetail> stockOutList;
    private List<ProductDetail> lowFaceList;
    private List<ProductDetail> priceErrorList;

    @Data
    public static class ProductDetail {
        private String imageUrl;
        private String productId;
        private String productName;
        private List<String> questionList;
        private String floorNum;
        private Boolean coreProductStatus;
    }
}
