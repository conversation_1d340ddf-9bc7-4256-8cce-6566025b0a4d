package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.dto.jz.JzBiTargetImage;
import com.lenztech.bi.enterprise.dto.jz.JzBiTargetProduct;
import com.lenztech.bi.enterprise.dto.jz.JzBiTargetResp;
import com.lenztech.bi.enterprise.mapper.lenzbi.JzReportMapper;
import com.lenztech.bi.enterprise.service.JzReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description:
 * @Author: zhangjie
 * @Date: 29/12/18 下午2:31
 */
@Service
public class JzReportServiceImpl implements JzReportService {

    public static final Logger logger = LoggerFactory.getLogger(JzReportServiceImpl.class);

    @Autowired
    private JzReportMapper jzReportMapper;

    /**
     * 根据答卷id查询BI识别指标结果
     *
     * @param responseId 答卷Id
     * @return JzBiTargetResp
     */
    @Override
    public JzBiTargetResp getBiTargetList(String responseId) {
        logger.info("responseId:" + responseId);
        JzBiTargetResp jzBiTargetResp = new JzBiTargetResp();
        try {
            List<JzBiTargetImage> jzBiTargetList = jzReportMapper.getImageList(responseId);
            List<JzBiTargetProduct> jzBiTargetProductList = jzReportMapper.getProductList(responseId);
            jzBiTargetResp.setResponseId(responseId);
            jzBiTargetResp.setImageTargetList(jzBiTargetList);
            jzBiTargetResp.setProductTargetList(jzBiTargetProductList);
        } catch (Exception e) {
            logger.error("/getBiTargetList========", e);
        }
        return jzBiTargetResp;
    }
}
