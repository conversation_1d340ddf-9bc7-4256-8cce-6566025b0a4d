package com.lenztech.bi.enterprise.mapper.enterprise;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.trax.lenz.api.dto.enterprise.mengniu.MengniuBiResultImage;
import com.trax.lenz.api.dto.enterprise.mengniu.MengniuBiResultSku;
import com.trax.lenz.api.dto.enterprise.mengniu.MengniuBiResultSummary;
import com.trax.lenz.api.dto.enterprise.mengniu.MengniuBiResultSummarySku;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 蒙牛bi指标Mapper
 *
 * <AUTHOR>
 * @date 2022-06-15 10:30:19
 */
@Mapper
@DS("lenzbi")
public interface MengniuMapper {

    /**
     * 查询指标集合
     *
     * @param responseId 答卷Id
     * @return List<MengniuBiResultImage>
     */
    List<MengniuBiResultImage> getImageList(@Param("responseId") String responseId);

    /**
     * 查询指标集合
     *
     * @param responseId 答卷Id
     * @return List<MengniuBiResultSku>
     */
    List<MengniuBiResultSku> getSkuList(@Param("responseId") String responseId);

    /**
     * 查询汇总结果
     *
     * @param responseId 答卷Id
     * @return List<MengniuBiResultSku>
     */
    MengniuBiResultSummary getSummaryResult(@Param("responseId") String responseId);

    /**
     * 查询汇总sku集合
     *
     * @param responseId 答卷Id
     * @return List<MengniuBiResultSku>
     */
    List<MengniuBiResultSummarySku> getSummarySkuList(@Param("responseId") String responseId);

}
