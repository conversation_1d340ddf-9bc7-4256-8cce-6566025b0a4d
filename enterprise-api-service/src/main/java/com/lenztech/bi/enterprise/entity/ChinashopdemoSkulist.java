package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("chinashopdemo_skulist")
public class ChinashopdemoSkulist implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品ID
     */
    private Integer productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品容量(ml)
     */
    private Float volume;

    /**
     * 产品价格
     */
    private Float price;

    /**
     * 是否为PSKU(1=是， 0=否)
     */
    @TableField("if_PSKU")
    private Integer ifPsku;


}
