package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-18
 */
public class BiStoreReport extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 答卷id
     */
    private String responseId;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 进店时间
     */
    private LocalDateTime enterTime;

    /**
     * 离店时间
     */
    private LocalDateTime leaveTime;

    /**
     * 耗时
     */
    private Integer operatingTime;

    /**
     * 品类id
     */
    private Integer categoryId;

    /**
     * 品类
     */
    private String category;

    /**
     * 分销率
     */
    private BigDecimal distRate;

    /**
     * 缺货率
     */
    private BigDecimal outOfStockRate;

    /**
     * 产品货架占比
     */
    private BigDecimal productSos;

    /**
     * 竞品货架占比
     */
    private BigDecimal competingProductSos;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }
    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
    public LocalDateTime getEnterTime() {
        return enterTime;
    }

    public void setEnterTime(LocalDateTime enterTime) {
        this.enterTime = enterTime;
    }
    public LocalDateTime getLeaveTime() {
        return leaveTime;
    }

    public void setLeaveTime(LocalDateTime leaveTime) {
        this.leaveTime = leaveTime;
    }
    public Integer getOperatingTime() {
        return operatingTime;
    }

    public void setOperatingTime(Integer operatingTime) {
        this.operatingTime = operatingTime;
    }
    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
    public BigDecimal getDistRate() {
        return distRate;
    }

    public void setDistRate(BigDecimal distRate) {
        this.distRate = distRate;
    }
    public BigDecimal getOutOfStockRate() {
        return outOfStockRate;
    }

    public void setOutOfStockRate(BigDecimal outOfStockRate) {
        this.outOfStockRate = outOfStockRate;
    }
    public BigDecimal getProductSos() {
        return productSos;
    }

    public void setProductSos(BigDecimal productSos) {
        this.productSos = productSos;
    }
    public BigDecimal getCompetingProductSos() {
        return competingProductSos;
    }

    public void setCompetingProductSos(BigDecimal competingProductSos) {
        this.competingProductSos = competingProductSos;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BiStoreReport{" +
        "id=" + id +
        ", taskId=" + taskId +
        ", responseId=" + responseId +
        ", storeCode=" + storeCode +
        ", storeName=" + storeName +
        ", enterTime=" + enterTime +
        ", leaveTime=" + leaveTime +
        ", operatingTime=" + operatingTime +
        ", categoryId=" + categoryId +
        ", category=" + category +
        ", distRate=" + distRate +
        ", outOfStockRate=" + outOfStockRate +
        ", productSos=" + productSos +
        ", competingProductSos=" + competingProductSos +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
