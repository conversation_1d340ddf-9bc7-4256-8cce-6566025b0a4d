package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * <p>
 * 宝洁cdl推送数据明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
public class PgHsmCdlPushDetailLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 程序执行日期
     */
    private String execDate;

    /**
     * 操作id
     */
    private String taskId;

    private String responseId;

    @TableField("addressIDnum")
    private String addressIDnum;

    private String category;

    private String month;

    private String date;

    /**
     * 指标名称
     */
    private String kpiName;

    /**
     * 指标值
     */
    private String kpiValue;

    /**
     * 客户类型 0 ge 1 dcp
     */
    private Integer customerType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getExecDate() {
        return execDate;
    }

    public void setExecDate(String execDate) {
        this.execDate = execDate;
    }
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }

    public String getAddressIDnum() {
        return addressIDnum;
    }

    public void setAddressIDnum(String addressIDnum) {
        this.addressIDnum = addressIDnum;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }
    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }
    public String getKpiName() {
        return kpiName;
    }

    public void setKpiName(String kpiName) {
        this.kpiName = kpiName;
    }
    public String getKpiValue() {
        return kpiValue;
    }

    public void setKpiValue(String kpiValue) {
        this.kpiValue = kpiValue;
    }
    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "PgHsmCdlPushDetailLog{" +
            "id=" + id +
            ", execDate=" + execDate +
            ", taskId=" + taskId +
            ", responseId=" + responseId +
            ", addressIDnum=" + addressIDnum +
            ", category=" + category +
            ", month=" + month +
            ", date=" + date +
            ", kpiName=" + kpiName +
            ", kpiValue=" + kpiValue +
            ", customerType=" + customerType +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
        "}";
    }
}
