package com.lenztech.bi.enterprise.comon;

/**
 * 产品类型（bi数据,武松订阅）
 *
 * <AUTHOR>
 * @date 2020-03-12 17:36:44
 */
public enum BiDataProductTypeEnum {

    /** 非必备本品 */
    SELF_PRODUCT(1, "非必备本品"),

    /** 竞品 */
    COMP_PRODUCT(2, "竞品"),

    /** 必备本品 */
    MUST_PRODUCT(0, "必备本品");

    private Integer value;

    private String desc;

    private BiDataProductTypeEnum(Integer value, String desc ){
        this.value = value;
        this.desc = desc;
    }

    /**
     * 获取显示名称
     * @param typeValue
     * @return
     */
    public static String getDesByTypeValue(int typeValue) {
        for (BiDataProductTypeEnum biReportSkuTypeEnum : values()) {
            if (biReportSkuTypeEnum.getValue().intValue() == typeValue) {
                return biReportSkuTypeEnum.desc;
            }
        }
        return null;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
