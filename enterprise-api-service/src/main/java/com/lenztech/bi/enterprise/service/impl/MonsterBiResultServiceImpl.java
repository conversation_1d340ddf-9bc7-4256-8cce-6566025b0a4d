package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lenztech.bi.enterprise.dto.bi.MonsterBiResultResp;
import com.lenztech.bi.enterprise.dto.bi.MonsterDistribDetail;
import com.lenztech.bi.enterprise.entity.MonsterPatchesResult;
import com.lenztech.bi.enterprise.entity.MonsterStoreResult;
import com.lenztech.bi.enterprise.service.MonsterBiResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 魔爪bi报表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Service
@Slf4j
public class MonsterBiResultServiceImpl implements MonsterBiResultService {


    @Autowired
    private MonsterPatchesResultServiceImpl monsterPatchesResultServiceImpl;
    @Autowired
    private MonsterStoreResultServiceImpl monsterStoreResultServiceImpl;

    @Override
    public List<MonsterBiResultResp> getReport(List<String> responseIdList) {

        List<MonsterBiResultResp> monsterBiResultRespList = new ArrayList<>();

        for (String responseId : responseIdList) {
            QueryWrapper<MonsterStoreResult> storeQuerySkuapper = new QueryWrapper<>();
            storeQuerySkuapper.eq("response_id", responseId);
            MonsterStoreResult one = monsterStoreResultServiceImpl.getOne(storeQuerySkuapper);
            QueryWrapper<MonsterPatchesResult> patchesStoreQuerySkuapper = new QueryWrapper<>();
            patchesStoreQuerySkuapper.eq("response_id", responseId);
            List<MonsterPatchesResult> list = monsterPatchesResultServiceImpl.list(patchesStoreQuerySkuapper);
            if (one == null) {
                log.info("没有查询到识别结果,responseId:{}", responseId);
               continue;
            }

            List<MonsterDistribDetail> productDistribList = new ArrayList<>();
            MonsterBiResultResp monsterBiResultResp = new MonsterBiResultResp();
            monsterBiResultResp.setHasNext(one.getHasNext());
            monsterBiResultResp.setHasPosm(one.getHasPosm());
            monsterBiResultResp.setHasUtcPosm(one.getHasUtcPosm());
            monsterBiResultResp.setHasDiff(one.getHasDiff());
            monsterBiResultResp.setResponseId(one.getResponseId());
            monsterBiResultResp.setProductDistribList(productDistribList);
            for (MonsterPatchesResult monsterPatchesResult : list) {
                MonsterDistribDetail monsterDistribDetail = new MonsterDistribDetail();
                monsterDistribDetail.setIsMonster(monsterPatchesResult.getIsMonster());
                monsterDistribDetail.setHasDist(monsterPatchesResult.getHasDist());
                monsterDistribDetail.setFacing(monsterPatchesResult.getFacing());
                monsterDistribDetail.setProductId(monsterPatchesResult.getProductId());
                monsterDistribDetail.setProductName(monsterPatchesResult.getProductName());
                productDistribList.add(monsterDistribDetail);
            }
            monsterBiResultRespList.add(monsterBiResultResp);
        }


        return monsterBiResultRespList;
    }
}
