package com.lenztech.bi.enterprise.utils;

import java.util.List;

public class Result {
    private Location location;
    //格式化详细地址
    private String formatted_address;
    private String business;
    //地址属性
    private AddressComponent addressComponent;
    private List pois;
    private List roads;
    private List poiRegions;
    //语义描述
    private String sematic_description;
    //城市编码
    private int cityCode;
    private String precise;
    private String confidence;
    private String level;

    public Location getLocation() {
        return location;
    }

    public void setLocation(Location location) {
        this.location = location;
    }

    public String getFormatted_address() {
        return formatted_address;
    }

    public void setFormatted_address(String formatted_address) {
        this.formatted_address = formatted_address;
    }

    public String getBusiness() {
        return business;
    }

    public void setBusiness(String business) {
        this.business = business;
    }

    public AddressComponent getAddressComponent() {
        return addressComponent;
    }

    public void setAddressComponent(AddressComponent addressComponent) {
        this.addressComponent = addressComponent;
    }

    public List getPois() {
        return pois;
    }

    public void setPois(List pois) {
        this.pois = pois;
    }

    public List getRoads() {
        return roads;
    }

    public void setRoads(List roads) {
        this.roads = roads;
    }

    public List getPoiRegions() {
        return poiRegions;
    }

    public void setPoiRegions(List poiRegions) {
        this.poiRegions = poiRegions;
    }

    public String getSematic_description() {
        return sematic_description;
    }

    public void setSematic_description(String sematic_description) {
        this.sematic_description = sematic_description;
    }

    public int getCityCode() {
        return cityCode;
    }

    public void setCityCode(int cityCode) {
        this.cityCode = cityCode;
    }

    public String getPrecise() {
        return precise;
    }

    public void setPrecise(String precise) {
        this.precise = precise;
    }

    public String getConfidence() {
        return confidence;
    }

    public void setConfidence(String confidence) {
        this.confidence = confidence;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    @Override
    public String toString() {
        return "Result{" +
                "location=" + (location!=null?location.toString():"") +
                ", formatted_address='" + formatted_address + '\'' +
                ", business='" + business + '\'' +
                ", addressComponent=" + (addressComponent!=null?addressComponent.toString():"") +
                ", pois=" + pois +
                ", roads=" + roads +
                ", poiRegions=" + poiRegions +
                ", sematic_description='" + sematic_description + '\'' +
                ", cityCode=" + cityCode +
                ", precise='" + precise + '\'' +
                ", confidence='" + confidence + '\'' +
                ", level='" + level + '\'' +
                '}';
    }
}
