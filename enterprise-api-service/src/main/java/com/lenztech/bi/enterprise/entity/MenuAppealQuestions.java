package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 图像识别申诉题目
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-28
 */
public class MenuAppealQuestions extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "question_id", type = IdType.AUTO)
    private Integer questionId;

    /**
     * 题目对应的菜单(也就是ppz项目)的外键
     */
    private Integer menuId;

    /**
     * ppz任务id
     */
    private String taskidOwner;

    /**
     * 产品名
     */
    private String productName;

    /**
     * 产品id，多个下划线拼接
     */
    private String productId;

    /**
     * 排序索引
     */
    private Integer index;

    /**
     * 题目类型  0分销 1面位 2填空 3单选 4多选  目前只支持产品有无申诉和数目申诉
     */
    private Integer questionType;

    private Date createTime;

    private Date updateTime;

    public Integer getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Integer questionId) {
        this.questionId = questionId;
    }

    public Integer getMenuId() {
        return menuId;
    }

    public void setMenuId(Integer menuId) {
        this.menuId = menuId;
    }

    public String getTaskidOwner() {
        return taskidOwner;
    }

    public void setTaskidOwner(String taskidOwner) {
        this.taskidOwner = taskidOwner;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Integer getQuestionType() {
        return questionType;
    }

    public void setQuestionType(Integer questionType) {
        this.questionType = questionType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
