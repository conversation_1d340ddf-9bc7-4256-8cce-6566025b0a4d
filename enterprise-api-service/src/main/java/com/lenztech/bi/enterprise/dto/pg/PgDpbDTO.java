package com.lenztech.bi.enterprise.dto.pg;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lenztech.bi.enterprise.entity.PgHsmDetailBiReport;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/6/22
 * Time: 10:20
 * 类功能:
 */
@Data
public class PgDpbDTO {

    private String taskId;

    private String responseId;

    private String addressIDnum;

    private String sfaStoreId;

    private String storeCodeSeq;

    private String distributorId;

    private String distributorStoreId;

    private String bizTeam;

    private String division;

    private String market;

    private String rd;

    private String province;

    private String city;

    private String cityLevel;

    private String storeName;

    private String storeType;

    private String newStoreType;

    private String banner;

    private String subBanner;

    private String address;

    private String date;

    private String category;

    /**
     * 0:为新增，1:为更新
     */
    private String ridStatus;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date answerUpdateTime;

    /**
     * 执行月份
     */
    private String month;

    private PgHsmDetailKpiTypeDTO pgHsmDetail;

}
