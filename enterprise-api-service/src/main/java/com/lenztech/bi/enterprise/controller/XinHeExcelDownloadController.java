/*
 * Copyright (c) 2022. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.lenztech.bi.enterprise.controller;

import com.alibaba.excel.EasyExcel;
import com.lenztech.bi.enterprise.dto.xinhe.DownloadExcelDTO;
import com.lenztech.bi.enterprise.dto.xinhe.XinheAppAggDataDTO;
import com.lenztech.bi.enterprise.entity.XinheAppAggData;
import com.lenztech.bi.enterprise.service.impl.XinHeExcelDownloadServiceImpl;
import com.lenztech.bi.enterprise.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 		date 2022/10/13
 **/
@Controller
@RequestMapping("xinHeExcelDownload")
public class XinHeExcelDownloadController {
	
	private final XinHeExcelDownloadServiceImpl xinHeExcelDownloadService;
	@Value("${trax.custom.argusDetailUrl:http://argus-test.langjtech" +
			       ".com/ask/main/sku/}")
	private String argusDetailUrl;
	
	@Autowired
	public XinHeExcelDownloadController(XinHeExcelDownloadServiceImpl xinHeExcelDownloadService) {
		this.xinHeExcelDownloadService = xinHeExcelDownloadService;
	}
	
	/**
	 * 下载欣和报表
	 * @param response
	 * @param downloadExcelDTO
	 * @throws IOException
	 */
	@GetMapping("download")
	public void download(HttpServletResponse response ,String taskId ,Long importTaskId ,Long importBatchId) throws IOException {
		DownloadExcelDTO downloadExcelDTO = new DownloadExcelDTO();
		downloadExcelDTO.setTaskId(taskId);
		downloadExcelDTO.setImportTaskId(importTaskId);
		downloadExcelDTO.setImportBatchId(importBatchId);
		// 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		response.setCharacterEncoding("utf-8");
		// 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
		String fileName =
				URLEncoder.encode("" + importTaskId + ((null == importBatchId) ? "" : "_" + importBatchId) + "_" + DateUtil.getCurrentDate("yyyyMMddHHmmss") ,
						"UTF-8");
		response.setHeader("Content-disposition" ,"attachment;filename*=utf-8''" + fileName + ".xlsx");
		EasyExcel.write(response.getOutputStream() ,XinheAppAggDataDTO.class).sheet("识别结果").doWrite(this.getListByImportInfo(downloadExcelDTO));
	}
	
	/**
	 * 获取数据
	 * @param downloadExcelDTO
	 * @return
	 */
	private List<XinheAppAggDataDTO> getListByImportInfo(DownloadExcelDTO downloadExcelDTO) {
		List<XinheAppAggData> list = xinHeExcelDownloadService.getListByImportInfo(downloadExcelDTO);
		return list.parallelStream().map(xinheAppAggData -> {
			XinheAppAggDataDTO xinheAppAggDataDTO = new XinheAppAggDataDTO();
			xinheAppAggDataDTO.setResponseId(xinheAppAggData.getResponseId());
			xinheAppAggDataDTO.setQuestionId(xinheAppAggData.getQuestionId());
			xinheAppAggDataDTO.setImageId(xinheAppAggData.getImageId());
			xinheAppAggDataDTO.setCustomImageId(xinheAppAggData.getCustomImageId());
			xinheAppAggDataDTO.setScene(xinheAppAggData.getScene());
			xinheAppAggDataDTO.setImageUrl(xinheAppAggData.getImageUrl());
			xinheAppAggDataDTO.setReportUrl(argusDetailUrl + xinheAppAggData.getResponseId() + "/" + xinheAppAggData.getQuestionId() +
					                                "?type=sku");
			xinheAppAggDataDTO.setVisitTime(xinheAppAggData.getVisitTime());
			xinheAppAggDataDTO.setProductId(xinheAppAggData.getProductId());
			xinheAppAggDataDTO.setProductName(xinheAppAggData.getProductName());
			xinheAppAggDataDTO.setCustomerCode(xinheAppAggData.getCustomerCode());
			xinheAppAggDataDTO.setProductTotal(xinheAppAggData.getProductTotal());
			xinheAppAggDataDTO.setLayer(xinheAppAggData.getLayer());
			xinheAppAggDataDTO.setLayerCount(xinheAppAggData.getLayerCount());
			return xinheAppAggDataDTO;
		}).collect(Collectors.toList());
	}
	
}
