package com.lenztech.bi.enterprise.mapper.lenzbi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.dto.tsingtao.TsingtaoProductKpi;
import com.lenztech.bi.enterprise.entity.QingpiTarProductList;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-28
 */
@DS("lenzbi")
public interface QingpiTarProductListMapper extends BaseMapper<QingpiTarProductList> {

    List<TsingtaoProductKpi> getQingpiProductList(@Param("areaName") String areaName, @Param("systemName") String systemName);

}
