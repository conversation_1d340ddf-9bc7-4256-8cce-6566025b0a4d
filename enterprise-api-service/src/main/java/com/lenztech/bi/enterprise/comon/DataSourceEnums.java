package com.lenztech.bi.enterprise.comon;

import lombok.Getter;

/**
 * 数据源枚举类
 *
 * @author: <PERSON><PERSON><PERSON>
 * @date：2024-03-27
 */
@Getter
public enum DataSourceEnums {

    LENZBI("lenzbi", "lenzbi"),
    LENZ_BI_POLAR("lenzbi-polar", "lenzbi-polar"),
    TASK("task", "task"),
    BI_TASK("bi-task", "bi-task"),
    BI_ENTERPRISE("bi-enterprise", "bi-enterprise");

    private final String code;
    private final String value;

    DataSourceEnums(String code, String value) {
        this.code = code;
        this.value = value;
    }
}
