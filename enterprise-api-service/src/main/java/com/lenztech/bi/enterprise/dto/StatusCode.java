package com.lenztech.bi.enterprise.dto;

/**
 * @Description: 响应状态
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 16/8/18 AM9:43
 */
public enum StatusCode {

	/** 成功 */
	SUCCESS("0","成功"),

	/** 系统异常 */
	SYS_ERROR("-10000","系统异常"),

	/** 处理失败 */
	FAILED("-10001","处理失败"),

	/** 参数异常 */
	PARAM_ERROR("-11001","参数异常"),

	/** 无权限访问 */
	AUTHORITY("-12001","无权限访问"),

	/** 访问次数超限 */
	REQUEST_MORE_COUNT("-12002","访问次数超限");

	private String code;
	private String desc;
 
	public String getCode() {
		return code;
	}
 
	public void setCode(String code) {
		this.code = code;
	}
 
	public String getDesc() {
		return desc;
	}
 
	public void setDesc(String desc) {
		this.desc = desc;
	}
	
	private StatusCode(String code, String desc){
		this.code = code;
		this.desc = desc; 
	}

}
