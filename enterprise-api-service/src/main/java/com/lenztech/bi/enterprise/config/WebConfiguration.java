package com.lenztech.bi.enterprise.config;
import com.lenztech.bi.enterprise.interceptor.IPInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @description:mvc配置
 * <AUTHOR>
 */
@Configuration("webConfiguration")
@Primary
public class WebConfiguration implements WebMvcConfigurer {

    /**
     * @description:声明IP拦截器bean
     * @author:ailikes
     * @return
     */
    @Bean
    public HandlerInterceptor ipInterceptor(){
        return new IPInterceptor();
    }

    /**
     * @description:添加拦截器
     * @author: ailikes
     * @param registry
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry){
        registry
                .addInterceptor(ipInterceptor())
                .addPathPatterns("/api/**")
                .addPathPatterns("/BISystem/**")
                .addPathPatterns("/biResult/**")
                .addPathPatterns("/BIService/**");
    }

}