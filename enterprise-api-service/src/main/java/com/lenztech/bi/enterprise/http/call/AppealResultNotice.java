package com.lenztech.bi.enterprise.http.call;

import com.lenztech.bi.enterprise.http.request.AppealQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
@ApiModel("申诉结果")
public class AppealResultNotice extends AppealQueryRequest {

    @ApiModelProperty("审核状态：0审核中；1已审核")
    private Integer checkState;

    @ApiModelProperty("申诉SKU结果信息")
    private List<AppealSkuResultInfo> skus;

    public List<AppealSkuResultInfo> getSkus() {
        return skus;
    }

    public void setSkus(List<AppealSkuResultInfo> skus) {
        this.skus = skus;
    }

    public Integer getCheckState() {
        return checkState;
    }

    public void setCheckState(Integer checkState) {
        this.checkState = checkState;
    }
}
