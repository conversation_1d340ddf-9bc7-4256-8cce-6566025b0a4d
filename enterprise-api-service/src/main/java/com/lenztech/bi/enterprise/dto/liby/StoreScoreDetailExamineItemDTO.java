package com.lenztech.bi.enterprise.dto.liby;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 考核项
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/14 11:17
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class StoreScoreDetailExamineItemDTO {

    private Integer diDuiScore;

    private Integer lianDaiScore;

    private Integer wuLiaoScore;

    private Integer quGeScore;

    private Integer guaTiaoScore;

}
