//package com.lenztech.bi.enterprise.mapper.task;
//
//import com.baomidou.dynamic.datasource.annotation.DS;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.lenztech.bi.enterprise.dto.UserUseAppDayDTO;
//import com.lenztech.bi.enterprise.entity.TOperateLog;
//import org.apache.ibatis.annotations.Param;
//
//import java.util.List;
//
///**
// * <p>
// * （个人用户）用户存储登陆相关的用户信息 Mapper 接口
// * </p>
// *
// * <AUTHOR>
// * @since 2019-08-28
// */
//@DS("task")
//public interface OperateLogMapper extends BaseMapper<TOperateLog> {
//
//    List<UserUseAppDayDTO> getUserUseAppDays(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("companyId") String companyId);
//}
