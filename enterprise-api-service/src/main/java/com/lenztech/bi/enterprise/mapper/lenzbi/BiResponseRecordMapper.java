package com.lenztech.bi.enterprise.mapper.lenzbi;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.entity.BiResponseRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("lenzbi")
public interface BiResponseRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(BiResponseRecord record);

    int insertSelective(BiResponseRecord record);

    BiResponseRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(BiResponseRecord record);

    int updateByPrimaryKey(BiResponseRecord record);

    List<BiResponseRecord> getRecordList(@Param("responseId") String responseId);
}