package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 千镇门店含有的sku分销信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("baishi_ai_store_distribution")
public class BaishiAiStoreDistribution implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    private Integer id;

    /**
     * rid
     */
    private String responseId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店编码
     */
    private String storeNum;

    /**
     * 门店地址
     */
    private String storeAddress;

    /**
     * 门店类型
     */
    private String storeType;

    /**
     * 拜访门店时间
     */
    private Date visitTime;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 必分销：1.是 0.否
     */
    private Integer mdist;

    /**
     * 是否分销：1.是 0.否
     */
    private Integer hasDist;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 面位
     */
    private Integer facing;


    /**
     * 条形码
     */
    private String barcode;
}
