## SpecStory

## Modify sendRequestAndUpdateLog Method for Success Check (2025/3/25 11:39:52)

_**User**_

sendRequestAndUpdateLog方法帮我修改下，接口返回成功时才算1，其它状态码不算。返回报文示例如下: {"code":"0","data":"","msg":"操作成功"}，{"code":"100500","data":"","msg":"工单：3488202600855240933客户编码30000825990为无效编码"}

---

_**Assistant**_

<think>用户要求我修改`sendRequestAndUpdateLog`方法，使得只有当API返回特定的成功响应（即报文中的code为"0"）时，才将status设置为1（表示成功），其他情况都设为0（表示失败）。

现在的实现是只判断HTTP状态码，如果是200（HttpStatus.SC_OK）就认为是成功，但用户的要求是即使HTTP状态码是200，也要根据返回报文中的"code"字段来判断是否真正成功。

示例成功报文：{"code":"0","data":"","msg":"操作成功"}
示例失败报文：{"code":"100500","data":"","msg":"工单：3488202600855240933客户编码30000825990为无效编码"}

我需要修改代码来解析JSON响应并检查"code"字段是否为"0"。</think># 修改 sendRequestAndUpdateLog 方法

我将修改 `sendRequestAndUpdateLog` 方法，使其根据返回报文中的 `code` 字段来判断是否成功，只有 `code` 为 "0" 时才算成功。

---

_****_

```diff
              // 设置日志属性
              pushLog.setResponseBody(response);
-             pushLog.setStatus(statusCode == HttpStatus.SC_OK ? 1 : 0);
              pushLog.setStatusCode(statusCode);
+             
+             // 判断是否成功：HTTP状态码为200并且返回报文中code为"0"才算成功
+             boolean isSuccess = false;
+             if (statusCode == HttpStatus.SC_OK && StringUtils.isNotBlank(response)) {
+                 try {
+                     // 解析JSON响应
+                     Gson gson = new Gson();
+                     Map<String, Object> responseMap = gson.fromJson(response, Map.class);
+                     // 检查code字段是否为"0"
+                     if (responseMap != null && "0".equals(responseMap.get("code"))) {
+                         isSuccess = true;
+                     }
+                 } catch (Exception e) {
+                     log.error("解析响应JSON失败", e);
+                 }
+             }
+             
+             pushLog.setStatus(isSuccess ? 1 : 0);
  
          } catch (Exception e) {
          }
  
-         log.info("数据发送结果 - ID: {}, Response: {}", stepId, response);
+         log.info("数据发送结果 - ID: {}, Response: {}, 是否成功: {}", stepId, response, pushLog.getStatus() == 1 ? "是" : "否");
      }
  
```

---

_****_

## 修改说明

我已经成功修改了 `sendRequestAndUpdateLog` 方法，现在它会根据接口返回的 JSON 报文中的 `code` 字段来判断请求是否成功：

1. 修改了判断成功的逻辑：
   - 只有当 HTTP 状态码为 200 **并且** JSON 响应中的 `code` 字段值为 "0" 时，才认为请求成功（将 `status` 设为 1）
   - 其他所有情况都视为失败（将 `status` 设为 0）

2. 增加了 JSON 解析的异常处理，确保即使解析失败也不会影响程序运行

3. 增强了日志输出，现在会显示请求是否成功的信息，更易于调试和监控

这样修改后，系统将正确识别如下情况：
- 成功：`{"code":"0","data":"","msg":"操作成功"}`
- 失败：`{"code":"100500","data":"","msg":"工单：3488202600855240933客户编码30000825990为无效编码"}`

## 下一步建议

为了让系统更加健壮，我还建议考虑以下增强：

1. 保存完整的错误消息（`msg` 字段）到日志中，便于排查问题
2. 考虑增加重试机制，在特定错误码出现时自动重试
3. 设置合理的监控和告警，针对大量失败的情况

需要我实现这些建议的任何一个吗？

---



---

