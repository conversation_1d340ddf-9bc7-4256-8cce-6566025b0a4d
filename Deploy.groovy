#!/usr/bin/env groovy
node {

    def branchOrTagName = BRANCH_NAME
    def publishContent = ''
    def version = 'master.latest'
    def configVersion = 'master'
    def deployEnv = ''
    def action = ''
    def ssh = ''
    def user = ''
    def targetHosts = ''
    def targetInternalHosts = ''
    def projectDir = '/data/projects/bi-service'
    def buildNotifyList = '<EMAIL>,<EMAIL>,<EMAIL>'
    def publishNotifyList = '<EMAIL>,<EMAIL>,<EMAIL>'
    def inHost = ''

    // 测试服务器ip列表
    def testHosts = [
            "**************"
    ]
    def testInternalHosts = [
            "**************"
    ]

    def uatHosts = [
            '*************'
    ]
    def uatInternalHosts = [
            '***********'
    ]

    //正式服务器ip列表
    def alphaHosts = [
            '*************',
            '************'
    ]

    def alphaInternalHosts = [
            '*************',
            '*************'
    ]
    def alphaIds = [
            'i-hp3h7s9g7t89z2eg2rvx',
            'i-hp36sa29zlr3sol1q99r'
    ]

    //正式服务器ip列表
    def deltaHosts = [
            '*************',
            '**************',
            '*************',
            '*************',
            '************'
    ]

    def deltaInternalHosts = [
            '*********',
            '**********',
            '*********',
            '**********',
            '**********'
    ]
    def deltaIds = [
            'ins-gh9bboqt',
            'ins-kptzx4dn',
            'ins-97qfj3j7',
            'ins-qn04d1kf',
            'ins-7dqp5p6r'
    ]


    // 腾讯云ip列表
    def gammaHosts = [
            '************',
            '************',
            '**************'
    ]
    def gammaInternalHosts = [
            '*********',
            '*********',
            '********'
    ]

    def gammaIds = [
            'ins-8zxqpbrf',
            'ins-rqmv526f',
            'ins-nebv3e3v'
    ]

    try {

        stage('Prepare Env') {
            echo '请输入环境参数......'
            checkout scm
            //默认镜像版本确认
            version = branchOrTagName + ".latest"
            //默认配置文件分支/TAG
            configVersion = branchOrTagName
            def vars = input message: '请输入部署的环境参数', parameters: [
                    text(defaultValue: '', description: '请输入这次部署更新的内容', name: 'publishContent'),
                    text(defaultValue: "${version}", description: '请输入这次部署使用的镜像版本', name: 'version'),
                    text(defaultValue: "${configVersion}", description: '请输入这次部署使用的配置文件分支/TAG名称', name: 'configVersion'),
                    choice(choices: ['test', 'uat', 'alpha','gamma','delta'], description: '请选择部署环境', name: 'deployEnv'),
                    choice(choices: ['deploy', 'stop'], description: '请选择本次操作', name: 'action')
            ]

            publishContent = vars['publishContent']
            version = vars['version']
            configVersion = vars['configVersion']
            deployEnv = vars['deployEnv']
            action = vars['action']

            if (deployEnv == 'uat') {
                user = 'ppz'
                targetHosts = uatHosts
                targetInternalHosts = uatInternalHosts
            } else if (deployEnv == 'test') {
                user = 'ppz'
                targetHosts = testHosts
                targetInternalHosts = testInternalHosts
            } else if (deployEnv == 'alpha') {
                user = 'ppz'
                targetHosts = alphaHosts
                targetInternalHosts = alphaInternalHosts
                targetId = alphaIds
            } else if (deployEnv == 'gamma') {
                user = 'ppz'
                targetHosts = gammaHosts
                targetInternalHosts = gammaInternalHosts
                targetId = gammaIds
            } else if (deployEnv == 'delta') {
                user = 'ppz'
                targetHosts = deltaHosts
                targetInternalHosts = deltaInternalHosts
                targetId = deltaIds
            } else {
                throw new Exception('发布环境不存在')
            }

            print('本次发布镜像版本:' + version)
            print('本次发布配置分支/TAG:' + configVersion)
            print('本次发布环境:' + deployEnv)
            print('本次发布服务器列表:' + targetHosts)
            print('本次发布动作:' + action + '服务')
            print('本次使用分支:' + branchOrTagName)
        }

        stage('Deploy') {
            echo '发布......'
            if (currentBuild.result == null || currentBuild.result == 'SUCCESS') {
                for (i = 0; i < targetHosts.size(); i++) {
                    sshagent(['deploy-key']) {
                        String targetHost = targetHosts[i]
                        print('公网IP:' + targetHost)
                        String targetInternalHost = targetInternalHosts[i]
                        print('内网IP:' + targetInternalHost)
                        ssh = "ssh -p 7721 -o StrictHostKeyChecking=no -l ${user} ${targetHost}"
                        sh "${ssh} 'pwd;mkdir -p ${projectDir}'"
                        sh "scp -P 7721 ./release/${deployEnv}/* ${user}@${targetHost}:${projectDir}"
                        sh "scp -P 7721 ./release/${deployEnv}/.env ${user}@${targetHost}:${projectDir}"
                        sh "scp -P 7721 ./release/scripts/dockerLog.sh ${user}@${targetHost}:${projectDir}"
                        sh "scp -P 7721 /data/project/nacos-config/${deployEnv}/.nacos ${user}@${targetHost}:${projectDir}"
                        print('映射IP:' + inHost)
                        print('源IP:' + targetHost)
                        if (deployEnv == 'test' |deployEnv == 'uat' ) {
                                sh "${ssh} 'cd ${projectDir};sed -i \"s/VERSION=master.latest/VERSION=${version}/g\" .env;sed -i \"s/CONFIG_VERSION=master/CONFIG_VERSION=${configVersion}/g\" .env;sed -i \"s/host_ip/${targetHost}/g\" .env;sed -i \"s/internal_host/${targetInternalHost}/g\" .nacos;sed -i \"s/internal_ip/${targetInternalHost}/g\" .nacos;bash ${action}.sh'"
                            } else {
                                String targetId = targetId[i]
                                sh "python3 ./release/${deployEnv}/set_slb.py ${targetId} 0"
                                sh "${ssh} 'cd ${projectDir};sed -i \"s/VERSION=master.latest/VERSION=${version}/g\" .env;sed -i \"s/CONFIG_VERSION=master/CONFIG_VERSION=${configVersion}/g\" .env;sed -i \"s/host_ip/${targetHost}/g\" .env;sed -i \"s/internal_host/${targetInternalHost}/g\" .nacos;sed -i \"s/internal_ip/${targetInternalHost}/g\" .nacos;bash ${action}.sh'"
                                sh "${ssh} 'cd ${projectDir};/bin/bash check_http.sh ${targetInternalHost}'"
                                sh "python3 ./release/${deployEnv}/set_slb.py ${targetId} 10"
                            }
                        //sh "${ssh} 'cd ${projectDir};sed -i \"s/VERSION=master.latest/VERSION=${branchOrTagName}.latest/g\" .env;bash ${action}.sh'"
                    }
                }
            }
            echo "BI接口服务[bi-service] ${deployEnv} 环境 ${action} 完成"
        }

        stage('Send Mail') {
            echo "CI/CD构建完成,发送邮件给 ${publishNotifyList}"
            msgBody = "BI接口服务[bi-service] ${deployEnv} 环境 ${action} 完成" +
                    "\n 分支名称：${branchOrTagName}" +
                    "\n 访问地址: https://mobile.lenztechretail.com/BIService/"
            msgBody += "\n\n 主要变更如下："
            msgBody += getChangeLogs()

            mail to: publishNotifyList,
                    from: '<EMAIL>',
                    body: msgBody,
                    subject: "【成功】BI接口${deployEnv}环境发布"

        }

        stage('Clean WS') {
            //cleanWs cleanWhenFailure: false, cleanWhenUnstable: false, deleteDirs: true, notFailBuild: true
        }

    } catch (e) {
        print('CI/CD构建异常,发送错误邮件给' + buildNotifyList)
        mail to: buildNotifyList,
                from: "<EMAIL>",
                subject: "【失败】BI接口服务[bi-service] CI构建通知",
                body: "lenz_mobile 构建失败，请前往${BUILD_URL} 查看" +
                        "\n 分支：${branchOrTagName}" +
                        "\n 失败原因：${e.toString()}" +
                        "\n 主要变更如下:" + getChangeLogs()
        throw e
    }
}

@NonCPS
def getChangeLogs() {
    String logs = ""
    def changeLogSets = currentBuild.changeSets
    for (int i = 0; i < changeLogSets.size(); i++) {
        def entries = changeLogSets[i].items
        for (int j = 0; j < entries.length; j++) {
            def entry = entries[j]
            logs += "\n ${entry.author}: ${entry.msg} [time:${new Date(entry.timestamp)},commitId:${entry.commitId}]"
            def files = new ArrayList(entry.affectedFiles)
            for (int k = 0; k < files.size(); k++) {
                def file = files[k]
                logs += "\n\t${file.editType.name}=> ${file.path}"
            }
        }
    }
    return logs
}
